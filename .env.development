# CipherScope Development Environment
# Migrated from existing .env file with development-specific configurations

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Environment
NODE_ENV=development
VITE_ENVIRONMENT=development
VITE_APP_NAME=CipherScope
VITE_APP_VERSION=1.0.0

# Application URLs (Development)
VITE_APP_URL=http://localhost:5173
VITE_API_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000/ws

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Database (DuckDB for development)
DATABASE_URL=duckdb:///./data/crypto_backend.db
VITE_DATABASE_TYPE=duckdb

# Redis Configuration (optional for local development)
REDIS_URL=redis://localhost:6379/0

# =============================================================================
# BLOCKCHAIN & WEB3 CONFIGURATION
# =============================================================================

# Infura API Key (from your existing config)
INFURA_API_KEY=********************************
VITE_INFURA_API_KEY=********************************

# Web3 Provider (Alchemy - from your existing config)
WEB3_PROVIDER_URL=https://eth-mainnet.g.alchemy.com/v2/BwYx2Nu9Fo8TBdqPmMw18bIz_MWdsQP-
VITE_WEB3_PROVIDER_URL=https://eth-mainnet.g.alchemy.com/v2/BwYx2Nu9Fo8TBdqPmMw18bIz_MWdsQP-

# Blockchain RPC URLs (Free tier endpoints)
ETHEREUM_RPC_URL=https://cloudflare-eth.com
POLYGON_RPC_URL=https://polygon-rpc.com
BSC_RPC_URL=https://bsc-dataseed.binance.org/

VITE_ETHEREUM_RPC_URL=https://cloudflare-eth.com
VITE_POLYGON_RPC_URL=https://polygon-rpc.com
VITE_BSC_RPC_URL=https://bsc-dataseed.binance.org/

# =============================================================================
# API KEYS (From your existing configuration)
# =============================================================================

# CoinGecko API (from your existing config)
COINGECKO_API_KEY=CG-wJx8SHeFD1KjG3F73LB14967
VITE_COINGECKO_API_KEY=CG-wJx8SHeFD1KjG3F73LB14967

# CoinAPI (Crypto data)
COIN_API_KEY=1dc67551-276c-4098-a1f2-2b58ee57ce69
VITE_COIN_API_KEY=1dc67551-276c-4098-a1f2-2b58ee57ce69

# Etherscan API (Ethereum blockchain data)
ETHERSCAN_API_KEY=**********************************
VITE_ETHERSCAN_API_KEY=**********************************

# Dune Analytics (Blockchain analytics)
DUNE_API_KEY=8L1zlFUtMWg4FCMsZuFkKIkXbRpPYefv
VITE_DUNE_API_KEY=8L1zlFUtMWg4FCMsZuFkKIkXbRpPYefv

# Birdeye API (Solana-focused)
API_BIRDEYE_API_KEY=34eb196d0dad4234ace628c3e8767360
VITE_BIRDEYE_API_KEY=34eb196d0dad4234ace628c3e8767360

# =============================================================================
# WEBSOCKET CONFIGURATION (Infura WebSockets)
# =============================================================================

# Main WebSocket URLs
INFURA_MAINNET_WS=wss://mainnet.infura.io/ws/v3/********************************
INFURA_SEPOLIA_WS=wss://sepolia.infura.io/ws/v3/********************************

VITE_INFURA_MAINNET_WS=wss://mainnet.infura.io/ws/v3/********************************
VITE_INFURA_SEPOLIA_WS=wss://sepolia.infura.io/ws/v3/********************************

# Multi-chain WebSocket URLs
INFURA_POLYGON_MAINNET_WS=wss://polygon-mainnet.infura.io/ws/v3/********************************
INFURA_BASE_MAINNET_WS=wss://base-mainnet.infura.io/ws/v3/********************************
INFURA_ARBITRUM_MAINNET_WS=wss://arbitrum-mainnet.infura.io/ws/v3/********************************
INFURA_OPTIMISM_MAINNET_WS=wss://optimism-mainnet.infura.io/ws/v3/********************************

VITE_INFURA_POLYGON_MAINNET_WS=wss://polygon-mainnet.infura.io/ws/v3/********************************
VITE_INFURA_BASE_MAINNET_WS=wss://base-mainnet.infura.io/ws/v3/********************************
VITE_INFURA_ARBITRUM_MAINNET_WS=wss://arbitrum-mainnet.infura.io/ws/v3/********************************
VITE_INFURA_OPTIMISM_MAINNET_WS=wss://optimism-mainnet.infura.io/ws/v3/********************************

# =============================================================================
# SOCIAL MEDIA & SENTIMENT ANALYSIS
# =============================================================================

# Reddit API (For social sentiment)
REDDIT_CLIENT_ID=Ar2tSflbt93IycLB0fEyLg
REDDIT_CLIENT_SECRET=ec67445f-0ac1-4f3a-8de3-c54ec3233341

# Twitter/X API
TWITTER_BEARER_TOKEN=your_twitter_bearer_token_here

# =============================================================================
# AI & ML SERVICES
# =============================================================================

# OpenRouter API (AI services)
OPENROUTER_API_KEY=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

# =============================================================================
# WEB SCRAPING & DATA COLLECTION
# =============================================================================

# ScrapingBee API (Web scraping)
SCRAPINGBEE_API_KEY=********************************************************************************

# HyperBrowser API
HYPERBROWSER_API_KEY=hb_adc1508444618104ba9ed438f19b

# =============================================================================
# SECURITY & AUTHENTICATION
# =============================================================================

# JWT Configuration (Development)
JWT_SECRET_KEY=dev_jwt_secret_key_12345_extended_for_security
ENCRYPTION_KEY=dev_encryption_key_12345_extended_for_security

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Debug and Logging
DEBUG=true
LOG_LEVEL=DEBUG
VITE_DEBUG=true
VITE_LOG_LEVEL=DEBUG

# Development Features
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_HOT_RELOAD=true
VITE_ENABLE_MOCK_DATA=true
VITE_ENABLE_DEBUG_PANEL=true

# =============================================================================
# SYSTEM CONFIGURATION
# =============================================================================

# Performance Settings
MAX_CONCURRENT_ANALYSES=10
CACHE_TTL_SECONDS=3600
API_RATE_LIMIT_PER_MINUTE=60

# =============================================================================
# MONITORING & TELEMETRY
# =============================================================================

# OpenTelemetry Configuration
OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4317
OTEL_SERVICE_NAME=cipherscope-frontend-dev
OTEL_RESOURCE_ATTRIBUTES=service.name=cipherscope-frontend-dev,service.version=1.0.0,environment=development

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/disable features for development
VITE_ENABLE_REAL_TIME=true
VITE_ENABLE_ADVANCED_CHARTS=true
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_TRADING=true
VITE_ENABLE_SECURITY_MONITORING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true
