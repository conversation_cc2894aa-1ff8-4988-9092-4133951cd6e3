# CipherScope Frontend Environment Variables
# Copy this file to .env.local and fill in your values

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Application URLs
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000/ws

# Environment
NODE_ENV=development
NEXT_PUBLIC_ENVIRONMENT=development

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================

# NextAuth.js configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-here

# JWT Secret
JWT_SECRET=your-jwt-secret-here

# API Keys
NEXT_PUBLIC_API_KEY=your-api-key-here

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/disable features
NEXT_PUBLIC_ENABLE_REAL_TIME=true
NEXT_PUBLIC_ENABLE_ADVANCED_CHARTS=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_DEBUG=false

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================

# Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=
NEXT_PUBLIC_MIXPANEL_TOKEN=
ANALYTICS_KEY=your-analytics-key-here

# Error Tracking
NEXT_PUBLIC_SENTRY_DSN=
SENTRY_AUTH_TOKEN=

# Monitoring
NEXT_PUBLIC_DATADOG_CLIENT_TOKEN=
NEXT_PUBLIC_DATADOG_APPLICATION_ID=

# =============================================================================
# TRADING & MARKET DATA
# =============================================================================

# Market data providers
NEXT_PUBLIC_COINGECKO_API_KEY=
NEXT_PUBLIC_COINMARKETCAP_API_KEY=
NEXT_PUBLIC_BINANCE_API_KEY=
NEXT_PUBLIC_POLYGON_API_KEY=

# WebSocket endpoints
NEXT_PUBLIC_BINANCE_WS_URL=wss://stream.binance.com:9443/ws
NEXT_PUBLIC_COINBASE_WS_URL=wss://ws-feed.pro.coinbase.com

# =============================================================================
# BLOCKCHAIN CONFIGURATION
# =============================================================================

# Ethereum
NEXT_PUBLIC_ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your-project-id
NEXT_PUBLIC_ETHEREUM_CHAIN_ID=1

# Polygon
NEXT_PUBLIC_POLYGON_RPC_URL=https://polygon-mainnet.infura.io/v3/your-project-id
NEXT_PUBLIC_POLYGON_CHAIN_ID=137

# BSC
NEXT_PUBLIC_BSC_RPC_URL=https://bsc-dataseed.binance.org/
NEXT_PUBLIC_BSC_CHAIN_ID=56

# =============================================================================
# STORAGE & CDN
# =============================================================================

# AWS S3
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-west-2
AWS_S3_BUCKET=cipherscope-assets

# CloudFront
NEXT_PUBLIC_CDN_URL=https://cdn.cipherscope.com

# =============================================================================
# DATABASE & CACHE
# =============================================================================

# Database (for server-side operations)
DATABASE_URL=postgresql://username:password@localhost:5432/cipherscope

# Redis (for caching)
REDIS_URL=redis://localhost:6379/0

# =============================================================================
# NOTIFICATIONS
# =============================================================================

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Push notifications
NEXT_PUBLIC_VAPID_PUBLIC_KEY=
VAPID_PRIVATE_KEY=

# Slack
SLACK_WEBHOOK_URL=
SLACK_BOT_TOKEN=

# Discord
DISCORD_WEBHOOK_URL=

# =============================================================================
# DEVELOPMENT & DEBUGGING
# =============================================================================

# Debug flags
DEBUG=false
VERBOSE_LOGGING=false

# Development tools
ANALYZE=false
BUNDLE_ANALYZE=false

# Hot reload
FAST_REFRESH=true

# =============================================================================
# PRODUCTION CONFIGURATION
# =============================================================================

# SSL/TLS
SSL_CERT_PATH=/etc/ssl/certs/cert.pem
SSL_KEY_PATH=/etc/ssl/private/key.pem

# Rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS
CORS_ORIGINS=http://localhost:3000,https://cipherscope.com

# =============================================================================
# MONITORING & LOGGING
# =============================================================================

# Log level
LOG_LEVEL=info

# Metrics
METRICS_ENABLED=true
METRICS_PORT=9090

# Health checks
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30000

# =============================================================================
# PERFORMANCE
# =============================================================================

# Caching
CACHE_TTL=3600
STATIC_CACHE_TTL=86400

# Compression
COMPRESSION_ENABLED=true
COMPRESSION_LEVEL=6

# =============================================================================
# SECURITY
# =============================================================================

# Content Security Policy
CSP_ENABLED=true
CSP_REPORT_URI=

# HSTS
HSTS_MAX_AGE=31536000
HSTS_INCLUDE_SUBDOMAINS=true

# Session
SESSION_SECRET=your-session-secret-here
SESSION_MAX_AGE=86400000

# =============================================================================
# THIRD-PARTY INTEGRATIONS
# =============================================================================

# Social media
TWITTER_API_KEY=
TWITTER_API_SECRET=
DISCORD_CLIENT_ID=
TELEGRAM_BOT_TOKEN=

# Payment processing
STRIPE_PUBLIC_KEY=
STRIPE_SECRET_KEY=

# AI/ML Services
OPENAI_API_KEY=
ANTHROPIC_API_KEY=

# =============================================================================
# DEPLOYMENT
# =============================================================================

# Container configuration
CONTAINER_PORT=3000
CONTAINER_HOST=0.0.0.0

# Kubernetes
K8S_NAMESPACE=cipherscope
K8S_SERVICE_ACCOUNT=cipherscope-frontend

# Docker
DOCKER_REGISTRY=ghcr.io
DOCKER_IMAGE_TAG=latest

# =============================================================================
# BACKUP & RECOVERY
# =============================================================================

# Backup configuration
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
