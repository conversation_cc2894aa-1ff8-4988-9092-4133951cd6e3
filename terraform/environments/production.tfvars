# CipherScope Production Environment Configuration

# Basic Configuration
aws_region = "us-west-2"
environment = "production"
project_name = "cipherscope"

# VPC Configuration
vpc_cidr = "10.0.0.0/16"
private_subnet_cidrs = ["********/24", "********/24", "********/24"]
public_subnet_cidrs = ["**********/24", "**********/24", "**********/24"]

# EKS Configuration
kubernetes_version = "1.28"
node_instance_types = ["t3.large", "t3.xlarge"]
node_group_min_size = 3
node_group_max_size = 20
node_group_desired_size = 6

# Database Configuration
db_instance_class = "db.r6g.large"
db_allocated_storage = 100
db_max_allocated_storage = 1000
db_name = "cipherscope"
db_username = "cipherscope"
# db_password should be set via environment variable: TF_VAR_db_password

# Redis Configuration
redis_node_type = "cache.r6g.large"
redis_num_cache_nodes = 3

# Domain Configuration
domain_name = "cipherscope.com"
# certificate_arn should be set after creating ACM certificate

# Security Configuration
allowed_cidr_blocks = ["0.0.0.0/0"]  # Restrict this in production
enable_waf = true

# Monitoring Configuration
enable_monitoring = true
log_retention_days = 90

# Backup Configuration
backup_retention_period = 30
enable_point_in_time_recovery = true

# Scaling Configuration
enable_cluster_autoscaler = true
enable_horizontal_pod_autoscaler = true

# Performance Configuration
enable_ssl_termination = true
enable_cross_zone_load_balancing = true
enable_deletion_protection = true

# Cost Optimization
enable_spot_instances = false  # Disable for production stability

# Additional Tags
additional_tags = {
  Owner = "DevOps Team"
  CostCenter = "Engineering"
  Backup = "Required"
  Compliance = "SOC2"
}
