# CipherScope Staging Environment Configuration

# Basic Configuration
aws_region = "us-west-2"
environment = "staging"
project_name = "cipherscope"

# VPC Configuration
vpc_cidr = "********/16"
private_subnet_cidrs = ["********/24", "********/24", "********/24"]
public_subnet_cidrs = ["**********/24", "**********/24", "**********/24"]

# EKS Configuration
kubernetes_version = "1.28"
node_instance_types = ["t3.medium"]
node_group_min_size = 2
node_group_max_size = 6
node_group_desired_size = 3

# Database Configuration
db_instance_class = "db.t3.medium"
db_allocated_storage = 50
db_max_allocated_storage = 200
db_name = "cipherscope_staging"
db_username = "cipherscope"
# db_password should be set via environment variable: TF_VAR_db_password

# Redis Configuration
redis_node_type = "cache.t3.medium"
redis_num_cache_nodes = 2

# Domain Configuration
domain_name = "staging.cipherscope.com"
# certificate_arn should be set after creating ACM certificate

# Security Configuration
allowed_cidr_blocks = ["0.0.0.0/0"]
enable_waf = true

# Monitoring Configuration
enable_monitoring = true
log_retention_days = 30

# Backup Configuration
backup_retention_period = 7
enable_point_in_time_recovery = true

# Scaling Configuration
enable_cluster_autoscaler = true
enable_horizontal_pod_autoscaler = true

# Performance Configuration
enable_ssl_termination = true
enable_cross_zone_load_balancing = true
enable_deletion_protection = false

# Cost Optimization
enable_spot_instances = true  # Enable for cost savings in staging

# Additional Tags
additional_tags = {
  Owner = "DevOps Team"
  CostCenter = "Engineering"
  Environment = "Staging"
  AutoShutdown = "Enabled"
}
