import { Token, PriceData, Alert } from '@/types';
import { mockTokens, mockAlerts, generatePriceData, generateMoreTokens } from '@/data/mockData';

const API_DELAY = 300; // Simulate network delay

export const api = {
  // Token Discovery
  getTokens: async (page: number = 1, limit: number = 50): Promise<{ tokens: Token[], hasMore: boolean }> => {
    await new Promise(resolve => setTimeout(resolve, API_DELAY));
    
    const allTokens = [...mockTokens, ...generateMoreTokens(200)];
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const tokens = allTokens.slice(startIndex, endIndex);
    const hasMore = endIndex < allTokens.length;
    
    return { tokens, hasMore };
  },

  // Token Details
  getToken: async (id: string): Promise<Token | null> => {
    await new Promise(resolve => setTimeout(resolve, API_DELAY));
    return mockTokens.find(token => token.id === id) || null;
  },

  // Price Data
  getPriceData: async (tokenId: string, timeframe: string = '24h'): Promise<PriceData[]> => {
    await new Promise(resolve => setTimeout(resolve, API_DELAY));
    const token = mockTokens.find(t => t.id === tokenId);
    if (!token) return [];
    
    const days = timeframe === '24h' ? 1 : timeframe === '7d' ? 7 : 30;
    return generatePriceData(token.price, days);
  },

  // Alerts
  getAlerts: async (): Promise<Alert[]> => {
    await new Promise(resolve => setTimeout(resolve, API_DELAY));
    return mockAlerts;
  },

  markAlertAsRead: async (alertId: string): Promise<void> => {
    await new Promise(resolve => setTimeout(resolve, API_DELAY));
    const alert = mockAlerts.find(a => a.id === alertId);
    if (alert) alert.isRead = true;
  },

  // Filters
  getTokensByChain: async (chain: string): Promise<Token[]> => {
    await new Promise(resolve => setTimeout(resolve, API_DELAY));
    return mockTokens.filter(token => token.chain === chain);
  },

  getTokensByCategory: async (category: string): Promise<Token[]> => {
    await new Promise(resolve => setTimeout(resolve, API_DELAY));
    return mockTokens.filter(token => token.category === category);
  }
};

// WebSocket simulation for real-time data
export class MockWebSocket {
  private callbacks: Array<(data: any) => void> = [];
  private interval?: NodeJS.Timeout;

  connect() {
    this.interval = setInterval(() => {
      // Simulate price updates
      const randomToken = mockTokens[Math.floor(Math.random() * mockTokens.length)];
      const priceChange = (Math.random() - 0.5) * 0.02; // ±1% change
      const newPrice = randomToken.price * (1 + priceChange);
      
      this.callbacks.forEach(callback => 
        callback({
          type: 'price_update',
          tokenId: randomToken.id,
          price: newPrice,
          change: priceChange * 100,
          timestamp: Date.now()
        })
      );
    }, 2000);
  }

  onMessage(callback: (data: any) => void) {
    this.callbacks.push(callback);
  }

  disconnect() {
    if (this.interval) {
      clearInterval(this.interval);
    }
    this.callbacks = [];
  }
}