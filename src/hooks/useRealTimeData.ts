'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { useWebSocket } from './useWebSocket';
import type { 
  Token, 
  TokenAnalysis, 
  SystemAlert, 
  SystemMetrics,
  RealTimeUpdate 
} from '@/lib/types';

interface RealTimeDataState {
  tokens: Map<string, Token>;
  tokenPrices: Map<string, RealTimeUpdate>;
  systemMetrics: SystemMetrics | null;
  alerts: SystemAlert[];
  discoveries: Token[];
  lastUpdate: string;
}

interface UseRealTimeDataOptions {
  subscriptions?: string[];
  maxAlerts?: number;
  maxDiscoveries?: number;
  onTokenUpdate?: (token: Token) => void;
  onPriceUpdate?: (update: RealTimeUpdate) => void;
  onAlert?: (alert: SystemAlert) => void;
  onSystemUpdate?: (metrics: SystemMetrics) => void;
}

export function useRealTimeData(options: UseRealTimeDataOptions = {}) {
  const {
    subscriptions = ['tokens', 'prices', 'system', 'alerts', 'discoveries'],
    maxAlerts = 50,
    maxDiscoveries = 20,
    onTokenUpdate,
    onPriceUpdate,
    onAlert,
    onSystemUpdate,
  } = options;

  const [data, setData] = useState<RealTimeDataState>({
    tokens: new Map(),
    tokenPrices: new Map(),
    systemMetrics: null,
    alerts: [],
    discoveries: [],
    lastUpdate: new Date().toISOString(),
  });

  const [isSubscribed, setIsSubscribed] = useState(false);
  const subscriptionsRef = useRef<Set<string>>(new Set());

  // WebSocket connection
  const { sendMessage, lastMessage, isConnected, connectionState } = useWebSocket({
    url: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000/ws',
    onOpen: () => {
      // Subscribe to data streams when connection opens
      subscribeToStreams();
    },
    onMessage: handleWebSocketMessage,
    reconnectAttempts: 5,
    reconnectInterval: 2000,
    heartbeatInterval: 30000,
  });

  const subscribeToStreams = useCallback(() => {
    if (!isConnected) return;

    subscriptions.forEach(subscription => {
      if (!subscriptionsRef.current.has(subscription)) {
        sendMessage({
          type: 'subscribe',
          stream: subscription,
          timestamp: new Date().toISOString(),
        });
        subscriptionsRef.current.add(subscription);
      }
    });

    setIsSubscribed(true);
  }, [isConnected, subscriptions, sendMessage]);

  const unsubscribeFromStreams = useCallback(() => {
    if (!isConnected) return;

    subscriptionsRef.current.forEach(subscription => {
      sendMessage({
        type: 'unsubscribe',
        stream: subscription,
        timestamp: new Date().toISOString(),
      });
    });

    subscriptionsRef.current.clear();
    setIsSubscribed(false);
  }, [isConnected, sendMessage]);

  function handleWebSocketMessage(message: any) {
    if (!message || !message.type) return;

    setData(prevData => {
      const newData = { ...prevData };
      newData.lastUpdate = new Date().toISOString();

      switch (message.type) {
        case 'token_update':
          if (message.data) {
            newData.tokens.set(message.data.address, message.data);
            onTokenUpdate?.(message.data);
          }
          break;

        case 'price_update':
          if (message.data) {
            const update: RealTimeUpdate = {
              tokenAddress: message.data.tokenAddress,
              price: message.data.price,
              change24h: message.data.change24h,
              volume24h: message.data.volume24h,
              timestamp: message.data.timestamp || new Date().toISOString(),
            };
            newData.tokenPrices.set(message.data.tokenAddress, update);
            onPriceUpdate?.(update);
          }
          break;

        case 'system_metrics':
          if (message.data) {
            newData.systemMetrics = message.data;
            onSystemUpdate?.(message.data);
          }
          break;

        case 'security_alert':
        case 'system_alert':
          if (message.data) {
            const alert: SystemAlert = {
              ...message.data,
              timestamp: message.data.timestamp || new Date().toISOString(),
            };
            newData.alerts = [alert, ...newData.alerts.slice(0, maxAlerts - 1)];
            onAlert?.(alert);
          }
          break;

        case 'token_discovery':
          if (message.data) {
            const discovery: Token = {
              ...message.data,
              createdAt: message.data.createdAt || new Date().toISOString(),
            };
            newData.discoveries = [discovery, ...newData.discoveries.slice(0, maxDiscoveries - 1)];
          }
          break;

        case 'bulk_update':
          if (message.data) {
            // Handle bulk updates for efficiency
            if (message.data.tokens) {
              message.data.tokens.forEach((token: Token) => {
                newData.tokens.set(token.address, token);
              });
            }
            if (message.data.prices) {
              message.data.prices.forEach((update: RealTimeUpdate) => {
                newData.tokenPrices.set(update.tokenAddress, update);
              });
            }
          }
          break;

        case 'subscription_confirmed':
          console.log(`Subscribed to ${message.stream}`);
          break;

        case 'subscription_error':
          console.error(`Subscription error for ${message.stream}:`, message.error);
          break;

        case 'pong':
          // Heartbeat response - no action needed
          break;

        default:
          console.warn('Unknown message type:', message.type);
      }

      return newData;
    });
  }

  // Subscribe to specific token updates
  const subscribeToToken = useCallback((tokenAddress: string, chainId?: number) => {
    if (!isConnected) return false;

    return sendMessage({
      type: 'subscribe_token',
      tokenAddress,
      chainId: chainId || 1,
      timestamp: new Date().toISOString(),
    });
  }, [isConnected, sendMessage]);

  // Unsubscribe from specific token updates
  const unsubscribeFromToken = useCallback((tokenAddress: string) => {
    if (!isConnected) return false;

    return sendMessage({
      type: 'unsubscribe_token',
      tokenAddress,
      timestamp: new Date().toISOString(),
    });
  }, [isConnected, sendMessage]);

  // Request historical data
  const requestHistoricalData = useCallback((
    tokenAddress: string,
    timeframe: string = '1d',
    limit: number = 100
  ) => {
    if (!isConnected) return false;

    return sendMessage({
      type: 'request_historical',
      tokenAddress,
      timeframe,
      limit,
      timestamp: new Date().toISOString(),
    });
  }, [isConnected, sendMessage]);

  // Send custom message
  const sendCustomMessage = useCallback((message: any) => {
    if (!isConnected) return false;
    return sendMessage(message);
  }, [isConnected, sendMessage]);

  // Get token data
  const getToken = useCallback((address: string): Token | undefined => {
    return data.tokens.get(address);
  }, [data.tokens]);

  // Get token price
  const getTokenPrice = useCallback((address: string): RealTimeUpdate | undefined => {
    return data.tokenPrices.get(address);
  }, [data.tokenPrices]);

  // Get active alerts
  const getActiveAlerts = useCallback((): SystemAlert[] => {
    return data.alerts.filter(alert => !alert.resolved);
  }, [data.alerts]);

  // Get recent discoveries
  const getRecentDiscoveries = useCallback((limit: number = 10): Token[] => {
    return data.discoveries.slice(0, limit);
  }, [data.discoveries]);

  // Clear old data
  const clearOldData = useCallback(() => {
    setData(prevData => ({
      ...prevData,
      alerts: prevData.alerts.slice(0, maxAlerts),
      discoveries: prevData.discoveries.slice(0, maxDiscoveries),
    }));
  }, [maxAlerts, maxDiscoveries]);

  // Auto-subscribe when connection is established
  useEffect(() => {
    if (isConnected && !isSubscribed) {
      subscribeToStreams();
    }
  }, [isConnected, isSubscribed, subscribeToStreams]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (isConnected) {
        unsubscribeFromStreams();
      }
    };
  }, [isConnected, unsubscribeFromStreams]);

  // Periodic cleanup of old data
  useEffect(() => {
    const interval = setInterval(clearOldData, 5 * 60 * 1000); // Every 5 minutes
    return () => clearInterval(interval);
  }, [clearOldData]);

  return {
    // Connection state
    isConnected,
    isSubscribed,
    connectionState,
    lastUpdate: data.lastUpdate,

    // Data access
    tokens: Array.from(data.tokens.values()),
    tokenPrices: Array.from(data.tokenPrices.values()),
    systemMetrics: data.systemMetrics,
    alerts: data.alerts,
    discoveries: data.discoveries,

    // Helper functions
    getToken,
    getTokenPrice,
    getActiveAlerts,
    getRecentDiscoveries,

    // Subscription management
    subscribeToToken,
    unsubscribeFromToken,
    subscribeToStreams,
    unsubscribeFromStreams,

    // Data requests
    requestHistoricalData,
    sendCustomMessage,

    // Utility
    clearOldData,

    // Raw data maps for performance-critical operations
    tokenMap: data.tokens,
    priceMap: data.tokenPrices,
  };
}
