'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { toast } from 'sonner';

export interface WebSocketConfig {
  url: string;
  reconnectAttempts?: number;
  reconnectInterval?: number;
  heartbeatInterval?: number;
  maxReconnectDelay?: number;
  onOpen?: () => void;
  onClose?: () => void;
  onError?: (error: Event) => void;
  onMessage?: (data: any) => void;
}

export enum ConnectionState {
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
}

export function useWebSocket(config: WebSocketConfig) {
  const {
    url,
    reconnectAttempts = 5,
    reconnectInterval = 1000,
    heartbeatInterval = 30000,
    maxReconnectDelay = 30000,
    onOpen,
    onClose,
    onError,
    onMessage,
  } = config;

  const [connectionState, setConnectionState] = useState<ConnectionState>(
    ConnectionState.DISCONNECTED
  );
  const [lastMessage, setLastMessage] = useState<any>(null);
  const [messageHistory, setMessageHistory] = useState<any[]>([]);

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const mountedRef = useRef(true);

  const clearTimeouts = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    if (heartbeatTimeoutRef.current) {
      clearTimeout(heartbeatTimeoutRef.current);
      heartbeatTimeoutRef.current = null;
    }
  }, []);

  const startHeartbeat = useCallback(() => {
    clearTimeouts();
    heartbeatTimeoutRef.current = setTimeout(() => {
      if (wsRef.current?.readyState === WebSocket.OPEN) {
        wsRef.current.send(JSON.stringify({ type: 'ping' }));
        startHeartbeat();
      }
    }, heartbeatInterval);
  }, [heartbeatInterval, clearTimeouts]);

  const connect = useCallback(() => {
    if (!mountedRef.current) return;

    try {
      setConnectionState(ConnectionState.CONNECTING);
      wsRef.current = new WebSocket(url);

      wsRef.current.onopen = () => {
        if (!mountedRef.current) return;
        
        setConnectionState(ConnectionState.CONNECTED);
        reconnectAttemptsRef.current = 0;
        startHeartbeat();
        onOpen?.();
        
        toast.success('Connected to real-time data feed');
      };

      wsRef.current.onmessage = (event) => {
        if (!mountedRef.current) return;

        try {
          const data = JSON.parse(event.data);
          
          // Handle heartbeat responses
          if (data.type === 'pong') {
            return;
          }

          setLastMessage(data);
          setMessageHistory(prev => [...prev.slice(-99), data]); // Keep last 100 messages
          onMessage?.(data);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      wsRef.current.onclose = () => {
        if (!mountedRef.current) return;

        setConnectionState(ConnectionState.DISCONNECTED);
        clearTimeouts();
        onClose?.();

        // Attempt reconnection if we haven't exceeded max attempts
        if (reconnectAttemptsRef.current < reconnectAttempts) {
          const delay = Math.min(
            reconnectInterval * Math.pow(2, reconnectAttemptsRef.current),
            maxReconnectDelay
          );
          
          setConnectionState(ConnectionState.RECONNECTING);
          reconnectAttemptsRef.current++;
          
          reconnectTimeoutRef.current = setTimeout(() => {
            if (mountedRef.current) {
              connect();
            }
          }, delay);

          toast.warning(`Connection lost. Reconnecting in ${delay / 1000}s...`);
        } else {
          setConnectionState(ConnectionState.ERROR);
          toast.error('Failed to connect to real-time data feed');
        }
      };

      wsRef.current.onerror = (error) => {
        if (!mountedRef.current) return;
        
        setConnectionState(ConnectionState.ERROR);
        onError?.(error);
        console.error('WebSocket error:', error);
      };

    } catch (error) {
      setConnectionState(ConnectionState.ERROR);
      console.error('Failed to create WebSocket connection:', error);
      toast.error('Failed to establish connection');
    }
  }, [
    url,
    reconnectAttempts,
    reconnectInterval,
    maxReconnectDelay,
    startHeartbeat,
    onOpen,
    onClose,
    onError,
    onMessage,
  ]);

  const disconnect = useCallback(() => {
    mountedRef.current = false;
    clearTimeouts();
    
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    
    setConnectionState(ConnectionState.DISCONNECTED);
  }, [clearTimeouts]);

  const sendMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      try {
        wsRef.current.send(JSON.stringify(message));
        return true;
      } catch (error) {
        console.error('Failed to send WebSocket message:', error);
        return false;
      }
    }
    return false;
  }, []);

  const reconnect = useCallback(() => {
    disconnect();
    mountedRef.current = true;
    reconnectAttemptsRef.current = 0;
    setTimeout(connect, 100);
  }, [disconnect, connect]);

  // Initialize connection
  useEffect(() => {
    mountedRef.current = true;
    connect();

    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  return {
    connectionState,
    lastMessage,
    messageHistory,
    sendMessage,
    reconnect,
    disconnect,
    isConnected: connectionState === ConnectionState.CONNECTED,
    isConnecting: connectionState === ConnectionState.CONNECTING,
    isReconnecting: connectionState === ConnectionState.RECONNECTING,
    hasError: connectionState === ConnectionState.ERROR,
  };
}
