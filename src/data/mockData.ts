import { Token, PriceData, Alert, HeatmapData } from '@/types';

// Generate realistic price data
export const generatePriceData = (basePrice: number, days: number = 30): PriceData[] => {
  const data: PriceData[] = [];
  let currentPrice = basePrice;
  const now = Date.now();
  
  for (let i = days; i >= 0; i--) {
    const timestamp = now - (i * 24 * 60 * 60 * 1000);
    const volatility = 0.05 + Math.random() * 0.1;
    const change = (Math.random() - 0.5) * volatility;
    
    const open = currentPrice;
    const close = open * (1 + change);
    const high = Math.max(open, close) * (1 + Math.random() * 0.03);
    const low = Math.min(open, close) * (1 - Math.random() * 0.03);
    const volume = 1000000 + Math.random() * 5000000;
    
    data.push({ timestamp, open, high, low, close, volume });
    currentPrice = close;
  }
  
  return data;
};

export const mockTokens: Token[] = [
  {
    id: 'bitcoin',
    symbol: 'BTC',
    name: 'Bitcoin',
    chain: 'ethereum',
    price: 43250.89,
    change24h: 2.34,
    volume24h: 15847392847,
    marketCap: ************,
    aiScore: 92,
    riskLevel: 'low',
    category: 'bluechip',
    logo: '🟠',
    sentiment: { positive: 0.65, negative: 0.20, neutral: 0.15 },
    metrics: {
      liquidity: 0.95,
      holders: 47829384,
      transactions24h: 284739,
      volatility: 0.023,
      velocity: 0.067
    }
  },
  {
    id: 'ethereum',
    symbol: 'ETH',
    name: 'Ethereum',
    chain: 'ethereum',
    price: 2547.23,
    change24h: 1.89,
    volume24h: 8394729384,
    marketCap: ************,
    aiScore: 89,
    riskLevel: 'low',
    category: 'bluechip',
    logo: '⟠',
    sentiment: { positive: 0.62, negative: 0.23, neutral: 0.15 },
    metrics: {
      liquidity: 0.92,
      holders: 112847293,
      transactions24h: 1847293,
      volatility: 0.034,
      velocity: 0.089
    }
  },
  {
    id: 'pepe',
    symbol: 'PEPE',
    name: 'Pepe',
    chain: 'ethereum',
    price: 0.00000847,
    change24h: 15.67,
    volume24h: 284739384,
    marketCap: 3574839274,
    aiScore: 34,
    riskLevel: 'high',
    category: 'meme',
    logo: '🐸',
    sentiment: { positive: 0.45, negative: 0.35, neutral: 0.20 },
    metrics: {
      liquidity: 0.23,
      holders: 284739,
      transactions24h: 58473,
      volatility: 0.156,
      velocity: 0.234
    }
  },
  {
    id: 'solana',
    symbol: 'SOL',
    name: 'Solana',
    chain: 'solana',
    price: 98.47,
    change24h: -3.21,
    volume24h: 1584739384,
    marketCap: 42847392847,
    aiScore: 78,
    riskLevel: 'medium',
    category: 'infrastructure',
    logo: '◎',
    sentiment: { positive: 0.58, negative: 0.28, neutral: 0.14 },
    metrics: {
      liquidity: 0.78,
      holders: 2847293,
      transactions24h: 847392,
      volatility: 0.089,
      velocity: 0.134
    }
  },
  {
    id: 'dogecoin',
    symbol: 'DOGE',
    name: 'Dogecoin',
    chain: 'ethereum',
    price: 0.074,
    change24h: 8.92,
    volume24h: 947384738,
    marketCap: 10583947293,
    aiScore: 41,
    riskLevel: 'high',
    category: 'meme',
    logo: '🐕',
    sentiment: { positive: 0.52, negative: 0.31, neutral: 0.17 },
    metrics: {
      liquidity: 0.67,
      holders: 5847293,
      transactions24h: 184739,
      volatility: 0.123,
      velocity: 0.189
    }
  }
];

export const mockAlerts: Alert[] = [
  {
    id: '1',
    type: 'price',
    severity: 'warning',
    title: 'BTC Price Alert',
    message: 'Bitcoin has crossed above $43,000 resistance level',
    timestamp: Date.now() - 300000,
    tokenId: 'bitcoin',
    isRead: false,
    diagnostics: {
      triggeredAt: 43000,
      currentPrice: 43250.89,
      timeframe: '5m',
      volume: 'High'
    }
  },
  {
    id: '2',
    type: 'sentiment',
    severity: 'info',
    title: 'PEPE Sentiment Shift',
    message: 'Positive sentiment increased by 12% in the last hour',
    timestamp: Date.now() - 600000,
    tokenId: 'pepe',
    isRead: false
  },
  {
    id: '3',
    type: 'risk',
    severity: 'critical',
    title: 'High Volatility Detected',
    message: 'DOGE showing unusual volatility patterns',
    timestamp: Date.now() - 900000,
    tokenId: 'dogecoin',
    isRead: true
  }
];

export const mockHeatmapData: HeatmapData[] = mockTokens
  .filter(token => token.category === 'meme')
  .map(token => ({
    id: token.id,
    symbol: token.symbol,
    volatility: token.metrics.volatility,
    velocity: token.metrics.velocity,
    marketCap: token.marketCap,
    change24h: token.change24h
  }));

export const generateMoreTokens = (count: number): Token[] => {
  const symbols = ['SHIB', 'FLOKI', 'BONK', 'WIF', 'BRETT', 'ANDY', 'MOCHI', 'TURBO'];
  const names = ['Shiba Inu', 'Floki Inu', 'Bonk', 'Wif Hat', 'Brett', 'Andy', 'Mochi', 'Turbo'];
  const chains: Array<'ethereum' | 'solana' | 'cosmos'> = ['ethereum', 'solana', 'cosmos'];
  const categories: Array<'meme' | 'defi' | 'bluechip' | 'gaming' | 'infrastructure'> = ['meme', 'defi', 'gaming', 'infrastructure'];
  
  return Array.from({ length: count }, (_, i) => {
    const randomIndex = i % symbols.length;
    const chain = chains[Math.floor(Math.random() * chains.length)];
    const category = categories[Math.floor(Math.random() * categories.length)];
    const price = Math.random() * 100;
    const change24h = (Math.random() - 0.5) * 20;
    
    return {
      id: `token-${i}`,
      symbol: symbols[randomIndex] + (i > 7 ? i : ''),
      name: names[randomIndex] + (i > 7 ? ` ${i}` : ''),
      chain,
      price,
      change24h,
      volume24h: Math.random() * 1000000000,
      marketCap: Math.random() * 10000000000,
      aiScore: Math.floor(Math.random() * 100),
      riskLevel: Math.random() > 0.6 ? 'high' : Math.random() > 0.3 ? 'medium' : 'low',
      category,
      logo: '🪙',
      sentiment: {
        positive: Math.random() * 0.7,
        negative: Math.random() * 0.4,
        neutral: Math.random() * 0.3
      },
      metrics: {
        liquidity: Math.random(),
        holders: Math.floor(Math.random() * 10000000),
        transactions24h: Math.floor(Math.random() * 1000000),
        volatility: Math.random() * 0.3,
        velocity: Math.random() * 0.4
      }
    };
  });
};