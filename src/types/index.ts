export interface Token {
  id: string;
  symbol: string;
  name: string;
  chain: 'ethereum' | 'solana' | 'cosmos';
  price: number;
  change24h: number;
  volume24h: number;
  marketCap: number;
  aiScore: number;
  riskLevel: 'low' | 'medium' | 'high';
  category: 'meme' | 'defi' | 'bluechip' | 'gaming' | 'infrastructure';
  logo: string;
  sentiment: {
    positive: number;
    negative: number;
    neutral: number;
  };
  metrics: {
    liquidity: number;
    holders: number;
    transactions24h: number;
    volatility: number;
    velocity: number;
  };
}

export interface PriceData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface Alert {
  id: string;
  type: 'price' | 'volume' | 'sentiment' | 'risk';
  severity: 'info' | 'warning' | 'critical';
  title: string;
  message: string;
  timestamp: number;
  tokenId?: string;
  isRead: boolean;
  diagnostics?: any;
}

export interface ChainFilter {
  id: string;
  name: string;
  enabled: boolean;
  icon: string;
}

export interface HeatmapData {
  id: string;
  symbol: string;
  volatility: number;
  velocity: number;
  marketCap: number;
  change24h: number;
}