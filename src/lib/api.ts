import type { 
  ApiResponse, 
  PaginatedResponse, 
  Token, 
  TokenAnalysis, 
  SystemAlert,
  SystemMetrics 
} from './types';

class ApiClient {
  private baseUrl: string;
  private timeout: number;
  private maxRetries: number;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
    this.timeout = 30000; // 30 seconds
    this.maxRetries = 3;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    const defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    const config: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
      signal: controller.signal,
    };

    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
      try {
        const response = await fetch(url, config);
        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        return data;
      } catch (error) {
        lastError = error as Error;
        
        // Don't retry on client errors (4xx)
        if (error instanceof Error && error.message.includes('HTTP 4')) {
          break;
        }

        // Don't retry on the last attempt
        if (attempt === this.maxRetries) {
          break;
        }

        // Exponential backoff
        const delay = Math.min(1000 * Math.pow(2, attempt), 10000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    clearTimeout(timeoutId);
    throw lastError || new Error('Request failed after retries');
  }

  // Token Discovery & Analysis
  async getTokens(params?: {
    page?: number;
    limit?: number;
    chain?: string;
    verified?: boolean;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<PaginatedResponse<Token>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.chain) searchParams.set('chain', params.chain);
    if (params?.verified !== undefined) searchParams.set('verified', params.verified.toString());
    if (params?.sortBy) searchParams.set('sort_by', params.sortBy);
    if (params?.sortOrder) searchParams.set('sort_order', params.sortOrder);

    const response = await this.request<PaginatedResponse<Token>>(
      `/api/tokens?${searchParams.toString()}`
    );
    return response.data!;
  }

  async getToken(address: string, chainId?: number): Promise<Token> {
    const params = chainId ? `?chain_id=${chainId}` : '';
    const response = await this.request<Token>(`/api/tokens/${address}${params}`);
    return response.data!;
  }

  async analyzeToken(address: string, chainId?: number): Promise<TokenAnalysis> {
    const response = await this.request<TokenAnalysis>('/api/analyze', {
      method: 'POST',
      body: JSON.stringify({
        token_address: address,
        chain_id: chainId || 1,
      }),
    });
    return response.data!;
  }

  async getTokenAnalysis(analysisId: string): Promise<TokenAnalysis> {
    const response = await this.request<TokenAnalysis>(`/api/analysis/${analysisId}`);
    return response.data!;
  }

  async getRecentDiscoveries(limit = 10): Promise<Token[]> {
    const response = await this.request<Token[]>(`/api/discoveries/recent?limit=${limit}`);
    return response.data!;
  }

  // System Monitoring
  async getSystemMetrics(): Promise<SystemMetrics> {
    const response = await this.request<SystemMetrics>('/api/system/metrics');
    return response.data!;
  }

  async getSystemAlerts(params?: {
    page?: number;
    limit?: number;
    type?: string;
    resolved?: boolean;
  }): Promise<PaginatedResponse<SystemAlert>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.type) searchParams.set('type', params.type);
    if (params?.resolved !== undefined) searchParams.set('resolved', params.resolved.toString());

    const response = await this.request<PaginatedResponse<SystemAlert>>(
      `/api/system/alerts?${searchParams.toString()}`
    );
    return response.data!;
  }

  async resolveAlert(alertId: string): Promise<void> {
    await this.request(`/api/system/alerts/${alertId}/resolve`, {
      method: 'POST',
    });
  }

  // Security & Compliance
  async getSecurityStatus(): Promise<{
    apiInventory: any[];
    boplaDetections: any[];
    botDetections: any[];
    threatIntelligence: any[];
  }> {
    const response = await this.request<any>('/api/security/status');
    return response.data!;
  }

  async getComplianceStatus(): Promise<{
    gdprCompliance: boolean;
    soxCompliance: boolean;
    soc2Compliance: boolean;
    lastAudit: string;
  }> {
    const response = await this.request<any>('/api/compliance/status');
    return response.data!;
  }

  // Performance & Optimization
  async getPerformanceMetrics(): Promise<{
    mlModelAccuracy: number;
    apiResponseTimes: number[];
    cacheHitRate: number;
    resourceUtilization: any;
  }> {
    const response = await this.request<any>('/api/performance/metrics');
    return response.data!;
  }

  async optimizeModel(modelId: string, parameters: any): Promise<void> {
    await this.request(`/api/ml/models/${modelId}/optimize`, {
      method: 'POST',
      body: JSON.stringify(parameters),
    });
  }

  // Real-time Data
  async getTokenPriceHistory(
    address: string,
    timeframe: '1h' | '1d' | '7d' | '30d' = '1d',
    chainId?: number
  ): Promise<Array<{ timestamp: number; price: number; volume: number }>> {
    const params = new URLSearchParams({
      timeframe,
      ...(chainId && { chain_id: chainId.toString() }),
    });

    const response = await this.request<any[]>(
      `/api/tokens/${address}/price-history?${params.toString()}`
    );
    return response.data!;
  }

  async getTokenMetrics(address: string, chainId?: number): Promise<{
    price: number;
    change24h: number;
    volume24h: number;
    marketCap: number;
    holders: number;
  }> {
    const params = chainId ? `?chain_id=${chainId}` : '';
    const response = await this.request<any>(`/api/tokens/${address}/metrics${params}`);
    return response.data!;
  }

  // Health Check
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    version: string;
    uptime: number;
    services: Record<string, 'up' | 'down'>;
  }> {
    const response = await this.request<any>('/api/health');
    return response.data!;
  }
}

// Create singleton instance
export const apiClient = new ApiClient();

// Export individual methods for easier use
export const {
  getTokens,
  getToken,
  analyzeToken,
  getTokenAnalysis,
  getRecentDiscoveries,
  getSystemMetrics,
  getSystemAlerts,
  resolveAlert,
  getSecurityStatus,
  getComplianceStatus,
  getPerformanceMetrics,
  optimizeModel,
  getTokenPriceHistory,
  getTokenMetrics,
  healthCheck,
} = apiClient;
