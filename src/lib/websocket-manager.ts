'use client';

import { EventEmitter } from 'events';

export interface WebSocketManagerConfig {
  url: string;
  protocols?: string[];
  reconnectAttempts?: number;
  reconnectInterval?: number;
  maxReconnectDelay?: number;
  heartbeatInterval?: number;
  connectionTimeout?: number;
  enableLogging?: boolean;
}

export enum WebSocketState {
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
  CLOSED = 'closed',
}

export interface WebSocketMessage {
  id?: string;
  type: string;
  data?: any;
  timestamp: string;
  source?: string;
}

export interface ConnectionMetrics {
  connectTime: number;
  reconnectCount: number;
  messagesSent: number;
  messagesReceived: number;
  bytesTransferred: number;
  lastPingTime?: number;
  averageLatency: number;
  uptime: number;
}

class WebSocketManager extends EventEmitter {
  private config: Required<WebSocketManagerConfig>;
  private ws: WebSocket | null = null;
  private state: WebSocketState = WebSocketState.DISCONNECTED;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private heartbeatTimeout: NodeJS.Timeout | null = null;
  private connectionTimeout: NodeJS.Timeout | null = null;
  private reconnectAttempts = 0;
  private messageQueue: WebSocketMessage[] = [];
  private subscriptions = new Set<string>();
  private metrics: ConnectionMetrics;
  private startTime: number;

  constructor(config: WebSocketManagerConfig) {
    super();
    
    this.config = {
      protocols: [],
      reconnectAttempts: 5,
      reconnectInterval: 1000,
      maxReconnectDelay: 30000,
      heartbeatInterval: 30000,
      connectionTimeout: 10000,
      enableLogging: process.env.NODE_ENV === 'development',
      ...config,
    };

    this.startTime = Date.now();
    this.metrics = {
      connectTime: 0,
      reconnectCount: 0,
      messagesSent: 0,
      messagesReceived: 0,
      bytesTransferred: 0,
      averageLatency: 0,
      uptime: 0,
    };

    this.log('WebSocketManager initialized', this.config);
  }

  private log(message: string, data?: any) {
    if (this.config.enableLogging) {
      console.log(`[WebSocketManager] ${message}`, data || '');
    }
  }

  private logError(message: string, error?: any) {
    console.error(`[WebSocketManager] ${message}`, error || '');
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.state === WebSocketState.CONNECTED) {
        resolve();
        return;
      }

      this.setState(WebSocketState.CONNECTING);
      this.log('Connecting to WebSocket', this.config.url);

      try {
        this.ws = new WebSocket(this.config.url, this.config.protocols);
        
        // Connection timeout
        this.connectionTimeout = setTimeout(() => {
          this.logError('Connection timeout');
          this.ws?.close();
          reject(new Error('Connection timeout'));
        }, this.config.connectionTimeout);

        this.ws.onopen = () => {
          this.clearTimeouts();
          this.setState(WebSocketState.CONNECTED);
          this.reconnectAttempts = 0;
          this.metrics.connectTime = Date.now();
          this.startHeartbeat();
          this.processMessageQueue();
          this.resubscribe();
          
          this.log('WebSocket connected');
          this.emit('connected');
          resolve();
        };

        this.ws.onmessage = (event) => {
          this.handleMessage(event);
        };

        this.ws.onclose = (event) => {
          this.clearTimeouts();
          this.log('WebSocket closed', { code: event.code, reason: event.reason });
          
          if (this.state !== WebSocketState.CLOSED) {
            this.setState(WebSocketState.DISCONNECTED);
            this.emit('disconnected', { code: event.code, reason: event.reason });
            this.attemptReconnect();
          }
        };

        this.ws.onerror = (error) => {
          this.logError('WebSocket error', error);
          this.setState(WebSocketState.ERROR);
          this.emit('error', error);
        };

      } catch (error) {
        this.logError('Failed to create WebSocket connection', error);
        this.setState(WebSocketState.ERROR);
        reject(error);
      }
    });
  }

  disconnect(): void {
    this.log('Disconnecting WebSocket');
    this.setState(WebSocketState.CLOSED);
    this.clearTimeouts();
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    
    this.subscriptions.clear();
    this.messageQueue = [];
    this.emit('disconnected', { code: 1000, reason: 'Client disconnect' });
  }

  send(message: WebSocketMessage): boolean {
    if (this.state !== WebSocketState.CONNECTED) {
      this.log('Queueing message (not connected)', message);
      this.messageQueue.push(message);
      return false;
    }

    try {
      const messageStr = JSON.stringify({
        ...message,
        id: message.id || this.generateMessageId(),
        timestamp: message.timestamp || new Date().toISOString(),
      });

      this.ws!.send(messageStr);
      this.metrics.messagesSent++;
      this.metrics.bytesTransferred += messageStr.length;
      
      this.log('Message sent', message);
      return true;
    } catch (error) {
      this.logError('Failed to send message', error);
      return false;
    }
  }

  subscribe(stream: string): boolean {
    this.subscriptions.add(stream);
    return this.send({
      type: 'subscribe',
      data: { stream },
      timestamp: new Date().toISOString(),
    });
  }

  unsubscribe(stream: string): boolean {
    this.subscriptions.delete(stream);
    return this.send({
      type: 'unsubscribe',
      data: { stream },
      timestamp: new Date().toISOString(),
    });
  }

  getState(): WebSocketState {
    return this.state;
  }

  getMetrics(): ConnectionMetrics {
    return {
      ...this.metrics,
      uptime: Date.now() - this.startTime,
    };
  }

  isConnected(): boolean {
    return this.state === WebSocketState.CONNECTED;
  }

  private setState(newState: WebSocketState): void {
    if (this.state !== newState) {
      const oldState = this.state;
      this.state = newState;
      this.log(`State changed: ${oldState} -> ${newState}`);
      this.emit('stateChange', { oldState, newState });
    }
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const message = JSON.parse(event.data);
      this.metrics.messagesReceived++;
      this.metrics.bytesTransferred += event.data.length;

      // Handle ping/pong for latency measurement
      if (message.type === 'pong' && message.data?.pingTime) {
        const latency = Date.now() - message.data.pingTime;
        this.updateLatency(latency);
        this.metrics.lastPingTime = Date.now();
        return;
      }

      this.log('Message received', message);
      this.emit('message', message);
    } catch (error) {
      this.logError('Failed to parse message', error);
    }
  }

  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.config.reconnectAttempts) {
      this.logError('Max reconnection attempts reached');
      this.setState(WebSocketState.ERROR);
      this.emit('maxReconnectAttemptsReached');
      return;
    }

    const delay = Math.min(
      this.config.reconnectInterval * Math.pow(2, this.reconnectAttempts),
      this.config.maxReconnectDelay
    );

    this.setState(WebSocketState.RECONNECTING);
    this.reconnectAttempts++;
    this.metrics.reconnectCount++;

    this.log(`Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts})`);

    this.reconnectTimeout = setTimeout(() => {
      this.connect().catch((error) => {
        this.logError('Reconnection failed', error);
        this.attemptReconnect();
      });
    }, delay);
  }

  private startHeartbeat(): void {
    this.heartbeatTimeout = setTimeout(() => {
      if (this.state === WebSocketState.CONNECTED) {
        this.send({
          type: 'ping',
          data: { pingTime: Date.now() },
          timestamp: new Date().toISOString(),
        });
        this.startHeartbeat();
      }
    }, this.config.heartbeatInterval);
  }

  private clearTimeouts(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout);
      this.heartbeatTimeout = null;
    }
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = null;
    }
  }

  private processMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.state === WebSocketState.CONNECTED) {
      const message = this.messageQueue.shift()!;
      this.send(message);
    }
  }

  private resubscribe(): void {
    this.subscriptions.forEach(stream => {
      this.send({
        type: 'subscribe',
        data: { stream },
        timestamp: new Date().toISOString(),
      });
    });
  }

  private updateLatency(latency: number): void {
    // Simple moving average for latency
    this.metrics.averageLatency = this.metrics.averageLatency === 0
      ? latency
      : (this.metrics.averageLatency * 0.9) + (latency * 0.1);
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Singleton instance for global use
let globalWebSocketManager: WebSocketManager | null = null;

export function createWebSocketManager(config: WebSocketManagerConfig): WebSocketManager {
  return new WebSocketManager(config);
}

export function getGlobalWebSocketManager(): WebSocketManager | null {
  return globalWebSocketManager;
}

export function setGlobalWebSocketManager(manager: WebSocketManager): void {
  globalWebSocketManager = manager;
}

export { WebSocketManager };
