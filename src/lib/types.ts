// Core Types for CipherScope Crypto Analytics Platform

export interface Token {
  address: string;
  symbol: string;
  name: string;
  chain: string;
  chainId: number;
  decimals: number;
  logoUrl?: string;
  verified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface TokenPrice {
  current: number;
  change24h: number;
  change7d: number;
  change30d: number;
  marketCap: number;
  volume24h: number;
  circulatingSupply: number;
  totalSupply: number;
  maxSupply?: number;
  fullyDilutedValuation?: number;
  lastUpdated: string;
}

export interface TokenAnalysis {
  id: string;
  tokenAddress: string;
  chainId: number;
  overallScore: number;
  riskScore: number;
  alphaScore: number;
  confidenceLevel: number;
  investmentRecommendation: 'BUY' | 'HOLD' | 'SELL' | 'AVOID';
  executionTimeMs: number;
  analyzedAt: string;
  discoveryData?: DiscoveryData;
  validationResults?: ValidationResults;
  chainInfo?: ChainInfo;
  marketData?: MarketData;
  technicalAnalysis?: TechnicalAnalysis;
  sentimentAnalysis?: SentimentAnalysis;
}

export interface DiscoveryData {
  source: string;
  discoveredAt: string;
  trending: boolean;
  volume24h: number;
  priceChange24h: number;
  marketCapRank?: number;
  socialMentions?: number;
}

export interface ValidationResults {
  isHoneypot: boolean;
  liquidityScore: number;
  contractVerified: boolean;
  ownershipRenounced: boolean;
  hasProxyContract: boolean;
  hasTimelock: boolean;
  rugPullRisk: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  securityScore: number;
  auditStatus?: 'AUDITED' | 'UNAUDITED' | 'PENDING';
}

export interface ChainInfo {
  chainId: number;
  chainName: string;
  nativeCurrency: string;
  blockExplorer: string;
  rpcUrl: string;
  gasPrice: number;
  blockTime: number;
  totalValueLocked?: number;
}

export interface MarketData {
  price: TokenPrice;
  exchanges: Exchange[];
  liquidityPools: LiquidityPool[];
  holders: HolderData;
  transactions: TransactionData;
}

export interface Exchange {
  name: string;
  pair: string;
  price: number;
  volume24h: number;
  liquidity: number;
  lastUpdated: string;
}

export interface LiquidityPool {
  dex: string;
  pair: string;
  liquidity: number;
  volume24h: number;
  fee: number;
  apy?: number;
}

export interface HolderData {
  totalHolders: number;
  top10Percentage: number;
  top100Percentage: number;
  whaleCount: number;
  averageHolding: number;
}

export interface TransactionData {
  transactions24h: number;
  uniqueWallets24h: number;
  averageTransactionSize: number;
  largestTransaction24h: number;
}

export interface TechnicalAnalysis {
  indicators: TechnicalIndicators;
  patterns: TechnicalPatterns;
  signals: TradingSignals;
  support: number[];
  resistance: number[];
  trend: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
  momentum: 'STRONG' | 'WEAK' | 'NEUTRAL';
}

export interface TechnicalIndicators {
  rsi: number;
  macd: {
    macd: number;
    signal: number;
    histogram: number;
  };
  bollinger: {
    upper: number;
    middle: number;
    lower: number;
  };
  ema: {
    ema12: number;
    ema26: number;
    ema50: number;
    ema200: number;
  };
  volume: {
    sma: number;
    ratio: number;
  };
}

export interface TechnicalPatterns {
  detected: string[];
  confidence: number;
  timeframe: string;
  breakoutProbability?: number;
}

export interface TradingSignals {
  overall: 'BUY' | 'SELL' | 'HOLD';
  strength: number;
  signals: {
    indicator: string;
    signal: 'BUY' | 'SELL' | 'NEUTRAL';
    strength: number;
  }[];
}

export interface SentimentAnalysis {
  overall: 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL';
  score: number;
  sources: SentimentSource[];
  socialMetrics: SocialMetrics;
  newsAnalysis: NewsAnalysis;
}

export interface SentimentSource {
  platform: string;
  sentiment: 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL';
  score: number;
  mentions: number;
  engagement: number;
  lastUpdated: string;
}

export interface SocialMetrics {
  twitterFollowers?: number;
  twitterMentions24h?: number;
  redditSubscribers?: number;
  redditPosts24h?: number;
  telegramMembers?: number;
  discordMembers?: number;
}

export interface NewsAnalysis {
  articles: NewsArticle[];
  overallSentiment: 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL';
  sentimentScore: number;
}

export interface NewsArticle {
  title: string;
  source: string;
  url: string;
  publishedAt: string;
  sentiment: 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL';
  score: number;
  summary?: string;
}

// WebSocket Types
export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
}

export interface RealTimeUpdate {
  tokenAddress: string;
  price: number;
  change24h: number;
  volume24h: number;
  timestamp: string;
}

export interface SystemAlert {
  id: string;
  type: 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL';
  title: string;
  message: string;
  timestamp: string;
  resolved: boolean;
  metadata?: Record<string, any>;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Chart Types
export interface ChartDataPoint {
  timestamp: number;
  value: number;
  volume?: number;
}

export interface CandlestickData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

// UI State Types
export interface UIState {
  theme: 'light' | 'dark' | 'system';
  sidebarCollapsed: boolean;
  activeFilters: Record<string, any>;
  selectedTokens: string[];
  viewMode: 'grid' | 'list' | 'chart';
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
}

// Configuration Types
export interface AppConfig {
  apiUrl: string;
  wsUrl: string;
  refreshInterval: number;
  maxRetries: number;
  timeout: number;
  features: {
    realTimeUpdates: boolean;
    advancedCharts: boolean;
    notifications: boolean;
    darkMode: boolean;
  };
}
