import { Suspense } from 'react';
import { DashboardLayout } from '@/components/dashboard/dashboard-layout';
import { PerformanceOverview } from '@/components/monitoring/performance-overview';
import { SystemMetrics } from '@/components/monitoring/system-metrics';
import { ApiPerformance } from '@/components/monitoring/api-performance';
import { MlModelMetrics } from '@/components/monitoring/ml-model-metrics';
import { ResourceUtilization } from '@/components/monitoring/resource-utilization';
import { PerformanceOptimization } from '@/components/monitoring/performance-optimization';
import { SystemMonitoring } from '@/components/dashboard/system-monitoring';
import { ConnectionStatus } from '@/components/real-time/connection-status';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { 
  Activity, 
  Cpu, 
  Zap, 
  BarChart3,
  TrendingUp,
  Settings
} from 'lucide-react';

export default function MonitoringPage() {
  return (
    <DashboardLayout>
      <div className="flex-1 space-y-6 p-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-gradient">
              Performance Monitoring
            </h1>
            <p className="text-muted-foreground">
              Advanced system monitoring, ML model performance, and optimization insights
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            <ConnectionStatus showDetails={true} showMetrics={true} />
          </div>
        </div>

        {/* Performance Overview */}
        <Suspense fallback={<LoadingSpinner />}>
          <PerformanceOverview />
        </Suspense>

        {/* Main Monitoring Grid */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Left Column */}
          <div className="space-y-6">
            <Suspense fallback={<LoadingSpinner />}>
              <SystemMetrics />
            </Suspense>
            
            <Suspense fallback={<LoadingSpinner />}>
              <MlModelMetrics />
            </Suspense>
            
            <Suspense fallback={<LoadingSpinner />}>
              <PerformanceOptimization />
            </Suspense>
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            <Suspense fallback={<LoadingSpinner />}>
              <ApiPerformance />
            </Suspense>
            
            <Suspense fallback={<LoadingSpinner />}>
              <ResourceUtilization />
            </Suspense>
            
            <Suspense fallback={<LoadingSpinner />}>
              <SystemMonitoring />
            </Suspense>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
