import { Suspense } from 'react';
import { DashboardLayout } from '@/components/dashboard/dashboard-layout';
import { SecurityOverview } from '@/components/security/security-overview';
import { ApiInventory } from '@/components/security/api-inventory';
import { BoplDetection } from '@/components/security/bopl-detection';
import { BotDetection } from '@/components/security/bot-detection';
import { ThreatIntelligence } from '@/components/security/threat-intelligence';
import { ComplianceMonitoring } from '@/components/security/compliance-monitoring';
import { SecurityAlerts } from '@/components/dashboard/security-alerts';
import { ConnectionStatus } from '@/components/real-time/connection-status';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { 
  Shield, 
  Eye, 
  Bot, 
  AlertTriangle,
  FileText,
  Activity
} from 'lucide-react';

export default function SecurityPage() {
  return (
    <DashboardLayout>
      <div className="flex-1 space-y-6 p-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-gradient">
              Security Dashboard
            </h1>
            <p className="text-muted-foreground">
              Comprehensive security monitoring, threat detection, and compliance management
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            <ConnectionStatus showDetails={false} />
          </div>
        </div>

        {/* Security Overview */}
        <Suspense fallback={<LoadingSpinner />}>
          <SecurityOverview />
        </Suspense>

        {/* Main Security Grid */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Left Column */}
          <div className="space-y-6">
            <Suspense fallback={<LoadingSpinner />}>
              <ApiInventory />
            </Suspense>
            
            <Suspense fallback={<LoadingSpinner />}>
              <BotDetection />
            </Suspense>
            
            <Suspense fallback={<LoadingSpinner />}>
              <ComplianceMonitoring />
            </Suspense>
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            <Suspense fallback={<LoadingSpinner />}>
              <BoplDetection />
            </Suspense>
            
            <Suspense fallback={<LoadingSpinner />}>
              <ThreatIntelligence />
            </Suspense>
            
            <Suspense fallback={<LoadingSpinner />}>
              <SecurityAlerts />
            </Suspense>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
