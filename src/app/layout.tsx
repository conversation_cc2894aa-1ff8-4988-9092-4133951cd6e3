import type { <PERSON>ada<PERSON> } from 'next';
import { Inter, JetBrains_Mono } from 'next/font/google';
import { ThemeProvider } from '@/components/theme-provider';
import { QueryProvider } from '@/components/query-provider';
import { Toaster } from '@/components/ui/sonner';
import { cn } from '@/lib/utils';
import './globals.css';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

const jetbrainsMono = JetBrains_Mono({
  subsets: ['latin'],
  variable: '--font-mono',
  display: 'swap',
});

export const metadata: Metadata = {
  title: {
    default: 'CipherScope - Advanced Crypto Analytics Platform',
    template: '%s | CipherScope',
  },
  description: 'Enterprise-grade cryptocurrency token analysis and discovery platform with real-time monitoring, AI-powered insights, and comprehensive security features.',
  keywords: [
    'cryptocurrency',
    'crypto analytics',
    'token analysis',
    'DeFi',
    'blockchain',
    'real-time monitoring',
    'AI insights',
    'security analysis',
    'trading dashboard',
  ],
  authors: [{ name: 'CipherScope Team' }],
  creator: 'CipherScope',
  publisher: 'CipherScope',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: '/',
    title: 'CipherScope - Advanced Crypto Analytics Platform',
    description: 'Enterprise-grade cryptocurrency token analysis and discovery platform with real-time monitoring and AI-powered insights.',
    siteName: 'CipherScope',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'CipherScope - Advanced Crypto Analytics Platform',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'CipherScope - Advanced Crypto Analytics Platform',
    description: 'Enterprise-grade cryptocurrency token analysis and discovery platform with real-time monitoring and AI-powered insights.',
    images: ['/og-image.png'],
    creator: '@cipherscope',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#000000" />
        <meta name="color-scheme" content="dark light" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
      </head>
      <body
        className={cn(
          'min-h-screen bg-background font-sans antialiased',
          inter.variable,
          jetbrainsMono.variable
        )}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <QueryProvider>
            <div className="relative flex min-h-screen flex-col">
              <div className="flex-1">{children}</div>
            </div>
            <Toaster 
              position="bottom-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: 'hsl(var(--background))',
                  color: 'hsl(var(--foreground))',
                  border: '1px solid hsl(var(--border))',
                },
              }}
            />
          </QueryProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
