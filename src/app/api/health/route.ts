import { NextRequest, NextResponse } from 'next/server';

// Health check endpoint for monitoring and load balancers
export async function GET(request: NextRequest) {
  try {
    // Basic health checks
    const healthChecks = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      memory: {
        used: Math.round((process.memoryUsage().heapUsed / 1024 / 1024) * 100) / 100,
        total: Math.round((process.memoryUsage().heapTotal / 1024 / 1024) * 100) / 100,
        external: Math.round((process.memoryUsage().external / 1024 / 1024) * 100) / 100,
      },
      checks: {
        server: 'ok',
        database: 'ok', // Would check actual DB connection in real app
        cache: 'ok',    // Would check Redis connection in real app
        external_apis: 'ok',
      },
    };

    // Perform additional checks based on query parameters
    const detailed = request.nextUrl.searchParams.get('detailed') === 'true';
    
    if (detailed) {
      // Add more detailed health information
      healthChecks.checks = {
        ...healthChecks.checks,
        node_version: process.version,
        platform: process.platform,
        arch: process.arch,
        load_average: process.platform !== 'win32' ? require('os').loadavg() : [0, 0, 0],
        free_memory: Math.round((require('os').freemem() / 1024 / 1024) * 100) / 100,
        total_memory: Math.round((require('os').totalmem() / 1024 / 1024) * 100) / 100,
      };
    }

    // Check if any critical services are down
    const criticalChecks = ['server', 'database'];
    const hasFailures = criticalChecks.some(check => 
      healthChecks.checks[check as keyof typeof healthChecks.checks] !== 'ok'
    );

    if (hasFailures) {
      healthChecks.status = 'unhealthy';
      return NextResponse.json(healthChecks, { status: 503 });
    }

    return NextResponse.json(healthChecks, { 
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
      message: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 503 });
  }
}

// Support HEAD requests for simple health checks
export async function HEAD(request: NextRequest) {
  try {
    // Simple check - just return 200 if server is responding
    return new NextResponse(null, { 
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    });
  } catch (error) {
    return new NextResponse(null, { status: 503 });
  }
}
