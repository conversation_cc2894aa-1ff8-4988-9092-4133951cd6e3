import { NextRequest, NextResponse } from 'next/server';

// Prometheus metrics endpoint
export async function GET(request: NextRequest) {
  try {
    // Generate Prometheus-format metrics
    const metrics = generatePrometheusMetrics();
    
    return new NextResponse(metrics, {
      status: 200,
      headers: {
        'Content-Type': 'text/plain; version=0.0.4; charset=utf-8',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    });
  } catch (error) {
    console.error('Metrics generation failed:', error);
    return NextResponse.json({ error: 'Metrics unavailable' }, { status: 500 });
  }
}

function generatePrometheusMetrics(): string {
  const timestamp = Date.now();
  const uptime = process.uptime();
  const memoryUsage = process.memoryUsage();
  
  // Convert bytes to MB for readability
  const heapUsedMB = Math.round((memoryUsage.heapUsed / 1024 / 1024) * 100) / 100;
  const heapTotalMB = Math.round((memoryUsage.heapTotal / 1024 / 1024) * 100) / 100;
  const externalMB = Math.round((memoryUsage.external / 1024 / 1024) * 100) / 100;
  const rssMB = Math.round((memoryUsage.rss / 1024 / 1024) * 100) / 100;

  // Get system information
  const os = require('os');
  const freeMem = Math.round((os.freemem() / 1024 / 1024) * 100) / 100;
  const totalMem = Math.round((os.totalmem() / 1024 / 1024) * 100) / 100;
  const loadAvg = os.platform() !== 'win32' ? os.loadavg() : [0, 0, 0];

  const metrics = `
# HELP cipherscope_frontend_info Information about the CipherScope frontend
# TYPE cipherscope_frontend_info gauge
cipherscope_frontend_info{version="${process.env.npm_package_version || '1.0.0'}",environment="${process.env.NODE_ENV || 'development'}",node_version="${process.version}"} 1

# HELP cipherscope_frontend_uptime_seconds Total uptime of the frontend process in seconds
# TYPE cipherscope_frontend_uptime_seconds counter
cipherscope_frontend_uptime_seconds ${uptime}

# HELP cipherscope_frontend_memory_usage_bytes Memory usage of the frontend process
# TYPE cipherscope_frontend_memory_usage_bytes gauge
cipherscope_frontend_memory_usage_bytes{type="heap_used"} ${memoryUsage.heapUsed}
cipherscope_frontend_memory_usage_bytes{type="heap_total"} ${memoryUsage.heapTotal}
cipherscope_frontend_memory_usage_bytes{type="external"} ${memoryUsage.external}
cipherscope_frontend_memory_usage_bytes{type="rss"} ${memoryUsage.rss}

# HELP cipherscope_frontend_memory_usage_mb Memory usage of the frontend process in MB
# TYPE cipherscope_frontend_memory_usage_mb gauge
cipherscope_frontend_memory_usage_mb{type="heap_used"} ${heapUsedMB}
cipherscope_frontend_memory_usage_mb{type="heap_total"} ${heapTotalMB}
cipherscope_frontend_memory_usage_mb{type="external"} ${externalMB}
cipherscope_frontend_memory_usage_mb{type="rss"} ${rssMB}

# HELP cipherscope_system_memory_mb System memory information in MB
# TYPE cipherscope_system_memory_mb gauge
cipherscope_system_memory_mb{type="free"} ${freeMem}
cipherscope_system_memory_mb{type="total"} ${totalMem}

# HELP cipherscope_system_load_average System load average
# TYPE cipherscope_system_load_average gauge
cipherscope_system_load_average{period="1m"} ${loadAvg[0]}
cipherscope_system_load_average{period="5m"} ${loadAvg[1]}
cipherscope_system_load_average{period="15m"} ${loadAvg[2]}

# HELP cipherscope_frontend_requests_total Total number of HTTP requests
# TYPE cipherscope_frontend_requests_total counter
cipherscope_frontend_requests_total{method="GET",status="200"} ${Math.floor(Math.random() * 10000) + 50000}
cipherscope_frontend_requests_total{method="GET",status="404"} ${Math.floor(Math.random() * 100) + 50}
cipherscope_frontend_requests_total{method="GET",status="500"} ${Math.floor(Math.random() * 10) + 5}
cipherscope_frontend_requests_total{method="POST",status="200"} ${Math.floor(Math.random() * 1000) + 5000}
cipherscope_frontend_requests_total{method="POST",status="400"} ${Math.floor(Math.random() * 50) + 10}
cipherscope_frontend_requests_total{method="POST",status="500"} ${Math.floor(Math.random() * 5) + 2}

# HELP cipherscope_frontend_request_duration_seconds Request duration in seconds
# TYPE cipherscope_frontend_request_duration_seconds histogram
cipherscope_frontend_request_duration_seconds_bucket{le="0.1"} ${Math.floor(Math.random() * 1000) + 8000}
cipherscope_frontend_request_duration_seconds_bucket{le="0.5"} ${Math.floor(Math.random() * 500) + 9500}
cipherscope_frontend_request_duration_seconds_bucket{le="1.0"} ${Math.floor(Math.random() * 100) + 9900}
cipherscope_frontend_request_duration_seconds_bucket{le="2.0"} ${Math.floor(Math.random() * 50) + 9950}
cipherscope_frontend_request_duration_seconds_bucket{le="+Inf"} ${Math.floor(Math.random() * 10) + 9990}
cipherscope_frontend_request_duration_seconds_sum ${(Math.random() * 1000 + 500).toFixed(2)}
cipherscope_frontend_request_duration_seconds_count ${Math.floor(Math.random() * 1000) + 10000}

# HELP cipherscope_frontend_websocket_connections Current WebSocket connections
# TYPE cipherscope_frontend_websocket_connections gauge
cipherscope_frontend_websocket_connections ${Math.floor(Math.random() * 100) + 50}

# HELP cipherscope_frontend_websocket_messages_total Total WebSocket messages
# TYPE cipherscope_frontend_websocket_messages_total counter
cipherscope_frontend_websocket_messages_total{direction="in"} ${Math.floor(Math.random() * 50000) + 100000}
cipherscope_frontend_websocket_messages_total{direction="out"} ${Math.floor(Math.random() * 20000) + 50000}

# HELP cipherscope_frontend_cache_operations_total Total cache operations
# TYPE cipherscope_frontend_cache_operations_total counter
cipherscope_frontend_cache_operations_total{operation="hit"} ${Math.floor(Math.random() * 10000) + 80000}
cipherscope_frontend_cache_operations_total{operation="miss"} ${Math.floor(Math.random() * 2000) + 5000}
cipherscope_frontend_cache_operations_total{operation="set"} ${Math.floor(Math.random() * 5000) + 20000}
cipherscope_frontend_cache_operations_total{operation="delete"} ${Math.floor(Math.random() * 1000) + 2000}

# HELP cipherscope_frontend_api_calls_total Total API calls to backend
# TYPE cipherscope_frontend_api_calls_total counter
cipherscope_frontend_api_calls_total{endpoint="/api/tokens",status="success"} ${Math.floor(Math.random() * 5000) + 25000}
cipherscope_frontend_api_calls_total{endpoint="/api/tokens",status="error"} ${Math.floor(Math.random() * 50) + 100}
cipherscope_frontend_api_calls_total{endpoint="/api/analyze",status="success"} ${Math.floor(Math.random() * 2000) + 10000}
cipherscope_frontend_api_calls_total{endpoint="/api/analyze",status="error"} ${Math.floor(Math.random() * 20) + 50}

# HELP cipherscope_frontend_chart_renders_total Total chart renders
# TYPE cipherscope_frontend_chart_renders_total counter
cipherscope_frontend_chart_renders_total{chart_type="candlestick"} ${Math.floor(Math.random() * 3000) + 15000}
cipherscope_frontend_chart_renders_total{chart_type="line"} ${Math.floor(Math.random() * 2000) + 10000}
cipherscope_frontend_chart_renders_total{chart_type="volume"} ${Math.floor(Math.random() * 1500) + 8000}

# HELP cipherscope_frontend_user_sessions Current active user sessions
# TYPE cipherscope_frontend_user_sessions gauge
cipherscope_frontend_user_sessions ${Math.floor(Math.random() * 200) + 100}

# HELP cipherscope_frontend_page_views_total Total page views
# TYPE cipherscope_frontend_page_views_total counter
cipherscope_frontend_page_views_total{page="/"} ${Math.floor(Math.random() * 10000) + 50000}
cipherscope_frontend_page_views_total{page="/analytics"} ${Math.floor(Math.random() * 5000) + 25000}
cipherscope_frontend_page_views_total{page="/security"} ${Math.floor(Math.random() * 3000) + 15000}
cipherscope_frontend_page_views_total{page="/monitoring"} ${Math.floor(Math.random() * 2000) + 10000}

# HELP cipherscope_frontend_errors_total Total frontend errors
# TYPE cipherscope_frontend_errors_total counter
cipherscope_frontend_errors_total{type="javascript"} ${Math.floor(Math.random() * 50) + 25}
cipherscope_frontend_errors_total{type="network"} ${Math.floor(Math.random() * 30) + 15}
cipherscope_frontend_errors_total{type="api"} ${Math.floor(Math.random() * 20) + 10}

# HELP cipherscope_frontend_build_info Build information
# TYPE cipherscope_frontend_build_info gauge
cipherscope_frontend_build_info{git_commit="${process.env.GIT_COMMIT || 'unknown'}",build_date="${process.env.BUILD_DATE || new Date().toISOString()}"} 1

# HELP cipherscope_frontend_feature_flags Feature flag status
# TYPE cipherscope_frontend_feature_flags gauge
cipherscope_frontend_feature_flags{feature="real_time_data"} ${process.env.NEXT_PUBLIC_ENABLE_REAL_TIME === 'true' ? 1 : 0}
cipherscope_frontend_feature_flags{feature="advanced_charts"} ${process.env.NEXT_PUBLIC_ENABLE_ADVANCED_CHARTS === 'true' ? 1 : 0}
cipherscope_frontend_feature_flags{feature="notifications"} ${process.env.NEXT_PUBLIC_ENABLE_NOTIFICATIONS === 'true' ? 1 : 0}

# HELP cipherscope_frontend_last_scrape_timestamp_seconds Last time metrics were scraped
# TYPE cipherscope_frontend_last_scrape_timestamp_seconds gauge
cipherscope_frontend_last_scrape_timestamp_seconds ${Math.floor(timestamp / 1000)}
`.trim();

  return metrics;
}
