import { Suspense } from 'react';
import { DashboardLayout } from '@/components/dashboard/dashboard-layout';
import { DashboardOverview } from '@/components/dashboard/dashboard-overview';
import { TokenDiscovery } from '@/components/dashboard/token-discovery';
import { SystemMonitoring } from '@/components/dashboard/system-monitoring';
import { SecurityAlerts } from '@/components/dashboard/security-alerts';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

export default function HomePage() {
  return (
    <DashboardLayout>
      <div className="flex-1 space-y-6 p-6">
        {/* Dashboard Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-gradient">
              CipherScope Dashboard
            </h1>
            <p className="text-muted-foreground">
              Advanced crypto analytics with real-time monitoring and AI-powered insights
            </p>
          </div>
        </div>

        {/* Dashboard Overview Cards */}
        <Suspense fallback={<LoadingSpinner />}>
          <DashboardOverview />
        </Suspense>

        {/* Main Dashboard Grid */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Token Discovery Section */}
          <div className="space-y-6">
            <Suspense fallback={<LoadingSpinner />}>
              <TokenDiscovery />
            </Suspense>
          </div>

          {/* Monitoring & Security Section */}
          <div className="space-y-6">
            <Suspense fallback={<LoadingSpinner />}>
              <SystemMonitoring />
            </Suspense>
            
            <Suspense fallback={<LoadingSpinner />}>
              <SecurityAlerts />
            </Suspense>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
