"""
BOPLA (Broken Object Property Level Authorization) Vulnerability Detection System

This module implements field-level API access monitoring to detect BOPLA attacks,
which are more prevalent than BOLA in 2025. BOPLA occurs when applications fail
to properly authorize access to specific object properties/fields.

Features:
- Real-time field-level access monitoring
- User permission mapping and validation
- Sensitive field classification and protection
- Anomaly detection for unauthorized field access
- Machine learning-based access pattern analysis
- Automated policy enforcement and alerting
"""

import asyncio
import json
import logging
import re
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple, Union
import threading
import hashlib

import structlog
from fastapi import Request, Response

from ..core.config import config
from ..core.logging_config import get_logger
from ..compliance.audit_framework import AuditTrail, AuditEventType, RiskLevel
from ..monitoring.dashboard import metrics_collector, increment_counter, set_gauge

logger = get_logger(__name__)


class FieldSensitivity(Enum):
    """Field sensitivity levels for BOPLA detection."""
    PUBLIC = "public"
    INTERNAL = "internal"
    SENSITIVE = "sensitive"
    CONFIDENTIAL = "confidential"
    RESTRICTED = "restricted"


class AccessPattern(Enum):
    """Access pattern types."""
    NORMAL = "normal"
    SUSPICIOUS = "suspicious"
    ANOMALOUS = "anomalous"
    MALICIOUS = "malicious"


class UserRole(Enum):
    """User role types."""
    ANONYMOUS = "anonymous"
    USER = "user"
    PREMIUM = "premium"
    ADMIN = "admin"
    SYSTEM = "system"


@dataclass
class FieldAccessRule:
    """Field access authorization rule."""
    field_pattern: str
    allowed_roles: List[UserRole]
    sensitivity: FieldSensitivity
    read_allowed: bool = True
    write_allowed: bool = False
    conditions: Dict[str, Any] = field(default_factory=dict)
    description: str = ""


@dataclass
class AccessAttempt:
    """Record of a field access attempt."""
    user_id: Optional[str]
    user_role: UserRole
    endpoint: str
    method: str
    field_path: str
    field_value: Optional[str]
    access_type: str  # 'read' or 'write'
    timestamp: datetime
    ip_address: Optional[str]
    user_agent: Optional[str]
    authorized: bool
    sensitivity: FieldSensitivity
    context: Dict[str, Any] = field(default_factory=dict)


@dataclass
class BOPLAAlert:
    """BOPLA security alert."""
    alert_id: str
    user_id: Optional[str]
    user_role: UserRole
    violation_type: str
    field_path: str
    endpoint: str
    severity: str
    timestamp: datetime
    details: Dict[str, Any]
    risk_score: float


class BOPLADetector:
    """
    Advanced BOPLA vulnerability detection system.
    
    Monitors field-level access patterns and detects unauthorized access
    to sensitive object properties with ML-based anomaly detection.
    """
    
    def __init__(self, audit_trail: Optional[AuditTrail] = None):
        self.audit_trail = audit_trail
        self.access_rules = self._initialize_access_rules()
        self.access_history: deque = deque(maxlen=100000)  # Keep last 100k access attempts
        self.user_patterns: Dict[str, Dict[str, Any]] = defaultdict(dict)
        self.alerts: List[BOPLAAlert] = []
        self._lock = threading.RLock()
        
        # Pattern analysis
        self.suspicious_patterns = self._initialize_suspicious_patterns()
        self.baseline_patterns: Dict[str, Dict[str, float]] = defaultdict(dict)
        
        # ML-based anomaly detection parameters
        self.anomaly_threshold = 0.7
        self.learning_window_hours = 24
        self.min_samples_for_baseline = 100
        
        logger.info("BOPLADetector initialized")
    
    def _initialize_access_rules(self) -> List[FieldAccessRule]:
        """Initialize field access authorization rules."""
        return [
            # Crypto-specific sensitive fields
            FieldAccessRule(
                field_pattern=r".*private.*key.*",
                allowed_roles=[UserRole.SYSTEM],
                sensitivity=FieldSensitivity.RESTRICTED,
                read_allowed=False,
                write_allowed=False,
                description="Private keys should never be accessible"
            ),
            FieldAccessRule(
                field_pattern=r".*secret.*",
                allowed_roles=[UserRole.ADMIN, UserRole.SYSTEM],
                sensitivity=FieldSensitivity.CONFIDENTIAL,
                read_allowed=False,
                write_allowed=False,
                description="Secret values restricted to admin access"
            ),
            FieldAccessRule(
                field_pattern=r".*(balance|amount|value).*",
                allowed_roles=[UserRole.USER, UserRole.PREMIUM, UserRole.ADMIN, UserRole.SYSTEM],
                sensitivity=FieldSensitivity.SENSITIVE,
                read_allowed=True,
                write_allowed=False,
                description="Financial data readable by authenticated users"
            ),
            FieldAccessRule(
                field_pattern=r".*(email|phone|address).*",
                allowed_roles=[UserRole.USER, UserRole.PREMIUM, UserRole.ADMIN, UserRole.SYSTEM],
                sensitivity=FieldSensitivity.CONFIDENTIAL,
                read_allowed=True,
                write_allowed=False,
                conditions={"owner_only": True},
                description="Personal data accessible only to owner"
            ),
            FieldAccessRule(
                field_pattern=r".*(api.*key|token|auth).*",
                allowed_roles=[UserRole.ADMIN, UserRole.SYSTEM],
                sensitivity=FieldSensitivity.CONFIDENTIAL,
                read_allowed=False,
                write_allowed=False,
                description="API credentials restricted to admin access"
            ),
            FieldAccessRule(
                field_pattern=r".*(user.*id|customer.*id).*",
                allowed_roles=[UserRole.USER, UserRole.PREMIUM, UserRole.ADMIN, UserRole.SYSTEM],
                sensitivity=FieldSensitivity.INTERNAL,
                read_allowed=True,
                write_allowed=False,
                conditions={"context_required": True},
                description="User IDs require proper context"
            ),
            # Public fields
            FieldAccessRule(
                field_pattern=r".*(name|symbol|description|public).*",
                allowed_roles=[UserRole.ANONYMOUS, UserRole.USER, UserRole.PREMIUM, UserRole.ADMIN, UserRole.SYSTEM],
                sensitivity=FieldSensitivity.PUBLIC,
                read_allowed=True,
                write_allowed=False,
                description="Public information accessible to all"
            ),
        ]
    
    def _initialize_suspicious_patterns(self) -> Dict[str, List[str]]:
        """Initialize patterns that indicate suspicious access."""
        return {
            "field_enumeration": [
                r".*\.(.*\.){5,}.*",  # Deep field traversal
                r".*\[\d+\]\..*\[\d+\]\..*",  # Array index enumeration
            ],
            "sensitive_field_probing": [
                r".*(admin|root|system|internal).*",
                r".*(password|secret|key|token).*",
                r".*(config|settings|env).*",
            ],
            "bulk_access": [
                r".*users?\[\d+\]\..*",  # Accessing multiple user records
                r".*accounts?\[\d+\]\..*",  # Accessing multiple accounts
            ],
            "privilege_escalation": [
                r".*(role|permission|access|privilege).*",
                r".*(admin|superuser|root).*",
            ]
        }
    
    async def monitor_field_access(self, request: Request, response: Response,
                                  request_data: Optional[Dict[str, Any]] = None,
                                  response_data: Optional[Dict[str, Any]] = None,
                                  user_context: Optional[Dict[str, Any]] = None) -> List[BOPLAAlert]:
        """
        Monitor field access in API request/response for BOPLA vulnerabilities.
        
        Args:
            request: FastAPI request object
            response: FastAPI response object
            request_data: Parsed request data
            response_data: Parsed response data
            user_context: User authentication context
            
        Returns:
            List of BOPLA alerts if violations detected
        """
        alerts = []
        
        try:
            # Extract user information
            user_id = user_context.get("user_id") if user_context else None
            user_role = self._determine_user_role(user_context)
            
            # Analyze request fields (write access)
            if request_data:
                request_alerts = await self._analyze_field_access(
                    data=request_data,
                    access_type="write",
                    user_id=user_id,
                    user_role=user_role,
                    endpoint=str(request.url.path),
                    method=request.method,
                    ip_address=request.client.host if request.client else None,
                    user_agent=request.headers.get("user-agent")
                )
                alerts.extend(request_alerts)
            
            # Analyze response fields (read access)
            if response_data and response.status_code < 400:
                response_alerts = await self._analyze_field_access(
                    data=response_data,
                    access_type="read",
                    user_id=user_id,
                    user_role=user_role,
                    endpoint=str(request.url.path),
                    method=request.method,
                    ip_address=request.client.host if request.client else None,
                    user_agent=request.headers.get("user-agent")
                )
                alerts.extend(response_alerts)
            
            # Update metrics
            increment_counter("bopla_field_accesses_monitored", 1)
            if alerts:
                increment_counter("bopla_violations_detected", len(alerts))
                set_gauge("bopla_active_alerts", len(self.alerts))
            
        except Exception as e:
            logger.error(f"Failed to monitor field access: {e}")
            increment_counter("bopla_monitoring_errors", 1)
        
        return alerts

    def _determine_user_role(self, user_context: Optional[Dict[str, Any]]) -> UserRole:
        """Determine user role from context."""
        if not user_context:
            return UserRole.ANONYMOUS

        role_str = user_context.get("role", "").lower()
        if role_str == "admin":
            return UserRole.ADMIN
        elif role_str == "premium":
            return UserRole.PREMIUM
        elif role_str == "system":
            return UserRole.SYSTEM
        elif user_context.get("authenticated", False):
            return UserRole.USER
        else:
            return UserRole.ANONYMOUS

    async def _analyze_field_access(self, data: Dict[str, Any], access_type: str,
                                   user_id: Optional[str], user_role: UserRole,
                                   endpoint: str, method: str,
                                   ip_address: Optional[str], user_agent: Optional[str]) -> List[BOPLAAlert]:
        """Analyze field access patterns for BOPLA violations."""
        alerts = []

        try:
            # Extract all field paths from the data
            field_paths = self._extract_field_paths(data)

            for field_path, field_value in field_paths:
                # Check authorization for this field
                access_attempt = AccessAttempt(
                    user_id=user_id,
                    user_role=user_role,
                    endpoint=endpoint,
                    method=method,
                    field_path=field_path,
                    field_value=str(field_value)[:100] if field_value else None,  # Truncate for logging
                    access_type=access_type,
                    timestamp=datetime.now(timezone.utc),
                    ip_address=ip_address,
                    user_agent=user_agent,
                    authorized=False,  # Will be updated
                    sensitivity=FieldSensitivity.PUBLIC  # Will be updated
                )

                # Check field authorization
                violation = await self._check_field_authorization(access_attempt, field_value)

                if violation:
                    alert = BOPLAAlert(
                        alert_id=self._generate_alert_id(access_attempt),
                        user_id=user_id,
                        user_role=user_role,
                        violation_type=violation["type"],
                        field_path=field_path,
                        endpoint=endpoint,
                        severity=violation["severity"],
                        timestamp=access_attempt.timestamp,
                        details=violation["details"],
                        risk_score=violation["risk_score"]
                    )

                    alerts.append(alert)
                    await self._handle_bopla_alert(alert)

                # Record access attempt for pattern analysis
                with self._lock:
                    self.access_history.append(access_attempt)

                # Update user patterns (async)
                await self._update_user_patterns(access_attempt)

        except Exception as e:
            logger.error(f"Failed to analyze field access: {e}")

        return alerts

    def _extract_field_paths(self, data: Any, prefix: str = "") -> List[Tuple[str, Any]]:
        """Extract all field paths from nested data structure."""
        paths = []

        try:
            if isinstance(data, dict):
                for key, value in data.items():
                    current_path = f"{prefix}.{key}" if prefix else key
                    paths.append((current_path, value))

                    # Recursively extract nested paths
                    if isinstance(value, (dict, list)):
                        nested_paths = self._extract_field_paths(value, current_path)
                        paths.extend(nested_paths)

            elif isinstance(data, list):
                for i, item in enumerate(data):
                    current_path = f"{prefix}[{i}]"
                    paths.append((current_path, item))

                    if isinstance(item, (dict, list)):
                        nested_paths = self._extract_field_paths(item, current_path)
                        paths.extend(nested_paths)

        except Exception as e:
            logger.error(f"Failed to extract field paths: {e}")

        return paths

    async def _check_field_authorization(self, access_attempt: AccessAttempt,
                                       field_value: Any) -> Optional[Dict[str, Any]]:
        """Check if field access is authorized."""
        try:
            # Find matching access rule
            matching_rule = None
            for rule in self.access_rules:
                if re.search(rule.field_pattern, access_attempt.field_path, re.IGNORECASE):
                    matching_rule = rule
                    break

            if not matching_rule:
                # No specific rule found, apply default public access
                access_attempt.authorized = True
                access_attempt.sensitivity = FieldSensitivity.PUBLIC
                return None

            # Update access attempt with rule information
            access_attempt.sensitivity = matching_rule.sensitivity

            # Check role authorization
            if access_attempt.user_role not in matching_rule.allowed_roles:
                return {
                    "type": "unauthorized_role",
                    "severity": "high",
                    "risk_score": 0.8,
                    "details": {
                        "required_roles": [role.value for role in matching_rule.allowed_roles],
                        "user_role": access_attempt.user_role.value,
                        "field_sensitivity": matching_rule.sensitivity.value,
                        "rule_description": matching_rule.description
                    }
                }

            # Check access type authorization
            if access_attempt.access_type == "read" and not matching_rule.read_allowed:
                return {
                    "type": "unauthorized_read",
                    "severity": "high",
                    "risk_score": 0.9,
                    "details": {
                        "field_sensitivity": matching_rule.sensitivity.value,
                        "rule_description": matching_rule.description
                    }
                }

            if access_attempt.access_type == "write" and not matching_rule.write_allowed:
                return {
                    "type": "unauthorized_write",
                    "severity": "critical",
                    "risk_score": 0.95,
                    "details": {
                        "field_sensitivity": matching_rule.sensitivity.value,
                        "rule_description": matching_rule.description
                    }
                }

            # Check additional conditions
            if matching_rule.conditions:
                condition_violation = await self._check_access_conditions(
                    access_attempt, matching_rule.conditions, field_value
                )
                if condition_violation:
                    return condition_violation

            # Check for suspicious patterns
            pattern_violation = await self._check_suspicious_patterns(access_attempt)
            if pattern_violation:
                return pattern_violation

            # Check for anomalous behavior
            anomaly_violation = await self._check_access_anomalies(access_attempt)
            if anomaly_violation:
                return anomaly_violation

            # Access is authorized
            access_attempt.authorized = True
            return None

        except Exception as e:
            logger.error(f"Failed to check field authorization: {e}")
            return {
                "type": "authorization_check_error",
                "severity": "medium",
                "risk_score": 0.5,
                "details": {"error": str(e)}
            }

    async def _check_access_conditions(self, access_attempt: AccessAttempt,
                                     conditions: Dict[str, Any], field_value: Any) -> Optional[Dict[str, Any]]:
        """Check additional access conditions."""
        try:
            # Owner-only access condition
            if conditions.get("owner_only", False):
                if not access_attempt.user_id:
                    return {
                        "type": "owner_only_violation",
                        "severity": "high",
                        "risk_score": 0.85,
                        "details": {
                            "condition": "owner_only",
                            "user_authenticated": access_attempt.user_id is not None
                        }
                    }

            # Context required condition
            if conditions.get("context_required", False):
                if not access_attempt.context:
                    return {
                        "type": "context_required_violation",
                        "severity": "medium",
                        "risk_score": 0.6,
                        "details": {
                            "condition": "context_required",
                            "context_provided": bool(access_attempt.context)
                        }
                    }

            return None

        except Exception as e:
            logger.error(f"Failed to check access conditions: {e}")
            return None

    async def _check_suspicious_patterns(self, access_attempt: AccessAttempt) -> Optional[Dict[str, Any]]:
        """Check for suspicious access patterns."""
        try:
            field_path = access_attempt.field_path.lower()

            for pattern_type, patterns in self.suspicious_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, field_path, re.IGNORECASE):
                        return {
                            "type": f"suspicious_pattern_{pattern_type}",
                            "severity": "medium",
                            "risk_score": 0.7,
                            "details": {
                                "pattern_type": pattern_type,
                                "matched_pattern": pattern,
                                "field_path": access_attempt.field_path
                            }
                        }

            return None

        except Exception as e:
            logger.error(f"Failed to check suspicious patterns: {e}")
            return None

    async def _check_access_anomalies(self, access_attempt: AccessAttempt) -> Optional[Dict[str, Any]]:
        """Check for anomalous access patterns using ML-based detection."""
        try:
            if not access_attempt.user_id:
                return None  # Skip anomaly detection for anonymous users

            # Get user's historical access patterns
            user_patterns = self.user_patterns.get(access_attempt.user_id, {})

            # Check if we have enough baseline data
            total_accesses = user_patterns.get("total_accesses", 0)
            if total_accesses < self.min_samples_for_baseline:
                return None  # Not enough data for anomaly detection

            # Calculate anomaly score
            anomaly_score = await self._calculate_anomaly_score(access_attempt, user_patterns)

            if anomaly_score > self.anomaly_threshold:
                return {
                    "type": "anomalous_access_pattern",
                    "severity": "medium" if anomaly_score < 0.9 else "high",
                    "risk_score": anomaly_score,
                    "details": {
                        "anomaly_score": anomaly_score,
                        "threshold": self.anomaly_threshold,
                        "user_baseline_accesses": total_accesses,
                        "field_path": access_attempt.field_path
                    }
                }

            return None

        except Exception as e:
            logger.error(f"Failed to check access anomalies: {e}")
            return None

    async def _calculate_anomaly_score(self, access_attempt: AccessAttempt,
                                     user_patterns: Dict[str, Any]) -> float:
        """Calculate anomaly score for access attempt."""
        try:
            anomaly_factors = []

            # Factor 1: Field access frequency
            field_accesses = user_patterns.get("field_accesses", {})
            field_frequency = field_accesses.get(access_attempt.field_path, 0)
            total_accesses = user_patterns.get("total_accesses", 1)
            field_probability = field_frequency / total_accesses

            # Lower probability = higher anomaly
            if field_probability == 0:
                anomaly_factors.append(1.0)  # Never accessed before
            else:
                anomaly_factors.append(1.0 - field_probability)

            # Factor 2: Time-based anomaly
            current_hour = access_attempt.timestamp.hour
            hour_accesses = user_patterns.get("hour_distribution", {})
            hour_frequency = hour_accesses.get(str(current_hour), 0)
            hour_probability = hour_frequency / total_accesses if total_accesses > 0 else 0

            if hour_probability < 0.1:  # Less than 10% of accesses at this hour
                anomaly_factors.append(0.3)

            # Factor 3: Endpoint access pattern
            endpoint_accesses = user_patterns.get("endpoint_accesses", {})
            endpoint_frequency = endpoint_accesses.get(access_attempt.endpoint, 0)
            endpoint_probability = endpoint_frequency / total_accesses if total_accesses > 0 else 0

            if endpoint_probability == 0:
                anomaly_factors.append(0.5)  # New endpoint

            # Factor 4: Access type pattern
            access_type_accesses = user_patterns.get("access_type_distribution", {})
            access_type_frequency = access_type_accesses.get(access_attempt.access_type, 0)
            access_type_probability = access_type_frequency / total_accesses if total_accesses > 0 else 0

            if access_type_probability < 0.2:  # Less than 20% of accesses of this type
                anomaly_factors.append(0.2)

            # Calculate weighted anomaly score
            if not anomaly_factors:
                return 0.0

            # Use maximum anomaly factor as primary score
            base_score = max(anomaly_factors)

            # Add bonus for multiple anomaly factors
            if len(anomaly_factors) > 1:
                base_score += 0.1 * (len(anomaly_factors) - 1)

            return min(1.0, base_score)

        except Exception as e:
            logger.error(f"Failed to calculate anomaly score: {e}")
            return 0.0

    async def _update_user_patterns(self, access_attempt: AccessAttempt):
        """Update user access patterns for anomaly detection."""
        try:
            if not access_attempt.user_id:
                return

            user_id = access_attempt.user_id
            patterns = self.user_patterns[user_id]

            # Update total access count
            patterns["total_accesses"] = patterns.get("total_accesses", 0) + 1

            # Update field access patterns
            field_accesses = patterns.setdefault("field_accesses", {})
            field_accesses[access_attempt.field_path] = field_accesses.get(access_attempt.field_path, 0) + 1

            # Update hour distribution
            hour_dist = patterns.setdefault("hour_distribution", {})
            hour_key = str(access_attempt.timestamp.hour)
            hour_dist[hour_key] = hour_dist.get(hour_key, 0) + 1

            # Update endpoint access patterns
            endpoint_accesses = patterns.setdefault("endpoint_accesses", {})
            endpoint_accesses[access_attempt.endpoint] = endpoint_accesses.get(access_attempt.endpoint, 0) + 1

            # Update access type distribution
            access_type_dist = patterns.setdefault("access_type_distribution", {})
            access_type_dist[access_attempt.access_type] = access_type_dist.get(access_attempt.access_type, 0) + 1

            # Update last access time
            patterns["last_access"] = access_attempt.timestamp

            # Cleanup old patterns (keep only recent data)
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=self.learning_window_hours)
            patterns["last_cleanup"] = patterns.get("last_cleanup", cutoff_time)

            if access_attempt.timestamp - patterns["last_cleanup"] > timedelta(hours=1):
                # Perform cleanup
                await self._cleanup_old_patterns(user_id, cutoff_time)
                patterns["last_cleanup"] = access_attempt.timestamp

        except Exception as e:
            logger.error(f"Failed to update user patterns: {e}")

    async def _cleanup_old_patterns(self, user_id: str, cutoff_time: datetime):
        """Clean up old pattern data to maintain performance."""
        try:
            # Remove old access attempts from history
            with self._lock:
                self.access_history = deque(
                    [attempt for attempt in self.access_history
                     if attempt.timestamp > cutoff_time],
                    maxlen=self.access_history.maxlen
                )

            # Note: In a production system, you might want to implement
            # more sophisticated pattern decay rather than hard cutoffs

        except Exception as e:
            logger.error(f"Failed to cleanup old patterns: {e}")

    def _generate_alert_id(self, access_attempt: AccessAttempt) -> str:
        """Generate unique alert ID."""
        content = f"{access_attempt.user_id}_{access_attempt.field_path}_{access_attempt.timestamp.isoformat()}"
        return hashlib.md5(content.encode()).hexdigest()[:16]

    async def _handle_bopla_alert(self, alert: BOPLAAlert):
        """Handle BOPLA security alert."""
        try:
            # Store alert
            with self._lock:
                self.alerts.append(alert)

                # Keep only recent alerts (last 24 hours)
                cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
                self.alerts = [a for a in self.alerts if a.timestamp > cutoff_time]

            # Log alert
            logger.warning("BOPLA violation detected",
                          alert_id=alert.alert_id,
                          user_id=alert.user_id,
                          user_role=alert.user_role.value,
                          violation_type=alert.violation_type,
                          field_path=alert.field_path,
                          endpoint=alert.endpoint,
                          severity=alert.severity,
                          risk_score=alert.risk_score)

            # Log to audit trail
            if self.audit_trail:
                risk_level = RiskLevel.HIGH if alert.severity in ["high", "critical"] else RiskLevel.MEDIUM
                self.audit_trail.log_event(
                    AuditEventType.SECURITY_VIOLATION,
                    alert.user_id,
                    "bopla_detector",
                    "field_access_violation",
                    {
                        "alert_id": alert.alert_id,
                        "violation_type": alert.violation_type,
                        "field_path": alert.field_path,
                        "endpoint": alert.endpoint,
                        "severity": alert.severity,
                        "risk_score": alert.risk_score,
                        "details": alert.details
                    },
                    risk_level=risk_level
                )

            # Update metrics
            increment_counter("bopla_alerts_generated", 1, {"severity": alert.severity})
            increment_counter("bopla_violations_by_type", 1, {"type": alert.violation_type})

            # Trigger automated response for critical alerts
            if alert.severity == "critical":
                await self._trigger_automated_response(alert)

        except Exception as e:
            logger.error(f"Failed to handle BOPLA alert: {e}")

    async def _trigger_automated_response(self, alert: BOPLAAlert):
        """Trigger automated response for critical BOPLA violations."""
        try:
            # For critical violations, we might want to:
            # 1. Temporarily block the user
            # 2. Require additional authentication
            # 3. Alert security team
            # 4. Log detailed forensic information

            logger.critical("Critical BOPLA violation - automated response triggered",
                           alert_id=alert.alert_id,
                           user_id=alert.user_id,
                           field_path=alert.field_path)

            # In a production system, implement actual response mechanisms here
            increment_counter("bopla_automated_responses", 1)

        except Exception as e:
            logger.error(f"Failed to trigger automated response: {e}")

    def generate_bopla_report(self) -> Dict[str, Any]:
        """Generate comprehensive BOPLA security report."""
        try:
            with self._lock:
                current_time = datetime.now(timezone.utc)

                # Basic statistics
                total_alerts = len(self.alerts)
                total_access_attempts = len(self.access_history)

                # Alert statistics by severity
                severity_stats = defaultdict(int)
                violation_type_stats = defaultdict(int)

                for alert in self.alerts:
                    severity_stats[alert.severity] += 1
                    violation_type_stats[alert.violation_type] += 1

                # User statistics
                user_stats = defaultdict(lambda: {"alerts": 0, "accesses": 0})
                for alert in self.alerts:
                    if alert.user_id:
                        user_stats[alert.user_id]["alerts"] += 1

                for attempt in self.access_history:
                    if attempt.user_id:
                        user_stats[attempt.user_id]["accesses"] += 1

                # Field access statistics
                field_stats = defaultdict(int)
                sensitive_field_stats = defaultdict(int)

                for attempt in self.access_history:
                    field_stats[attempt.field_path] += 1
                    if attempt.sensitivity in [FieldSensitivity.SENSITIVE,
                                             FieldSensitivity.CONFIDENTIAL,
                                             FieldSensitivity.RESTRICTED]:
                        sensitive_field_stats[attempt.field_path] += 1

                # Top violating users
                top_violators = sorted(
                    [(user_id, stats["alerts"]) for user_id, stats in user_stats.items()],
                    key=lambda x: x[1], reverse=True
                )[:10]

                # Most accessed sensitive fields
                top_sensitive_fields = sorted(
                    sensitive_field_stats.items(),
                    key=lambda x: x[1], reverse=True
                )[:10]

                return {
                    "report_generated_at": current_time.isoformat(),
                    "summary": {
                        "total_alerts_24h": total_alerts,
                        "total_access_attempts_24h": total_access_attempts,
                        "violation_rate": (total_alerts / total_access_attempts * 100) if total_access_attempts > 0 else 0,
                        "unique_users_monitored": len(user_stats),
                        "unique_fields_accessed": len(field_stats)
                    },
                    "alert_distribution": {
                        "by_severity": dict(severity_stats),
                        "by_violation_type": dict(violation_type_stats)
                    },
                    "top_violators": [{"user_id": user_id, "alert_count": count} for user_id, count in top_violators],
                    "sensitive_field_access": [{"field_path": field, "access_count": count} for field, count in top_sensitive_fields],
                    "security_recommendations": self._generate_security_recommendations(severity_stats, violation_type_stats)
                }

        except Exception as e:
            logger.error(f"Failed to generate BOPLA report: {e}")
            return {"error": str(e)}

    def _generate_security_recommendations(self, severity_stats: Dict[str, int],
                                         violation_type_stats: Dict[str, int]) -> List[str]:
        """Generate security recommendations based on BOPLA analysis."""
        recommendations = []

        try:
            # Check for critical violations
            if severity_stats.get("critical", 0) > 0:
                recommendations.append("URGENT: Investigate critical BOPLA violations immediately")

            # Check for high violation rates
            total_violations = sum(severity_stats.values())
            if total_violations > 100:  # Threshold for high violation rate
                recommendations.append("High BOPLA violation rate detected - review access control policies")

            # Check for specific violation types
            if violation_type_stats.get("unauthorized_role", 0) > 10:
                recommendations.append("Multiple role-based access violations - review user role assignments")

            if violation_type_stats.get("unauthorized_read", 0) > 5:
                recommendations.append("Unauthorized read attempts detected - strengthen field-level read controls")

            if violation_type_stats.get("unauthorized_write", 0) > 0:
                recommendations.append("CRITICAL: Unauthorized write attempts detected - immediate investigation required")

            if "suspicious_pattern" in str(violation_type_stats):
                recommendations.append("Suspicious access patterns detected - implement additional monitoring")

            if "anomalous_access_pattern" in str(violation_type_stats):
                recommendations.append("Anomalous user behavior detected - consider implementing adaptive authentication")

            if not recommendations:
                recommendations.append("BOPLA security posture is good - continue monitoring")

        except Exception as e:
            logger.error(f"Failed to generate security recommendations: {e}")
            recommendations.append("Unable to generate recommendations due to analysis error")

        return recommendations

    def get_user_access_summary(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get access summary for a specific user."""
        try:
            if user_id not in self.user_patterns:
                return None

            patterns = self.user_patterns[user_id]

            # Get recent alerts for this user
            user_alerts = [alert for alert in self.alerts if alert.user_id == user_id]

            # Get recent access attempts
            user_accesses = [attempt for attempt in self.access_history if attempt.user_id == user_id]

            return {
                "user_id": user_id,
                "total_accesses": patterns.get("total_accesses", 0),
                "alerts_24h": len(user_alerts),
                "last_access": patterns.get("last_access").isoformat() if patterns.get("last_access") else None,
                "most_accessed_fields": sorted(
                    patterns.get("field_accesses", {}).items(),
                    key=lambda x: x[1], reverse=True
                )[:10],
                "access_hour_distribution": patterns.get("hour_distribution", {}),
                "recent_alerts": [
                    {
                        "alert_id": alert.alert_id,
                        "violation_type": alert.violation_type,
                        "field_path": alert.field_path,
                        "severity": alert.severity,
                        "timestamp": alert.timestamp.isoformat()
                    }
                    for alert in user_alerts[-10:]  # Last 10 alerts
                ]
            }

        except Exception as e:
            logger.error(f"Failed to get user access summary for {user_id}: {e}")
            return None


# Global instance
bopla_detector = BOPLADetector()
