'use client';

import * as React from 'react';
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useWebSocket } from '@/hooks/useWebSocket';
import { formatCurrency, formatPercentage, formatTimeAgo, getChangeColor, getChangeIcon } from '@/lib/utils';
import { 
  TrendingUp, 
  TrendingDown, 
  Eye, 
  ExternalLink,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';
import type { Token, TokenAnalysis } from '@/lib/types';

interface RecentDiscovery extends Token {
  analysis: TokenAnalysis;
  priceChange24h: number;
  volume24h: number;
  marketCap: number;
  discoveredAt: string;
}

// Mock data - replace with actual API calls
const mockDiscoveries: RecentDiscovery[] = [
  {
    address: '******************************************',
    symbol: 'ALPHA',
    name: 'Alpha Protocol',
    chain: 'ethereum',
    chainId: 1,
    decimals: 18,
    verified: true,
    createdAt: '2025-01-10T10:00:00Z',
    updatedAt: '2025-01-10T12:00:00Z',
    discoveredAt: '2025-01-10T11:30:00Z',
    priceChange24h: 15.7,
    volume24h: 2500000,
    marketCap: 45000000,
    analysis: {
      id: 'analysis-1',
      tokenAddress: '******************************************',
      chainId: 1,
      overallScore: 85.2,
      riskScore: 25.3,
      alphaScore: 78.9,
      confidenceLevel: 92.1,
      investmentRecommendation: 'BUY',
      executionTimeMs: 1250,
      analyzedAt: '2025-01-10T11:35:00Z',
    },
  },
  {
    address: '******************************************',
    symbol: 'BETA',
    name: 'Beta Finance',
    chain: 'ethereum',
    chainId: 1,
    decimals: 18,
    verified: false,
    createdAt: '2025-01-10T09:30:00Z',
    updatedAt: '2025-01-10T12:00:00Z',
    discoveredAt: '2025-01-10T11:15:00Z',
    priceChange24h: -8.3,
    volume24h: 850000,
    marketCap: 12000000,
    analysis: {
      id: 'analysis-2',
      tokenAddress: '******************************************',
      chainId: 1,
      overallScore: 62.8,
      riskScore: 45.7,
      alphaScore: 55.2,
      confidenceLevel: 78.4,
      investmentRecommendation: 'HOLD',
      executionTimeMs: 980,
      analyzedAt: '2025-01-10T11:20:00Z',
    },
  },
];

export function TokenDiscovery() {
  const [refreshing, setRefreshing] = useState(false);

  // WebSocket connection for real-time updates
  const { lastMessage, isConnected } = useWebSocket({
    url: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000/ws',
    onMessage: (data) => {
      if (data.type === 'token_discovery') {
        // Handle real-time token discovery updates
        console.log('New token discovered:', data);
      }
    },
  });

  const { data: discoveries, isLoading, error, refetch } = useQuery({
    queryKey: ['token-discoveries'],
    queryFn: async () => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800));
      return mockDiscoveries;
    },
    refetchInterval: 60000, // Refetch every minute
  });

  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const getRiskBadgeColor = (riskScore: number) => {
    if (riskScore < 30) return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    if (riskScore < 60) return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
    return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
  };

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'BUY': return 'text-green-600 dark:text-green-400';
      case 'HOLD': return 'text-yellow-600 dark:text-yellow-400';
      case 'SELL': return 'text-red-600 dark:text-red-400';
      case 'AVOID': return 'text-red-700 dark:text-red-500';
      default: return 'text-muted-foreground';
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Eye className="h-5 w-5" />
              <span>Recent Token Discoveries</span>
            </CardTitle>
            <p className="text-sm text-muted-foreground mt-1">
              AI-powered token analysis and discovery
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1 text-xs">
              <div className={`h-2 w-2 rounded-full ${
                isConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'
              }`} />
              <span className="text-muted-foreground">
                {isConnected ? 'Live' : 'Offline'}
              </span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
                <LoadingSpinner size="sm" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-muted rounded animate-pulse" />
                  <div className="h-3 bg-muted rounded animate-pulse w-2/3" />
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="flex items-center justify-center p-8 text-muted-foreground">
            <AlertTriangle className="h-5 w-5 mr-2" />
            <span>Failed to load token discoveries</span>
          </div>
        ) : !discoveries?.length ? (
          <div className="flex items-center justify-center p-8 text-muted-foreground">
            <Clock className="h-5 w-5 mr-2" />
            <span>No recent discoveries</span>
          </div>
        ) : (
          <div className="space-y-4">
            {discoveries.map((token) => (
              <div
                key={token.address}
                className="flex items-center space-x-4 p-4 border rounded-lg hover:bg-accent/50 transition-colors"
              >
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="font-semibold">{token.symbol}</div>
                    <div className="text-sm text-muted-foreground truncate">
                      {token.name}
                    </div>
                    {token.verified && (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-4 text-sm">
                    <div className="flex items-center space-x-1">
                      <span className="text-muted-foreground">Score:</span>
                      <span className="font-medium">
                        {token.analysis.overallScore.toFixed(1)}
                      </span>
                    </div>
                    
                    <Badge className={getRiskBadgeColor(token.analysis.riskScore)}>
                      Risk: {token.analysis.riskScore.toFixed(1)}
                    </Badge>
                    
                    <div className={`font-medium ${getRecommendationColor(token.analysis.investmentRecommendation)}`}>
                      {token.analysis.investmentRecommendation}
                    </div>
                  </div>
                </div>

                <div className="text-right">
                  <div className="flex items-center space-x-1 mb-1">
                    <span className={getChangeColor(token.priceChange24h)}>
                      {getChangeIcon(token.priceChange24h)}
                      {formatPercentage(Math.abs(token.priceChange24h))}
                    </span>
                  </div>
                  
                  <div className="text-xs text-muted-foreground">
                    {formatTimeAgo(token.discoveredAt)}
                  </div>
                </div>

                <Button variant="ghost" size="sm">
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
