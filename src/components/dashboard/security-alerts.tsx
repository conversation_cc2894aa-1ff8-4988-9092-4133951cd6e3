'use client';

import * as React from 'react';
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useWebSocket } from '@/hooks/useWebSocket';
import { formatTimeAgo } from '@/lib/utils';
import { 
  Shield, 
  AlertTriangle, 
  AlertCircle, 
  Info, 
  CheckCircle,
  X,
  Eye,
  Bot,
  Lock,
  Activity
} from 'lucide-react';
import type { SystemAlert } from '@/lib/types';

// Mock data - replace with actual API calls
const mockAlerts: SystemAlert[] = [
  {
    id: 'alert-1',
    type: 'WARNING',
    title: 'High Risk Token Detected',
    message: 'Token 0x1234...5678 shows suspicious trading patterns with 89% risk score',
    timestamp: '2025-01-10T11:45:00Z',
    resolved: false,
    metadata: {
      tokenAddress: '0x1234567890123456789012345678901234567890',
      riskScore: 89.2,
      category: 'token_analysis',
    },
  },
  {
    id: 'alert-2',
    type: 'CRITICAL',
    title: 'BOPLA Vulnerability Detected',
    message: 'Potential broken object-level authorization detected in API endpoint /api/tokens/{id}',
    timestamp: '2025-01-10T11:30:00Z',
    resolved: false,
    metadata: {
      endpoint: '/api/tokens/{id}',
      severity: 'high',
      category: 'security',
    },
  },
  {
    id: 'alert-3',
    type: 'INFO',
    title: 'AI Bot Detection Update',
    message: 'Successfully blocked 23 bot requests in the last hour',
    timestamp: '2025-01-10T11:15:00Z',
    resolved: true,
    metadata: {
      blockedRequests: 23,
      category: 'bot_detection',
    },
  },
  {
    id: 'alert-4',
    type: 'ERROR',
    title: 'API Rate Limit Exceeded',
    message: 'Client ************* exceeded rate limit (1000 req/min)',
    timestamp: '2025-01-10T11:00:00Z',
    resolved: true,
    metadata: {
      clientIp: '*************',
      requestCount: 1247,
      category: 'rate_limiting',
    },
  },
];

export function SecurityAlerts() {
  const [alerts, setAlerts] = useState<SystemAlert[]>([]);

  // WebSocket connection for real-time security alerts
  const { lastMessage, isConnected } = useWebSocket({
    url: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000/ws',
    onMessage: (data) => {
      if (data.type === 'security_alert') {
        setAlerts(prev => [data.alert, ...prev.slice(0, 9)]); // Keep last 10 alerts
      }
    },
  });

  const { data: fetchedAlerts, isLoading, error } = useQuery({
    queryKey: ['security-alerts'],
    queryFn: async () => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      return mockAlerts;
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Combine fetched alerts with real-time alerts
  const allAlerts = [...alerts, ...(fetchedAlerts || [])];
  const uniqueAlerts = allAlerts.filter((alert, index, self) => 
    index === self.findIndex(a => a.id === alert.id)
  ).slice(0, 10);

  const getAlertIcon = (type: SystemAlert['type']) => {
    switch (type) {
      case 'CRITICAL': return AlertCircle;
      case 'ERROR': return AlertTriangle;
      case 'WARNING': return AlertTriangle;
      case 'INFO': return Info;
      default: return Info;
    }
  };

  const getAlertColor = (type: SystemAlert['type']) => {
    switch (type) {
      case 'CRITICAL': return 'text-red-600 bg-red-50 border-red-200 dark:bg-red-950 dark:border-red-800';
      case 'ERROR': return 'text-red-600 bg-red-50 border-red-200 dark:bg-red-950 dark:border-red-800';
      case 'WARNING': return 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:bg-yellow-950 dark:border-yellow-800';
      case 'INFO': return 'text-blue-600 bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800';
      default: return 'text-gray-600 bg-gray-50 border-gray-200 dark:bg-gray-950 dark:border-gray-800';
    }
  };

  const getBadgeVariant = (type: SystemAlert['type']) => {
    switch (type) {
      case 'CRITICAL': return 'destructive';
      case 'ERROR': return 'destructive';
      case 'WARNING': return 'secondary';
      case 'INFO': return 'outline';
      default: return 'outline';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'token_analysis': return Eye;
      case 'security': return Lock;
      case 'bot_detection': return Bot;
      case 'rate_limiting': return Activity;
      default: return Shield;
    }
  };

  const resolveAlert = (alertId: string) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, resolved: true } : alert
    ));
  };

  const dismissAlert = (alertId: string) => {
    setAlerts(prev => prev.filter(alert => alert.id !== alertId));
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Security Alerts</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              {uniqueAlerts.filter(a => !a.resolved).length} active
            </Badge>
            <div className={`h-2 w-2 rounded-full ${
              isConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'
            }`} />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-3">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-3 p-3 border rounded-lg">
                <LoadingSpinner size="sm" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-muted rounded animate-pulse" />
                  <div className="h-3 bg-muted rounded animate-pulse w-2/3" />
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="text-center text-muted-foreground py-8">
            <AlertTriangle className="h-5 w-5 mx-auto mb-2" />
            <span>Failed to load security alerts</span>
          </div>
        ) : !uniqueAlerts.length ? (
          <div className="text-center text-muted-foreground py-8">
            <CheckCircle className="h-5 w-5 mx-auto mb-2 text-green-600" />
            <span>No security alerts</span>
            <p className="text-xs mt-1">All systems secure</p>
          </div>
        ) : (
          <div className="space-y-3">
            {uniqueAlerts.map((alert) => {
              const AlertIcon = getAlertIcon(alert.type);
              const CategoryIcon = getCategoryIcon(alert.metadata?.category || '');
              
              return (
                <div
                  key={alert.id}
                  className={`p-3 border rounded-lg transition-colors ${
                    alert.resolved ? 'opacity-60' : ''
                  } ${getAlertColor(alert.type)}`}
                >
                  <div className="flex items-start space-x-3">
                    <AlertIcon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-medium text-sm">{alert.title}</h4>
                        <Badge variant={getBadgeVariant(alert.type)} className="text-xs">
                          {alert.type}
                        </Badge>
                        {alert.resolved && (
                          <Badge variant="outline" className="text-xs">
                            Resolved
                          </Badge>
                        )}
                      </div>
                      
                      <p className="text-sm opacity-90 mb-2">{alert.message}</p>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2 text-xs opacity-75">
                          <CategoryIcon className="h-3 w-3" />
                          <span>{formatTimeAgo(alert.timestamp)}</span>
                        </div>
                        
                        {!alert.resolved && (
                          <div className="flex items-center space-x-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 px-2 text-xs"
                              onClick={() => resolveAlert(alert.id)}
                            >
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Resolve
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 px-2 text-xs"
                              onClick={() => dismissAlert(alert.id)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
