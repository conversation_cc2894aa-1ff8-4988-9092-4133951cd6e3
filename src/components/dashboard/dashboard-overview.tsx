'use client';

import * as React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { formatCurrency, formatCompactNumber, formatPercentage } from '@/lib/utils';
import { 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  Shield, 
  Zap,
  Eye,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

interface OverviewMetrics {
  totalTokensAnalyzed: number;
  tokensAnalyzedToday: number;
  averageRiskScore: number;
  highRiskTokens: number;
  systemUptime: number;
  apiResponseTime: number;
  accuracyRate: number;
  activeAlerts: number;
}

// Mock data - replace with actual API call
const mockMetrics: OverviewMetrics = {
  totalTokensAnalyzed: 15420,
  tokensAnalyzedToday: 342,
  averageRiskScore: 67.8,
  highRiskTokens: 23,
  systemUptime: 99.97,
  apiResponseTime: 0.101,
  accuracyRate: 98.1,
  activeAlerts: 2,
};

export function DashboardOverview() {
  const { data: metrics, isLoading, error } = useQuery({
    queryKey: ['dashboard-overview'],
    queryFn: async () => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      return mockMetrics;
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 8 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <LoadingSpinner />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error || !metrics) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            <span>Failed to load dashboard metrics</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const overviewCards = [
    {
      title: 'Tokens Analyzed',
      value: formatCompactNumber(metrics.totalTokensAnalyzed),
      change: `+${metrics.tokensAnalyzedToday} today`,
      icon: Eye,
      trend: 'up' as const,
      color: 'text-blue-600',
    },
    {
      title: 'Average Risk Score',
      value: metrics.averageRiskScore.toFixed(1),
      change: 'Moderate risk level',
      icon: Shield,
      trend: 'neutral' as const,
      color: 'text-yellow-600',
    },
    {
      title: 'High Risk Tokens',
      value: metrics.highRiskTokens.toString(),
      change: 'Requires attention',
      icon: AlertTriangle,
      trend: 'down' as const,
      color: 'text-red-600',
    },
    {
      title: 'System Uptime',
      value: `${metrics.systemUptime}%`,
      change: 'Excellent reliability',
      icon: CheckCircle,
      trend: 'up' as const,
      color: 'text-green-600',
    },
    {
      title: 'API Response Time',
      value: `${metrics.apiResponseTime}s`,
      change: 'Sub-second performance',
      icon: Zap,
      trend: 'up' as const,
      color: 'text-purple-600',
    },
    {
      title: 'Accuracy Rate',
      value: `${metrics.accuracyRate}%`,
      change: 'Exceeds target (95%)',
      icon: TrendingUp,
      trend: 'up' as const,
      color: 'text-green-600',
    },
    {
      title: 'Active Alerts',
      value: metrics.activeAlerts.toString(),
      change: 'Low alert volume',
      icon: Activity,
      trend: metrics.activeAlerts > 5 ? 'up' : 'down',
      color: metrics.activeAlerts > 5 ? 'text-red-600' : 'text-green-600',
    },
    {
      title: 'System Status',
      value: 'Operational',
      change: 'All systems green',
      icon: CheckCircle,
      trend: 'up' as const,
      color: 'text-green-600',
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {overviewCards.map((card, index) => {
        const Icon = card.icon;
        const TrendIcon = card.trend === 'up' ? TrendingUp : 
                          card.trend === 'down' ? TrendingDown : Activity;
        
        return (
          <Card key={index} className="relative overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {card.title}
              </CardTitle>
              <Icon className={`h-4 w-4 ${card.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value}</div>
              <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                <TrendIcon className={`h-3 w-3 ${
                  card.trend === 'up' ? 'text-green-600' : 
                  card.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                }`} />
                <span>{card.change}</span>
              </div>
            </CardContent>
            
            {/* Subtle gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-transparent to-black/5 pointer-events-none" />
          </Card>
        );
      })}
    </div>
  );
}
