'use client';

import * as React from 'react';
import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useWebSocket } from '@/hooks/useWebSocket';
import { formatNumber } from '@/lib/utils';
import { 
  Activity, 
  Cpu, 
  HardDrive, 
  Wifi, 
  Database,
  Clock,
  Zap,
  TrendingUp
} from 'lucide-react';

interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
    temperature: number;
  };
  memory: {
    used: number;
    total: number;
    usage: number;
  };
  disk: {
    used: number;
    total: number;
    usage: number;
  };
  network: {
    inbound: number;
    outbound: number;
    latency: number;
  };
  database: {
    connections: number;
    maxConnections: number;
    queryTime: number;
  };
  api: {
    requestsPerSecond: number;
    averageResponseTime: number;
    errorRate: number;
  };
  uptime: number;
  lastUpdated: string;
}

// Mock data - replace with actual API calls
const mockMetrics: SystemMetrics = {
  cpu: {
    usage: 45.2,
    cores: 8,
    temperature: 62,
  },
  memory: {
    used: 12.8,
    total: 32,
    usage: 40,
  },
  disk: {
    used: 256,
    total: 1024,
    usage: 25,
  },
  network: {
    inbound: 125.6,
    outbound: 89.3,
    latency: 12,
  },
  database: {
    connections: 45,
    maxConnections: 100,
    queryTime: 0.023,
  },
  api: {
    requestsPerSecond: 342,
    averageResponseTime: 0.089,
    errorRate: 0.12,
  },
  uptime: 99.97,
  lastUpdated: new Date().toISOString(),
};

export function SystemMonitoring() {
  const [realTimeMetrics, setRealTimeMetrics] = useState<SystemMetrics | null>(null);

  // WebSocket connection for real-time system metrics
  const { lastMessage, isConnected } = useWebSocket({
    url: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000/ws',
    onMessage: (data) => {
      if (data.type === 'system_metrics') {
        setRealTimeMetrics(data.metrics);
      }
    },
  });

  const { data: metrics, isLoading, error } = useQuery({
    queryKey: ['system-metrics'],
    queryFn: async () => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 600));
      return mockMetrics;
    },
    refetchInterval: 5000, // Refetch every 5 seconds
  });

  // Use real-time data if available, otherwise fall back to API data
  const currentMetrics = realTimeMetrics || metrics;

  const getUsageColor = (usage: number) => {
    if (usage < 50) return 'text-green-600';
    if (usage < 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getProgressColor = (usage: number) => {
    if (usage < 50) return 'bg-green-500';
    if (usage < 80) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  if (isLoading && !realTimeMetrics) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>System Monitoring</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <LoadingSpinner />
        </CardContent>
      </Card>
    );
  }

  if (error && !realTimeMetrics) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>System Monitoring</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground">
            Failed to load system metrics
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!currentMetrics) return null;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>System Monitoring</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <div className={`h-2 w-2 rounded-full ${
              isConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'
            }`} />
            <span className="text-xs text-muted-foreground">
              {isConnected ? 'Real-time' : 'Cached'}
            </span>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* CPU Usage */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Cpu className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium">CPU Usage</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className={`text-sm font-medium ${getUsageColor(currentMetrics.cpu.usage)}`}>
                {currentMetrics.cpu.usage.toFixed(1)}%
              </span>
              <Badge variant="outline" className="text-xs">
                {currentMetrics.cpu.cores} cores
              </Badge>
            </div>
          </div>
          <Progress 
            value={currentMetrics.cpu.usage} 
            className="h-2"
            style={{
              '--progress-background': getProgressColor(currentMetrics.cpu.usage)
            } as React.CSSProperties}
          />
        </div>

        {/* Memory Usage */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <HardDrive className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-medium">Memory</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className={`text-sm font-medium ${getUsageColor(currentMetrics.memory.usage)}`}>
                {currentMetrics.memory.used.toFixed(1)}GB / {currentMetrics.memory.total}GB
              </span>
            </div>
          </div>
          <Progress 
            value={currentMetrics.memory.usage} 
            className="h-2"
            style={{
              '--progress-background': getProgressColor(currentMetrics.memory.usage)
            } as React.CSSProperties}
          />
        </div>

        {/* Disk Usage */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Database className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium">Disk Space</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className={`text-sm font-medium ${getUsageColor(currentMetrics.disk.usage)}`}>
                {currentMetrics.disk.used}GB / {currentMetrics.disk.total}GB
              </span>
            </div>
          </div>
          <Progress 
            value={currentMetrics.disk.usage} 
            className="h-2"
            style={{
              '--progress-background': getProgressColor(currentMetrics.disk.usage)
            } as React.CSSProperties}
          />
        </div>

        {/* Network & Performance Metrics */}
        <div className="grid grid-cols-2 gap-4 pt-4 border-t">
          <div className="space-y-1">
            <div className="flex items-center space-x-1">
              <Wifi className="h-3 w-3 text-blue-500" />
              <span className="text-xs text-muted-foreground">Network</span>
            </div>
            <div className="text-sm">
              <div>↓ {formatNumber(currentMetrics.network.inbound)} MB/s</div>
              <div>↑ {formatNumber(currentMetrics.network.outbound)} MB/s</div>
            </div>
          </div>

          <div className="space-y-1">
            <div className="flex items-center space-x-1">
              <Zap className="h-3 w-3 text-yellow-500" />
              <span className="text-xs text-muted-foreground">API Performance</span>
            </div>
            <div className="text-sm">
              <div>{formatNumber(currentMetrics.api.requestsPerSecond)} req/s</div>
              <div>{currentMetrics.api.averageResponseTime.toFixed(3)}s avg</div>
            </div>
          </div>

          <div className="space-y-1">
            <div className="flex items-center space-x-1">
              <Database className="h-3 w-3 text-purple-500" />
              <span className="text-xs text-muted-foreground">Database</span>
            </div>
            <div className="text-sm">
              <div>{currentMetrics.database.connections}/{currentMetrics.database.maxConnections} conn</div>
              <div>{currentMetrics.database.queryTime.toFixed(3)}s query</div>
            </div>
          </div>

          <div className="space-y-1">
            <div className="flex items-center space-x-1">
              <Clock className="h-3 w-3 text-green-500" />
              <span className="text-xs text-muted-foreground">Uptime</span>
            </div>
            <div className="text-sm">
              <div className="font-medium text-green-600">
                {currentMetrics.uptime.toFixed(2)}%
              </div>
              <div className="text-xs text-muted-foreground">
                Excellent
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
