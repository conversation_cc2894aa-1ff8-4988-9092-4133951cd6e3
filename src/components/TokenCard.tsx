import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Token } from '@/types';
import { cn } from '@/lib/utils';
import { TrendingUp, TrendingDown, Activity, Users, Zap } from 'lucide-react';

interface TokenCardProps {
  token: Token;
  onClick?: () => void;
  compact?: boolean;
}

export const TokenCard: React.FC<TokenCardProps> = ({ token, onClick, compact = false }) => {
  const isPositive = token.change24h > 0;
  
  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'bg-emerald-500/20 text-emerald-300';
      case 'medium': return 'bg-amber-500/20 text-amber-300';
      case 'high': return 'bg-red-500/20 text-red-300';
      default: return 'bg-gray-500/20 text-gray-300';
    }
  };

  const getChainColor = (chain: string) => {
    switch (chain) {
      case 'ethereum': return 'bg-blue-500/20 text-blue-300';
      case 'solana': return 'bg-purple-500/20 text-purple-300';
      case 'cosmos': return 'bg-pink-500/20 text-pink-300';
      default: return 'bg-gray-500/20 text-gray-300';
    }
  };

  if (compact) {
    return (
      <Card 
        className="cursor-pointer hover:bg-accent/50 transition-colors"
        onClick={onClick}
      >
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-2xl">{token.logo}</span>
              <div>
                <div className="font-semibold">{token.symbol}</div>
                <div className="text-sm text-muted-foreground">{token.name}</div>
              </div>
            </div>
            <div className="text-right">
              <div className="font-mono font-semibold">
                ${token.price.toLocaleString()}
              </div>
              <div className={cn(
                'text-sm font-medium flex items-center',
                isPositive ? 'text-emerald-400' : 'text-red-400'
              )}>
                {isPositive ? <TrendingUp className="w-3 h-3 mr-1" /> : <TrendingDown className="w-3 h-3 mr-1" />}
                {isPositive ? '+' : ''}{token.change24h.toFixed(2)}%
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card 
      className="cursor-pointer hover:bg-accent/50 transition-all hover:scale-[1.02] group"
      onClick={onClick}
    >
      <CardContent className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <span className="text-3xl">{token.logo}</span>
            <div>
              <div className="font-bold text-lg">{token.symbol}</div>
              <div className="text-sm text-muted-foreground">{token.name}</div>
              <div className="flex items-center space-x-2 mt-1">
                <Badge className={getChainColor(token.chain)} variant="secondary">
                  {token.chain}
                </Badge>
                <Badge className={getRiskColor(token.riskLevel)} variant="secondary">
                  {token.riskLevel} risk
                </Badge>
              </div>
            </div>
          </div>
          
          <div className="text-right">
            <div className="font-mono font-bold text-xl">
              ${token.price.toLocaleString()}
            </div>
            <div className={cn(
              'text-sm font-medium flex items-center justify-end',
              isPositive ? 'text-emerald-400' : 'text-red-400'
            )}>
              {isPositive ? <TrendingUp className="w-4 h-4 mr-1" /> : <TrendingDown className="w-4 h-4 mr-1" />}
              {isPositive ? '+' : ''}{token.change24h.toFixed(2)}%
            </div>
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">AI Score</span>
            <div className="flex items-center space-x-2">
              <div className="w-20 h-2 bg-secondary rounded-full overflow-hidden">
                <div 
                  className={cn(
                    "h-full transition-all",
                    token.aiScore >= 70 ? "bg-emerald-500" : 
                    token.aiScore >= 40 ? "bg-amber-500" : "bg-red-500"
                  )}
                  style={{ width: `${token.aiScore}%` }}
                />
              </div>
              <span className="font-mono text-sm font-medium">{token.aiScore}/100</span>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4 text-xs">
            <div className="flex items-center space-x-1">
              <Activity className="w-3 h-3 text-muted-foreground" />
              <span className="text-muted-foreground">Vol:</span>
              <span className="font-mono">${(token.volume24h / 1000000).toFixed(1)}M</span>
            </div>
            <div className="flex items-center space-x-1">
              <Users className="w-3 h-3 text-muted-foreground" />
              <span className="text-muted-foreground">Holders:</span>
              <span className="font-mono">{(token.metrics.holders / 1000).toFixed(0)}K</span>
            </div>
            <div className="flex items-center space-x-1">
              <Zap className="w-3 h-3 text-muted-foreground" />
              <span className="text-muted-foreground">Txns:</span>
              <span className="font-mono">{(token.metrics.transactions24h / 1000).toFixed(0)}K</span>
            </div>
          </div>

          <div className="pt-2 border-t border-border">
            <div className="text-xs text-muted-foreground mb-2">Sentiment</div>
            <div className="flex space-x-1 h-2 rounded-full overflow-hidden">
              <div 
                className="bg-emerald-500"
                style={{ width: `${token.sentiment.positive * 100}%` }}
              />
              <div 
                className="bg-red-500"
                style={{ width: `${token.sentiment.negative * 100}%` }}
              />
              <div 
                className="bg-gray-500"
                style={{ width: `${token.sentiment.neutral * 100}%` }}
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};