'use client';

import * as React from 'react';
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { formatNumber, formatTimeAgo } from '@/lib/utils';
import { 
  Zap, 
  TrendingUp, 
  Settings, 
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  Play,
  Pause,
  BarChart3,
  Target,
  Clock,
  Activity
} from 'lucide-react';

interface OptimizationRecommendation {
  id: string;
  category: 'PERFORMANCE' | 'COST' | 'RELIABILITY' | 'SECURITY';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  title: string;
  description: string;
  impact: {
    performance: number;
    cost: number;
    effort: number;
  };
  estimatedSavings: {
    responseTime: number;
    cost: number;
    resources: number;
  };
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'DISMISSED';
  implementationTime: number;
  lastUpdated: string;
  metrics: {
    before: Record<string, number>;
    after?: Record<string, number>;
  };
}

interface OptimizationMetrics {
  overallOptimizationScore: number;
  activeOptimizations: number;
  completedOptimizations: number;
  totalSavings: {
    responseTime: number;
    cost: number;
    resources: number;
  };
  recommendations: OptimizationRecommendation[];
  autoOptimization: {
    enabled: boolean;
    rulesActive: number;
    actionsExecuted: number;
  };
}

// Mock data - replace with actual API calls
const mockOptimizationMetrics: OptimizationMetrics = {
  overallOptimizationScore: 87.3,
  activeOptimizations: 3,
  completedOptimizations: 12,
  totalSavings: {
    responseTime: 23.4,
    cost: 15.7,
    resources: 18.9,
  },
  autoOptimization: {
    enabled: true,
    rulesActive: 8,
    actionsExecuted: 156,
  },
  recommendations: [
    {
      id: 'opt-1',
      category: 'PERFORMANCE',
      priority: 'HIGH',
      title: 'Enable Database Query Caching',
      description: 'Implement Redis caching for frequently accessed database queries to reduce response times',
      impact: {
        performance: 85,
        cost: 20,
        effort: 40,
      },
      estimatedSavings: {
        responseTime: 35.2,
        cost: 12.5,
        resources: 25.8,
      },
      status: 'PENDING',
      implementationTime: 4,
      lastUpdated: '2025-01-10T10:30:00Z',
      metrics: {
        before: {
          avgResponseTime: 0.234,
          cacheHitRate: 0,
          dbQueries: 1250,
        },
      },
    },
    {
      id: 'opt-2',
      category: 'COST',
      priority: 'MEDIUM',
      title: 'Optimize Container Resource Allocation',
      description: 'Right-size container resources based on actual usage patterns to reduce infrastructure costs',
      impact: {
        performance: 15,
        cost: 75,
        effort: 30,
      },
      estimatedSavings: {
        responseTime: 5.1,
        cost: 28.3,
        resources: 42.7,
      },
      status: 'IN_PROGRESS',
      implementationTime: 2,
      lastUpdated: '2025-01-10T09:15:00Z',
      metrics: {
        before: {
          cpuUtilization: 45.2,
          memoryUtilization: 62.8,
          cost: 1250,
        },
      },
    },
    {
      id: 'opt-3',
      category: 'RELIABILITY',
      priority: 'HIGH',
      title: 'Implement Circuit Breaker Pattern',
      description: 'Add circuit breakers to prevent cascade failures and improve system resilience',
      impact: {
        performance: 60,
        cost: 10,
        effort: 65,
      },
      estimatedSavings: {
        responseTime: 15.7,
        cost: 5.2,
        resources: 12.3,
      },
      status: 'PENDING',
      implementationTime: 8,
      lastUpdated: '2025-01-10T08:45:00Z',
      metrics: {
        before: {
          errorRate: 0.15,
          timeouts: 23,
          availability: 99.85,
        },
      },
    },
    {
      id: 'opt-4',
      category: 'PERFORMANCE',
      priority: 'CRITICAL',
      title: 'Upgrade ML Model Inference Engine',
      description: 'Migrate to optimized inference engine for 40% faster model predictions',
      impact: {
        performance: 95,
        cost: 35,
        effort: 80,
      },
      estimatedSavings: {
        responseTime: 45.8,
        cost: 8.9,
        resources: 22.1,
      },
      status: 'COMPLETED',
      implementationTime: 12,
      lastUpdated: '2025-01-08T14:20:00Z',
      metrics: {
        before: {
          inferenceTime: 0.124,
          throughput: 450,
          accuracy: 92.6,
        },
        after: {
          inferenceTime: 0.074,
          throughput: 680,
          accuracy: 93.1,
        },
      },
    },
  ],
};

export function PerformanceOptimization() {
  const [refreshing, setRefreshing] = useState(false);
  const [selectedRecommendation, setSelectedRecommendation] = useState<string | null>(null);

  const { data: metrics, isLoading, error, refetch } = useQuery({
    queryKey: ['performance-optimization'],
    queryFn: async () => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 700));
      return mockOptimizationMetrics;
    },
    refetchInterval: 60000, // Refetch every minute
  });

  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'PERFORMANCE': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'COST': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'RELIABILITY': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      case 'SECURITY': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'LOW': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'HIGH': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      case 'CRITICAL': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      case 'IN_PROGRESS': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'COMPLETED': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'DISMISSED': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING': return Clock;
      case 'IN_PROGRESS': return Activity;
      case 'COMPLETED': return CheckCircle;
      case 'DISMISSED': return AlertTriangle;
      default: return Clock;
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="h-5 w-5" />
            <span>Performance Optimization</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <LoadingSpinner />
        </CardContent>
      </Card>
    );
  }

  if (error || !metrics) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="h-5 w-5" />
            <span>Performance Optimization</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground">
            Failed to load optimization metrics
          </div>
        </CardContent>
      </Card>
    );
  }

  const pendingRecommendations = metrics.recommendations.filter(r => r.status === 'PENDING');
  const inProgressRecommendations = metrics.recommendations.filter(r => r.status === 'IN_PROGRESS');

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Zap className="h-5 w-5" />
            <span>Performance Optimization</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Badge variant="outline">
              {metrics.activeOptimizations} active
            </Badge>
            <Button variant="outline" size="sm" onClick={handleRefresh} disabled={refreshing}>
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Optimization Score */}
        <div className="p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-blue-600" />
              <span className="font-medium">Optimization Score</span>
            </div>
            <div className="text-2xl font-bold text-blue-600">
              {metrics.overallOptimizationScore.toFixed(1)}%
            </div>
          </div>
          <Progress value={metrics.overallOptimizationScore} className="h-2" />
          <div className="text-xs text-blue-700 dark:text-blue-400 mt-2">
            System is well-optimized with room for improvement
          </div>
        </div>

        {/* Savings Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 border rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              {metrics.totalSavings.responseTime.toFixed(1)}%
            </div>
            <div className="text-sm text-muted-foreground">Response Time Saved</div>
          </div>
          <div className="text-center p-4 border rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {metrics.totalSavings.cost.toFixed(1)}%
            </div>
            <div className="text-sm text-muted-foreground">Cost Reduction</div>
          </div>
          <div className="text-center p-4 border rounded-lg">
            <div className="text-2xl font-bold text-purple-600">
              {metrics.totalSavings.resources.toFixed(1)}%
            </div>
            <div className="text-sm text-muted-foreground">Resource Efficiency</div>
          </div>
        </div>

        {/* Auto-Optimization Status */}
        <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className={`h-3 w-3 rounded-full ${metrics.autoOptimization.enabled ? 'bg-green-500 animate-pulse' : 'bg-gray-500'}`} />
            <div>
              <div className="font-medium text-sm">Auto-Optimization</div>
              <div className="text-xs text-muted-foreground">
                {metrics.autoOptimization.rulesActive} rules active • {metrics.autoOptimization.actionsExecuted} actions executed
              </div>
            </div>
          </div>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4" />
          </Button>
        </div>

        {/* Recommendations */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">Optimization Recommendations</h4>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <span>{pendingRecommendations.length} pending</span>
              <span>•</span>
              <span>{inProgressRecommendations.length} in progress</span>
            </div>
          </div>
          
          {metrics.recommendations.map((recommendation) => {
            const StatusIcon = getStatusIcon(recommendation.status);
            
            return (
              <div
                key={recommendation.id}
                className="p-4 border rounded-lg hover:bg-accent/50 transition-colors cursor-pointer"
                onClick={() => setSelectedRecommendation(selectedRecommendation === recommendation.id ? null : recommendation.id)}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <StatusIcon className={`h-4 w-4 ${
                      recommendation.status === 'COMPLETED' ? 'text-green-600' :
                      recommendation.status === 'IN_PROGRESS' ? 'text-blue-600' :
                      recommendation.status === 'DISMISSED' ? 'text-red-600' : 'text-gray-600'
                    }`} />
                    <div>
                      <div className="font-medium text-sm">{recommendation.title}</div>
                      <div className="text-xs text-muted-foreground">{recommendation.description}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={getCategoryColor(recommendation.category)}>
                      {recommendation.category}
                    </Badge>
                    <Badge className={getPriorityColor(recommendation.priority)}>
                      {recommendation.priority}
                    </Badge>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
                  <div>
                    <div className="text-muted-foreground">Performance Impact</div>
                    <div className="font-medium">{recommendation.impact.performance}%</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Cost Impact</div>
                    <div className="font-medium">{recommendation.impact.cost}%</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Implementation</div>
                    <div className="font-medium">{recommendation.implementationTime}h</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Status</div>
                    <Badge className={getStatusColor(recommendation.status)}>
                      {recommendation.status}
                    </Badge>
                  </div>
                </div>
                
                {selectedRecommendation === recommendation.id && (
                  <div className="mt-4 pt-4 border-t space-y-4">
                    {/* Impact Analysis */}
                    <div>
                      <div className="text-xs font-medium mb-2">Impact Analysis</div>
                      <div className="grid grid-cols-3 gap-4">
                        <div>
                          <div className="text-xs text-muted-foreground mb-1">Performance</div>
                          <Progress value={recommendation.impact.performance} className="h-1" />
                          <div className="text-xs mt-1">{recommendation.impact.performance}%</div>
                        </div>
                        <div>
                          <div className="text-xs text-muted-foreground mb-1">Cost</div>
                          <Progress value={recommendation.impact.cost} className="h-1" />
                          <div className="text-xs mt-1">{recommendation.impact.cost}%</div>
                        </div>
                        <div>
                          <div className="text-xs text-muted-foreground mb-1">Effort</div>
                          <Progress value={recommendation.impact.effort} className="h-1" />
                          <div className="text-xs mt-1">{recommendation.impact.effort}%</div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Estimated Savings */}
                    <div>
                      <div className="text-xs font-medium mb-2">Estimated Savings</div>
                      <div className="grid grid-cols-3 gap-4 text-xs">
                        <div>
                          <div className="text-muted-foreground">Response Time</div>
                          <div className="font-medium text-green-600">
                            -{recommendation.estimatedSavings.responseTime.toFixed(1)}%
                          </div>
                        </div>
                        <div>
                          <div className="text-muted-foreground">Cost</div>
                          <div className="font-medium text-green-600">
                            -{recommendation.estimatedSavings.cost.toFixed(1)}%
                          </div>
                        </div>
                        <div>
                          <div className="text-muted-foreground">Resources</div>
                          <div className="font-medium text-green-600">
                            -{recommendation.estimatedSavings.resources.toFixed(1)}%
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Metrics Comparison */}
                    {recommendation.metrics.after && (
                      <div>
                        <div className="text-xs font-medium mb-2">Before vs After</div>
                        <div className="grid grid-cols-2 gap-4 text-xs">
                          <div>
                            <div className="text-muted-foreground mb-1">Before</div>
                            {Object.entries(recommendation.metrics.before).map(([key, value]) => (
                              <div key={key} className="flex justify-between">
                                <span>{key}:</span>
                                <span>{typeof value === 'number' ? value.toFixed(3) : value}</span>
                              </div>
                            ))}
                          </div>
                          <div>
                            <div className="text-muted-foreground mb-1">After</div>
                            {Object.entries(recommendation.metrics.after).map(([key, value]) => (
                              <div key={key} className="flex justify-between">
                                <span>{key}:</span>
                                <span className="text-green-600">{typeof value === 'number' ? value.toFixed(3) : value}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {/* Actions */}
                    <div className="flex items-center justify-between pt-2">
                      <div className="text-xs text-muted-foreground">
                        Updated {formatTimeAgo(recommendation.lastUpdated)}
                      </div>
                      {recommendation.status === 'PENDING' && (
                        <div className="flex items-center space-x-2">
                          <Button variant="outline" size="sm">
                            <Play className="h-3 w-3 mr-1" />
                            Implement
                          </Button>
                          <Button variant="outline" size="sm">
                            <BarChart3 className="h-3 w-3 mr-1" />
                            Analyze
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
