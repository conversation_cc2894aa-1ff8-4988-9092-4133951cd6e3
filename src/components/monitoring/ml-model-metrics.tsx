'use client';

import * as React from 'react';
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useRealTimeData } from '@/hooks/useRealTimeData';
import { formatNumber, formatTimeAgo } from '@/lib/utils';
import { 
  Cpu, 
  Brain, 
  Target, 
  Zap,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Settings,
  Activity,
  BarChart3,
  Clock
} from 'lucide-react';

interface MlModel {
  id: string;
  name: string;
  version: string;
  type: 'CLASSIFICATION' | 'REGRESSION' | 'CLUSTERING' | 'DEEP_LEARNING';
  status: 'ACTIVE' | 'TRAINING' | 'INACTIVE' | 'ERROR';
  accuracy: number;
  precision: number;
  recall: number;
  f1Score: number;
  latency: number;
  throughput: number;
  memoryUsage: number;
  lastTrained: string;
  trainingDuration: number;
  datasetSize: number;
  predictions24h: number;
  errorRate: number;
  driftScore: number;
  confidenceThreshold: number;
}

interface ModelPerformanceMetrics {
  models: MlModel[];
  overallAccuracy: number;
  totalPredictions: number;
  averageLatency: number;
  systemLoad: number;
  trainingJobs: {
    active: number;
    queued: number;
    completed: number;
    failed: number;
  };
}

// Mock data - replace with actual API calls
const mockModelMetrics: ModelPerformanceMetrics = {
  overallAccuracy: 94.7,
  totalPredictions: 156420,
  averageLatency: 0.045,
  systemLoad: 67.3,
  trainingJobs: {
    active: 2,
    queued: 1,
    completed: 47,
    failed: 3,
  },
  models: [
    {
      id: 'model-1',
      name: 'Token Risk Classifier',
      version: 'v2.1.3',
      type: 'CLASSIFICATION',
      status: 'ACTIVE',
      accuracy: 98.1,
      precision: 97.8,
      recall: 98.4,
      f1Score: 98.1,
      latency: 0.032,
      throughput: 1250,
      memoryUsage: 2.4,
      lastTrained: '2025-01-08T10:00:00Z',
      trainingDuration: 3600,
      datasetSize: 250000,
      predictions24h: 45620,
      errorRate: 0.08,
      driftScore: 0.12,
      confidenceThreshold: 0.85,
    },
    {
      id: 'model-2',
      name: 'Price Prediction Model',
      version: 'v1.8.2',
      type: 'REGRESSION',
      status: 'ACTIVE',
      accuracy: 89.3,
      precision: 88.7,
      recall: 90.1,
      f1Score: 89.4,
      latency: 0.058,
      throughput: 890,
      memoryUsage: 3.2,
      lastTrained: '2025-01-09T14:30:00Z',
      trainingDuration: 7200,
      datasetSize: 180000,
      predictions24h: 32140,
      errorRate: 0.15,
      driftScore: 0.08,
      confidenceThreshold: 0.80,
    },
    {
      id: 'model-3',
      name: 'Sentiment Analyzer',
      version: 'v3.0.1',
      type: 'DEEP_LEARNING',
      status: 'TRAINING',
      accuracy: 92.6,
      precision: 91.9,
      recall: 93.2,
      f1Score: 92.5,
      latency: 0.124,
      throughput: 450,
      memoryUsage: 5.8,
      lastTrained: '2025-01-10T08:00:00Z',
      trainingDuration: 14400,
      datasetSize: 500000,
      predictions24h: 18340,
      errorRate: 0.22,
      driftScore: 0.05,
      confidenceThreshold: 0.75,
    },
    {
      id: 'model-4',
      name: 'Anomaly Detection',
      version: 'v1.5.0',
      type: 'CLUSTERING',
      status: 'ACTIVE',
      accuracy: 95.4,
      precision: 94.8,
      recall: 96.1,
      f1Score: 95.4,
      latency: 0.089,
      throughput: 680,
      memoryUsage: 1.9,
      lastTrained: '2025-01-07T16:45:00Z',
      trainingDuration: 5400,
      datasetSize: 320000,
      predictions24h: 28920,
      errorRate: 0.11,
      driftScore: 0.18,
      confidenceThreshold: 0.90,
    },
  ],
};

export function MlModelMetrics() {
  const [refreshing, setRefreshing] = useState(false);
  const [selectedModel, setSelectedModel] = useState<string | null>(null);

  const { data: metrics, isLoading, error, refetch } = useQuery({
    queryKey: ['ml-model-metrics'],
    queryFn: async () => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 900));
      return mockModelMetrics;
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'TRAINING': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'INACTIVE': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      case 'ERROR': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'CLASSIFICATION': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'REGRESSION': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      case 'CLUSTERING': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      case 'DEEP_LEARNING': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getDriftColor = (score: number) => {
    if (score < 0.1) return 'text-green-600';
    if (score < 0.2) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getAccuracyColor = (accuracy: number) => {
    if (accuracy >= 95) return 'text-green-600';
    if (accuracy >= 90) return 'text-blue-600';
    if (accuracy >= 85) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Brain className="h-5 w-5" />
            <span>ML Model Performance</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <LoadingSpinner />
        </CardContent>
      </Card>
    );
  }

  if (error || !metrics) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Brain className="h-5 w-5" />
            <span>ML Model Performance</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground">
            Failed to load ML model metrics
          </div>
        </CardContent>
      </Card>
    );
  }

  const activeModels = metrics.models.filter(m => m.status === 'ACTIVE').length;
  const trainingModels = metrics.models.filter(m => m.status === 'TRAINING').length;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Brain className="h-5 w-5" />
            <span>ML Model Performance</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Badge variant="outline">
              {activeModels} active
            </Badge>
            <Button variant="outline" size="sm" onClick={handleRefresh} disabled={refreshing}>
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Overall Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {metrics.overallAccuracy.toFixed(1)}%
            </div>
            <div className="text-xs text-muted-foreground">Overall Accuracy</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {formatNumber(metrics.totalPredictions)}
            </div>
            <div className="text-xs text-muted-foreground">Predictions (24h)</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {metrics.averageLatency.toFixed(3)}s
            </div>
            <div className="text-xs text-muted-foreground">Avg Latency</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {metrics.systemLoad.toFixed(1)}%
            </div>
            <div className="text-xs text-muted-foreground">System Load</div>
          </div>
        </div>

        {/* Training Jobs Status */}
        <div className="p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <Activity className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-sm">Training Jobs</span>
            </div>
          </div>
          <div className="grid grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-lg font-bold text-blue-600">{metrics.trainingJobs.active}</div>
              <div className="text-xs text-muted-foreground">Active</div>
            </div>
            <div>
              <div className="text-lg font-bold text-yellow-600">{metrics.trainingJobs.queued}</div>
              <div className="text-xs text-muted-foreground">Queued</div>
            </div>
            <div>
              <div className="text-lg font-bold text-green-600">{metrics.trainingJobs.completed}</div>
              <div className="text-xs text-muted-foreground">Completed</div>
            </div>
            <div>
              <div className="text-lg font-bold text-red-600">{metrics.trainingJobs.failed}</div>
              <div className="text-xs text-muted-foreground">Failed</div>
            </div>
          </div>
        </div>

        {/* Model List */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">Active Models</h4>
          {metrics.models.map((model) => (
            <div
              key={model.id}
              className="p-4 border rounded-lg hover:bg-accent/50 transition-colors cursor-pointer"
              onClick={() => setSelectedModel(selectedModel === model.id ? null : model.id)}
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <Cpu className="h-4 w-4 text-blue-600" />
                  <div>
                    <div className="font-medium text-sm">{model.name}</div>
                    <div className="text-xs text-muted-foreground">v{model.version}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge className={getTypeColor(model.type)}>
                    {model.type}
                  </Badge>
                  <Badge className={getStatusColor(model.status)}>
                    {model.status}
                  </Badge>
                </div>
              </div>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
                <div>
                  <div className="text-muted-foreground">Accuracy</div>
                  <div className={`font-medium ${getAccuracyColor(model.accuracy)}`}>
                    {model.accuracy.toFixed(1)}%
                  </div>
                </div>
                <div>
                  <div className="text-muted-foreground">Latency</div>
                  <div className="font-medium">{model.latency.toFixed(3)}s</div>
                </div>
                <div>
                  <div className="text-muted-foreground">Predictions</div>
                  <div className="font-medium">{formatNumber(model.predictions24h)}</div>
                </div>
                <div>
                  <div className="text-muted-foreground">Drift Score</div>
                  <div className={`font-medium ${getDriftColor(model.driftScore)}`}>
                    {model.driftScore.toFixed(2)}
                  </div>
                </div>
              </div>
              
              {selectedModel === model.id && (
                <div className="mt-4 pt-4 border-t space-y-4">
                  {/* Detailed Metrics */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <div>
                      <div className="text-xs text-muted-foreground mb-1">Precision</div>
                      <div className="text-sm font-medium">{model.precision.toFixed(1)}%</div>
                      <Progress value={model.precision} className="h-1 mt-1" />
                    </div>
                    <div>
                      <div className="text-xs text-muted-foreground mb-1">Recall</div>
                      <div className="text-sm font-medium">{model.recall.toFixed(1)}%</div>
                      <Progress value={model.recall} className="h-1 mt-1" />
                    </div>
                    <div>
                      <div className="text-xs text-muted-foreground mb-1">F1 Score</div>
                      <div className="text-sm font-medium">{model.f1Score.toFixed(1)}%</div>
                      <Progress value={model.f1Score} className="h-1 mt-1" />
                    </div>
                  </div>
                  
                  {/* Performance Metrics */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
                    <div>
                      <div className="text-muted-foreground">Throughput</div>
                      <div className="font-medium">{formatNumber(model.throughput)} req/s</div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Memory Usage</div>
                      <div className="font-medium">{model.memoryUsage.toFixed(1)} GB</div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Error Rate</div>
                      <div className={`font-medium ${model.errorRate < 0.1 ? 'text-green-600' : 'text-red-600'}`}>
                        {model.errorRate.toFixed(2)}%
                      </div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Dataset Size</div>
                      <div className="font-medium">{formatNumber(model.datasetSize)}</div>
                    </div>
                  </div>
                  
                  {/* Training Info */}
                  <div className="bg-gray-50 dark:bg-gray-950/20 p-3 rounded border">
                    <div className="text-xs font-medium mb-2">Training Information</div>
                    <div className="grid grid-cols-2 gap-4 text-xs">
                      <div>
                        <div className="text-muted-foreground">Last Trained</div>
                        <div className="font-medium">{formatTimeAgo(model.lastTrained)}</div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Training Duration</div>
                        <div className="font-medium">{Math.floor(model.trainingDuration / 3600)}h {Math.floor((model.trainingDuration % 3600) / 60)}m</div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Actions */}
                  <div className="flex items-center justify-between pt-2">
                    <div className="text-xs text-muted-foreground">
                      Confidence Threshold: {(model.confidenceThreshold * 100).toFixed(0)}%
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm">
                        <Settings className="h-3 w-3 mr-1" />
                        Configure
                      </Button>
                      <Button variant="outline" size="sm">
                        <BarChart3 className="h-3 w-3 mr-1" />
                        Details
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
