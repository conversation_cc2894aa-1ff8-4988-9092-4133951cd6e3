'use client';

import * as React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useRealTimeData } from '@/hooks/useRealTimeData';
import { formatNumber } from '@/lib/utils';
import { 
  Activity, 
  Zap, 
  TrendingUp, 
  TrendingDown,
  Cpu,
  Database,
  Globe,
  BarChart3,
  Clock,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';

interface PerformanceMetrics {
  overallPerformanceScore: number;
  systemHealth: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR';
  apiResponseTime: {
    average: number;
    p95: number;
    p99: number;
    trend: number;
  };
  throughput: {
    requestsPerSecond: number;
    trend: number;
  };
  errorRate: {
    percentage: number;
    trend: number;
  };
  mlModelPerformance: {
    accuracy: number;
    latency: number;
    throughput: number;
    trend: number;
  };
  resourceUtilization: {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
  };
  uptime: {
    percentage: number;
    duration: number;
  };
  optimization: {
    cacheHitRate: number;
    compressionRatio: number;
    cdnOffload: number;
  };
}

// Mock data - replace with actual API calls
const mockPerformanceMetrics: PerformanceMetrics = {
  overallPerformanceScore: 92.4,
  systemHealth: 'EXCELLENT',
  apiResponseTime: {
    average: 0.089,
    p95: 0.234,
    p99: 0.567,
    trend: -12.3,
  },
  throughput: {
    requestsPerSecond: 2847,
    trend: 15.7,
  },
  errorRate: {
    percentage: 0.12,
    trend: -23.4,
  },
  mlModelPerformance: {
    accuracy: 98.1,
    latency: 0.045,
    throughput: 1250,
    trend: 2.3,
  },
  resourceUtilization: {
    cpu: 45.2,
    memory: 62.8,
    disk: 34.1,
    network: 28.9,
  },
  uptime: {
    percentage: 99.97,
    duration: 2592000, // 30 days in seconds
  },
  optimization: {
    cacheHitRate: 94.7,
    compressionRatio: 78.3,
    cdnOffload: 85.2,
  },
};

export function PerformanceOverview() {
  const { systemMetrics } = useRealTimeData();

  const { data: metrics, isLoading, error } = useQuery({
    queryKey: ['performance-overview'],
    queryFn: async () => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800));
      return mockPerformanceMetrics;
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 8 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <LoadingSpinner />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error || !metrics) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            <span>Failed to load performance metrics</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'EXCELLENT': return 'text-green-600 bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800';
      case 'GOOD': return 'text-blue-600 bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800';
      case 'FAIR': return 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:bg-yellow-950 dark:border-yellow-800';
      case 'POOR': return 'text-red-600 bg-red-50 border-red-200 dark:bg-red-950 dark:border-red-800';
      default: return 'text-gray-600 bg-gray-50 border-gray-200 dark:bg-gray-950 dark:border-gray-800';
    }
  };

  const getTrendColor = (trend: number) => {
    if (trend > 0) return 'text-green-600';
    if (trend < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getTrendIcon = (trend: number) => {
    if (trend > 0) return TrendingUp;
    if (trend < 0) return TrendingDown;
    return Activity;
  };

  const formatDuration = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    return `${days}d ${hours}h`;
  };

  const performanceCards = [
    {
      title: 'Performance Score',
      value: `${metrics.overallPerformanceScore.toFixed(1)}%`,
      change: 'Excellent performance',
      icon: BarChart3,
      color: 'text-green-600',
      progress: metrics.overallPerformanceScore,
    },
    {
      title: 'System Health',
      value: metrics.systemHealth,
      change: 'All systems operational',
      icon: CheckCircle,
      color: getHealthColor(metrics.systemHealth).split(' ')[0],
      badge: true,
    },
    {
      title: 'API Response Time',
      value: `${metrics.apiResponseTime.average.toFixed(3)}s`,
      change: `${Math.abs(metrics.apiResponseTime.trend).toFixed(1)}% ${metrics.apiResponseTime.trend > 0 ? 'slower' : 'faster'}`,
      icon: Zap,
      color: 'text-blue-600',
      trend: metrics.apiResponseTime.trend,
    },
    {
      title: 'Throughput',
      value: `${formatNumber(metrics.throughput.requestsPerSecond)} req/s`,
      change: `${Math.abs(metrics.throughput.trend).toFixed(1)}% ${metrics.throughput.trend > 0 ? 'increase' : 'decrease'}`,
      icon: Activity,
      color: 'text-purple-600',
      trend: metrics.throughput.trend,
    },
    {
      title: 'Error Rate',
      value: `${metrics.errorRate.percentage.toFixed(2)}%`,
      change: `${Math.abs(metrics.errorRate.trend).toFixed(1)}% ${metrics.errorRate.trend > 0 ? 'increase' : 'decrease'}`,
      icon: AlertTriangle,
      color: metrics.errorRate.percentage < 1 ? 'text-green-600' : 'text-red-600',
      trend: metrics.errorRate.trend,
    },
    {
      title: 'ML Model Accuracy',
      value: `${metrics.mlModelPerformance.accuracy.toFixed(1)}%`,
      change: `${Math.abs(metrics.mlModelPerformance.trend).toFixed(1)}% ${metrics.mlModelPerformance.trend > 0 ? 'improvement' : 'decline'}`,
      icon: Cpu,
      color: 'text-indigo-600',
      trend: metrics.mlModelPerformance.trend,
      progress: metrics.mlModelPerformance.accuracy,
    },
    {
      title: 'Cache Hit Rate',
      value: `${metrics.optimization.cacheHitRate.toFixed(1)}%`,
      change: 'Excellent caching',
      icon: Database,
      color: 'text-green-600',
      progress: metrics.optimization.cacheHitRate,
    },
    {
      title: 'System Uptime',
      value: `${metrics.uptime.percentage.toFixed(2)}%`,
      change: formatDuration(metrics.uptime.duration),
      icon: Clock,
      color: 'text-green-600',
      progress: metrics.uptime.percentage,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Main Performance Status */}
      <Card className="border-2">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-6 w-6 text-green-600" />
            <span>System Performance</span>
            <Badge className={getHealthColor(metrics.systemHealth)}>
              {metrics.systemHealth}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">Overall Score</div>
              <div className="text-3xl font-bold text-green-600">
                {metrics.overallPerformanceScore.toFixed(1)}%
              </div>
              <Progress value={metrics.overallPerformanceScore} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">Response Time (Avg)</div>
              <div className="text-3xl font-bold text-blue-600">
                {metrics.apiResponseTime.average.toFixed(3)}s
              </div>
              <div className="text-xs text-muted-foreground">
                P95: {metrics.apiResponseTime.p95.toFixed(3)}s | P99: {metrics.apiResponseTime.p99.toFixed(3)}s
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">Throughput</div>
              <div className="text-3xl font-bold text-purple-600">
                {formatNumber(metrics.throughput.requestsPerSecond)}
              </div>
              <div className="text-xs text-muted-foreground">requests per second</div>
            </div>
            
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">Error Rate</div>
              <div className={`text-3xl font-bold ${metrics.errorRate.percentage < 1 ? 'text-green-600' : 'text-red-600'}`}>
                {metrics.errorRate.percentage.toFixed(2)}%
              </div>
              <div className="text-xs text-muted-foreground">
                {metrics.errorRate.percentage < 1 ? 'Excellent' : 'Needs attention'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance Metrics Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {performanceCards.map((card, index) => {
          const Icon = card.icon;
          const TrendIcon = card.trend !== undefined ? getTrendIcon(card.trend) : null;
          
          return (
            <Card key={index} className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {card.title}
                </CardTitle>
                <Icon className={`h-4 w-4 ${card.color}`} />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    {card.badge ? (
                      <Badge className={getHealthColor(card.value)}>
                        {card.value}
                      </Badge>
                    ) : (
                      <div className="text-2xl font-bold">{card.value}</div>
                    )}
                  </div>
                  
                  {card.progress !== undefined && (
                    <Progress value={card.progress} className="h-1" />
                  )}
                  
                  <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                    {TrendIcon && (
                      <TrendIcon className={`h-3 w-3 ${getTrendColor(card.trend!)}`} />
                    )}
                    <span>{card.change}</span>
                  </div>
                </div>
              </CardContent>
              
              {/* Subtle gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-transparent to-black/5 pointer-events-none" />
            </Card>
          );
        })}
      </div>

      {/* Resource Utilization Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Resource Utilization</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>CPU Usage</span>
                <span className="font-medium">{metrics.resourceUtilization.cpu.toFixed(1)}%</span>
              </div>
              <Progress value={metrics.resourceUtilization.cpu} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Memory Usage</span>
                <span className="font-medium">{metrics.resourceUtilization.memory.toFixed(1)}%</span>
              </div>
              <Progress value={metrics.resourceUtilization.memory} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Disk Usage</span>
                <span className="font-medium">{metrics.resourceUtilization.disk.toFixed(1)}%</span>
              </div>
              <Progress value={metrics.resourceUtilization.disk} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Network Usage</span>
                <span className="font-medium">{metrics.resourceUtilization.network.toFixed(1)}%</span>
              </div>
              <Progress value={metrics.resourceUtilization.network} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
