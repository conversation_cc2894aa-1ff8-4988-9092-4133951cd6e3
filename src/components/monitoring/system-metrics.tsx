'use client';

import * as React from 'react';
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useRealTimeData } from '@/hooks/useRealTimeData';
import { formatNumber, formatTimeAgo } from '@/lib/utils';
import { 
  Cpu, 
  HardDrive, 
  MemoryStick, 
  Network,
  Activity,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';

interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
    frequency: number;
    temperature: number;
    processes: number;
  };
  memory: {
    used: number;
    total: number;
    available: number;
    cached: number;
    buffers: number;
  };
  disk: {
    used: number;
    total: number;
    available: number;
    readSpeed: number;
    writeSpeed: number;
    iops: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
    packetsIn: number;
    packetsOut: number;
    connections: number;
    bandwidth: number;
  };
  processes: {
    total: number;
    running: number;
    sleeping: number;
    zombie: number;
  };
  uptime: number;
  loadAverage: [number, number, number];
  timestamp: string;
}

// Mock data - replace with actual API calls
const mockSystemMetrics: SystemMetrics = {
  cpu: {
    usage: 45.2,
    cores: 8,
    frequency: 3.2,
    temperature: 65,
    processes: 156,
  },
  memory: {
    used: 6.4,
    total: 16.0,
    available: 9.6,
    cached: 2.1,
    buffers: 0.8,
  },
  disk: {
    used: 120.5,
    total: 500.0,
    available: 379.5,
    readSpeed: 450.2,
    writeSpeed: 320.8,
    iops: 1250,
  },
  network: {
    bytesIn: 1024000000,
    bytesOut: 512000000,
    packetsIn: 125000,
    packetsOut: 89000,
    connections: 234,
    bandwidth: 1000,
  },
  processes: {
    total: 156,
    running: 12,
    sleeping: 142,
    zombie: 2,
  },
  uptime: 2592000, // 30 days
  loadAverage: [1.2, 1.5, 1.8],
  timestamp: new Date().toISOString(),
};

export function SystemMetrics() {
  const [refreshing, setRefreshing] = useState(false);
  const [timeRange, setTimeRange] = useState('1h');

  const { data: metrics, isLoading, error, refetch } = useQuery({
    queryKey: ['system-metrics', timeRange],
    queryFn: async () => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 600));
      return mockSystemMetrics;
    },
    refetchInterval: 5000, // Refetch every 5 seconds
  });

  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const formatBytes = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${days}d ${hours}h ${minutes}m`;
  };

  const getUsageColor = (percentage: number) => {
    if (percentage < 50) return 'text-green-600';
    if (percentage < 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getHealthStatus = (metrics: SystemMetrics) => {
    const cpuHigh = metrics.cpu.usage > 80;
    const memoryHigh = (metrics.memory.used / metrics.memory.total) * 100 > 85;
    const diskHigh = (metrics.disk.used / metrics.disk.total) * 100 > 90;
    
    if (cpuHigh || memoryHigh || diskHigh) return 'warning';
    if (metrics.cpu.usage > 60 || (metrics.memory.used / metrics.memory.total) * 100 > 70) return 'caution';
    return 'healthy';
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>System Metrics</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <LoadingSpinner />
        </CardContent>
      </Card>
    );
  }

  if (error || !metrics) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>System Metrics</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground">
            <AlertTriangle className="h-5 w-5 mx-auto mb-2" />
            <span>Failed to load system metrics</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const healthStatus = getHealthStatus(metrics);
  const memoryUsagePercent = (metrics.memory.used / metrics.memory.total) * 100;
  const diskUsagePercent = (metrics.disk.used / metrics.disk.total) * 100;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>System Metrics</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Badge variant={
              healthStatus === 'healthy' ? 'default' :
              healthStatus === 'caution' ? 'secondary' : 'destructive'
            }>
              {healthStatus === 'healthy' ? 'Healthy' :
               healthStatus === 'caution' ? 'Caution' : 'Warning'}
            </Badge>
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-2 py-1 text-xs border rounded bg-background"
            >
              <option value="5m">5 Minutes</option>
              <option value="1h">1 Hour</option>
              <option value="24h">24 Hours</option>
            </select>
            <Button variant="outline" size="sm" onClick={handleRefresh} disabled={refreshing}>
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* System Overview */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {formatUptime(metrics.uptime)}
            </div>
            <div className="text-xs text-muted-foreground">System Uptime</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {metrics.loadAverage[0].toFixed(2)}
            </div>
            <div className="text-xs text-muted-foreground">Load Average (1m)</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {metrics.processes.total}
            </div>
            <div className="text-xs text-muted-foreground">Total Processes</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {metrics.network.connections}
            </div>
            <div className="text-xs text-muted-foreground">Network Connections</div>
          </div>
        </div>

        {/* CPU Metrics */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Cpu className="h-4 w-4 text-blue-600" />
            <h4 className="font-medium text-sm">CPU Usage</h4>
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>CPU Usage</span>
              <span className={`font-medium ${getUsageColor(metrics.cpu.usage)}`}>
                {metrics.cpu.usage.toFixed(1)}%
              </span>
            </div>
            <Progress value={metrics.cpu.usage} className="h-2" />
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
              <div>
                <div className="text-muted-foreground">Cores</div>
                <div className="font-medium">{metrics.cpu.cores}</div>
              </div>
              <div>
                <div className="text-muted-foreground">Frequency</div>
                <div className="font-medium">{metrics.cpu.frequency.toFixed(1)} GHz</div>
              </div>
              <div>
                <div className="text-muted-foreground">Temperature</div>
                <div className="font-medium">{metrics.cpu.temperature}°C</div>
              </div>
              <div>
                <div className="text-muted-foreground">Processes</div>
                <div className="font-medium">{metrics.cpu.processes}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Memory Metrics */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <MemoryStick className="h-4 w-4 text-green-600" />
            <h4 className="font-medium text-sm">Memory Usage</h4>
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Memory Usage</span>
              <span className={`font-medium ${getUsageColor(memoryUsagePercent)}`}>
                {memoryUsagePercent.toFixed(1)}%
              </span>
            </div>
            <Progress value={memoryUsagePercent} className="h-2" />
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
              <div>
                <div className="text-muted-foreground">Used</div>
                <div className="font-medium">{metrics.memory.used.toFixed(1)} GB</div>
              </div>
              <div>
                <div className="text-muted-foreground">Available</div>
                <div className="font-medium">{metrics.memory.available.toFixed(1)} GB</div>
              </div>
              <div>
                <div className="text-muted-foreground">Cached</div>
                <div className="font-medium">{metrics.memory.cached.toFixed(1)} GB</div>
              </div>
              <div>
                <div className="text-muted-foreground">Total</div>
                <div className="font-medium">{metrics.memory.total.toFixed(1)} GB</div>
              </div>
            </div>
          </div>
        </div>

        {/* Disk Metrics */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <HardDrive className="h-4 w-4 text-purple-600" />
            <h4 className="font-medium text-sm">Disk Usage</h4>
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Disk Usage</span>
              <span className={`font-medium ${getUsageColor(diskUsagePercent)}`}>
                {diskUsagePercent.toFixed(1)}%
              </span>
            </div>
            <Progress value={diskUsagePercent} className="h-2" />
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
              <div>
                <div className="text-muted-foreground">Used</div>
                <div className="font-medium">{metrics.disk.used.toFixed(1)} GB</div>
              </div>
              <div>
                <div className="text-muted-foreground">Available</div>
                <div className="font-medium">{metrics.disk.available.toFixed(1)} GB</div>
              </div>
              <div>
                <div className="text-muted-foreground">Read Speed</div>
                <div className="font-medium">{metrics.disk.readSpeed.toFixed(1)} MB/s</div>
              </div>
              <div>
                <div className="text-muted-foreground">Write Speed</div>
                <div className="font-medium">{metrics.disk.writeSpeed.toFixed(1)} MB/s</div>
              </div>
            </div>
          </div>
        </div>

        {/* Network Metrics */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Network className="h-4 w-4 text-orange-600" />
            <h4 className="font-medium text-sm">Network Activity</h4>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
            <div>
              <div className="text-muted-foreground">Bytes In</div>
              <div className="font-medium">{formatBytes(metrics.network.bytesIn)}</div>
            </div>
            <div>
              <div className="text-muted-foreground">Bytes Out</div>
              <div className="font-medium">{formatBytes(metrics.network.bytesOut)}</div>
            </div>
            <div>
              <div className="text-muted-foreground">Packets In</div>
              <div className="font-medium">{formatNumber(metrics.network.packetsIn)}</div>
            </div>
            <div>
              <div className="text-muted-foreground">Packets Out</div>
              <div className="font-medium">{formatNumber(metrics.network.packetsOut)}</div>
            </div>
          </div>
        </div>

        {/* Process Information */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">Process Information</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
            <div>
              <div className="text-muted-foreground">Running</div>
              <div className="font-medium text-green-600">{metrics.processes.running}</div>
            </div>
            <div>
              <div className="text-muted-foreground">Sleeping</div>
              <div className="font-medium text-blue-600">{metrics.processes.sleeping}</div>
            </div>
            <div>
              <div className="text-muted-foreground">Zombie</div>
              <div className="font-medium text-red-600">{metrics.processes.zombie}</div>
            </div>
            <div>
              <div className="text-muted-foreground">Total</div>
              <div className="font-medium">{metrics.processes.total}</div>
            </div>
          </div>
        </div>

        {/* Load Average */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">Load Average</h4>
          <div className="grid grid-cols-3 gap-4 text-xs">
            <div className="text-center">
              <div className="text-lg font-bold">{metrics.loadAverage[0].toFixed(2)}</div>
              <div className="text-muted-foreground">1 minute</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold">{metrics.loadAverage[1].toFixed(2)}</div>
              <div className="text-muted-foreground">5 minutes</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold">{metrics.loadAverage[2].toFixed(2)}</div>
              <div className="text-muted-foreground">15 minutes</div>
            </div>
          </div>
        </div>

        {/* Last Updated */}
        <div className="text-xs text-muted-foreground text-center pt-2 border-t">
          Last updated: {formatTimeAgo(metrics.timestamp)}
        </div>
      </CardContent>
    </Card>
  );
}
