'use client';

import * as React from 'react';
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { formatNumber, formatTimeAgo } from '@/lib/utils';
import { 
  Server, 
  Database, 
  Cloud, 
  Zap,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  DollarSign,
  Activity
} from 'lucide-react';

interface ResourceMetrics {
  containers: {
    total: number;
    running: number;
    stopped: number;
    failed: number;
  };
  kubernetes: {
    nodes: {
      total: number;
      ready: number;
      notReady: number;
    };
    pods: {
      total: number;
      running: number;
      pending: number;
      failed: number;
    };
    services: number;
    deployments: number;
  };
  database: {
    connections: {
      active: number;
      idle: number;
      max: number;
    };
    queries: {
      total: number;
      slow: number;
      failed: number;
    };
    storage: {
      used: number;
      total: number;
      growth: number;
    };
  };
  cache: {
    hitRate: number;
    missRate: number;
    evictions: number;
    memory: {
      used: number;
      total: number;
    };
  };
  costs: {
    compute: number;
    storage: number;
    network: number;
    total: number;
    trend: number;
  };
  scaling: {
    autoScalingEvents: number;
    currentReplicas: number;
    desiredReplicas: number;
    maxReplicas: number;
  };
}

// Mock data - replace with actual API calls
const mockResourceMetrics: ResourceMetrics = {
  containers: {
    total: 24,
    running: 22,
    stopped: 1,
    failed: 1,
  },
  kubernetes: {
    nodes: {
      total: 6,
      ready: 6,
      notReady: 0,
    },
    pods: {
      total: 45,
      running: 42,
      pending: 2,
      failed: 1,
    },
    services: 12,
    deployments: 8,
  },
  database: {
    connections: {
      active: 23,
      idle: 17,
      max: 100,
    },
    queries: {
      total: 156789,
      slow: 234,
      failed: 12,
    },
    storage: {
      used: 45.6,
      total: 100.0,
      growth: 2.3,
    },
  },
  cache: {
    hitRate: 94.7,
    missRate: 5.3,
    evictions: 1234,
    memory: {
      used: 2.8,
      total: 4.0,
    },
  },
  costs: {
    compute: 1250.45,
    storage: 234.67,
    network: 89.23,
    total: 1574.35,
    trend: -5.2,
  },
  scaling: {
    autoScalingEvents: 8,
    currentReplicas: 6,
    desiredReplicas: 6,
    maxReplicas: 10,
  },
};

export function ResourceUtilization() {
  const [refreshing, setRefreshing] = useState(false);
  const [timeRange, setTimeRange] = useState('24h');

  const { data: metrics, isLoading, error, refetch } = useQuery({
    queryKey: ['resource-utilization', timeRange],
    queryFn: async () => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 600));
      return mockResourceMetrics;
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const getHealthColor = (percentage: number) => {
    if (percentage < 70) return 'text-green-600';
    if (percentage < 85) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStatusColor = (status: 'healthy' | 'warning' | 'critical') => {
    switch (status) {
      case 'healthy': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'warning': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'critical': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getTrendColor = (trend: number) => {
    if (trend > 0) return 'text-red-600';
    if (trend < 0) return 'text-green-600';
    return 'text-gray-600';
  };

  const getTrendIcon = (trend: number) => {
    if (trend > 0) return TrendingUp;
    if (trend < 0) return TrendingDown;
    return Activity;
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Server className="h-5 w-5" />
            <span>Resource Utilization</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <LoadingSpinner />
        </CardContent>
      </Card>
    );
  }

  if (error || !metrics) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Server className="h-5 w-5" />
            <span>Resource Utilization</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground">
            <AlertTriangle className="h-5 w-5 mx-auto mb-2" />
            <span>Failed to load resource metrics</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const dbConnectionUsage = (metrics.database.connections.active / metrics.database.connections.max) * 100;
  const dbStorageUsage = (metrics.database.storage.used / metrics.database.storage.total) * 100;
  const cacheMemoryUsage = (metrics.cache.memory.used / metrics.cache.memory.total) * 100;
  const podHealthPercentage = (metrics.kubernetes.pods.running / metrics.kubernetes.pods.total) * 100;
  const containerHealthPercentage = (metrics.containers.running / metrics.containers.total) * 100;

  const TrendIcon = getTrendIcon(metrics.costs.trend);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Server className="h-5 w-5" />
            <span>Resource Utilization</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-2 py-1 text-xs border rounded bg-background"
            >
              <option value="1h">1 Hour</option>
              <option value="24h">24 Hours</option>
              <option value="7d">7 Days</option>
            </select>
            <Button variant="outline" size="sm" onClick={handleRefresh} disabled={refreshing}>
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Container Status */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Server className="h-4 w-4 text-blue-600" />
            <h4 className="font-medium text-sm">Container Status</h4>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-green-600">{metrics.containers.running}</div>
              <div className="text-xs text-muted-foreground">Running</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-600">{metrics.containers.stopped}</div>
              <div className="text-xs text-muted-foreground">Stopped</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-red-600">{metrics.containers.failed}</div>
              <div className="text-xs text-muted-foreground">Failed</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-600">{metrics.containers.total}</div>
              <div className="text-xs text-muted-foreground">Total</div>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Container Health</span>
              <span className={`font-medium ${getHealthColor(containerHealthPercentage)}`}>
                {containerHealthPercentage.toFixed(1)}%
              </span>
            </div>
            <Progress value={containerHealthPercentage} className="h-2" />
          </div>
        </div>

        {/* Kubernetes Cluster */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Cloud className="h-4 w-4 text-purple-600" />
            <h4 className="font-medium text-sm">Kubernetes Cluster</h4>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-lg font-bold text-green-600">{metrics.kubernetes.nodes.ready}</div>
              <div className="text-xs text-muted-foreground">Ready Nodes</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-blue-600">{metrics.kubernetes.pods.running}</div>
              <div className="text-xs text-muted-foreground">Running Pods</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-purple-600">{metrics.kubernetes.services}</div>
              <div className="text-xs text-muted-foreground">Services</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-orange-600">{metrics.kubernetes.deployments}</div>
              <div className="text-xs text-muted-foreground">Deployments</div>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Pod Health</span>
              <span className={`font-medium ${getHealthColor(100 - podHealthPercentage)}`}>
                {podHealthPercentage.toFixed(1)}%
              </span>
            </div>
            <Progress value={podHealthPercentage} className="h-2" />
          </div>
        </div>

        {/* Database Resources */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Database className="h-4 w-4 text-green-600" />
            <h4 className="font-medium text-sm">Database Resources</h4>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Connection Usage</span>
                <span className={`font-medium ${getHealthColor(dbConnectionUsage)}`}>
                  {metrics.database.connections.active}/{metrics.database.connections.max}
                </span>
              </div>
              <Progress value={dbConnectionUsage} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Storage Usage</span>
                <span className={`font-medium ${getHealthColor(dbStorageUsage)}`}>
                  {metrics.database.storage.used.toFixed(1)} GB
                </span>
              </div>
              <Progress value={dbStorageUsage} className="h-2" />
            </div>
          </div>
          <div className="grid grid-cols-3 gap-4 text-xs">
            <div>
              <div className="text-muted-foreground">Total Queries</div>
              <div className="font-medium">{formatNumber(metrics.database.queries.total)}</div>
            </div>
            <div>
              <div className="text-muted-foreground">Slow Queries</div>
              <div className="font-medium text-yellow-600">{metrics.database.queries.slow}</div>
            </div>
            <div>
              <div className="text-muted-foreground">Failed Queries</div>
              <div className="font-medium text-red-600">{metrics.database.queries.failed}</div>
            </div>
          </div>
        </div>

        {/* Cache Performance */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Zap className="h-4 w-4 text-yellow-600" />
            <h4 className="font-medium text-sm">Cache Performance</h4>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Hit Rate</span>
                <span className="font-medium text-green-600">{metrics.cache.hitRate.toFixed(1)}%</span>
              </div>
              <Progress value={metrics.cache.hitRate} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Memory Usage</span>
                <span className={`font-medium ${getHealthColor(cacheMemoryUsage)}`}>
                  {metrics.cache.memory.used.toFixed(1)} GB
                </span>
              </div>
              <Progress value={cacheMemoryUsage} className="h-2" />
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4 text-xs">
            <div>
              <div className="text-muted-foreground">Miss Rate</div>
              <div className="font-medium text-red-600">{metrics.cache.missRate.toFixed(1)}%</div>
            </div>
            <div>
              <div className="text-muted-foreground">Evictions</div>
              <div className="font-medium">{formatNumber(metrics.cache.evictions)}</div>
            </div>
          </div>
        </div>

        {/* Auto Scaling */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Activity className="h-4 w-4 text-indigo-600" />
            <h4 className="font-medium text-sm">Auto Scaling</h4>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-lg font-bold text-blue-600">{metrics.scaling.currentReplicas}</div>
              <div className="text-xs text-muted-foreground">Current</div>
            </div>
            <div>
              <div className="text-lg font-bold text-green-600">{metrics.scaling.desiredReplicas}</div>
              <div className="text-xs text-muted-foreground">Desired</div>
            </div>
            <div>
              <div className="text-lg font-bold text-purple-600">{metrics.scaling.maxReplicas}</div>
              <div className="text-xs text-muted-foreground">Max</div>
            </div>
            <div>
              <div className="text-lg font-bold text-orange-600">{metrics.scaling.autoScalingEvents}</div>
              <div className="text-xs text-muted-foreground">Events (24h)</div>
            </div>
          </div>
        </div>

        {/* Cost Analysis */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <DollarSign className="h-4 w-4 text-green-600" />
            <h4 className="font-medium text-sm">Cost Analysis</h4>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-lg font-bold text-blue-600">${metrics.costs.compute.toFixed(2)}</div>
              <div className="text-xs text-muted-foreground">Compute</div>
            </div>
            <div>
              <div className="text-lg font-bold text-purple-600">${metrics.costs.storage.toFixed(2)}</div>
              <div className="text-xs text-muted-foreground">Storage</div>
            </div>
            <div>
              <div className="text-lg font-bold text-orange-600">${metrics.costs.network.toFixed(2)}</div>
              <div className="text-xs text-muted-foreground">Network</div>
            </div>
            <div>
              <div className="text-lg font-bold text-green-600">${metrics.costs.total.toFixed(2)}</div>
              <div className="text-xs text-muted-foreground">Total</div>
            </div>
          </div>
          <div className="flex items-center justify-center space-x-2 text-sm">
            <TrendIcon className={`h-4 w-4 ${getTrendColor(metrics.costs.trend)}`} />
            <span className={getTrendColor(metrics.costs.trend)}>
              {Math.abs(metrics.costs.trend).toFixed(1)}% {metrics.costs.trend > 0 ? 'increase' : 'decrease'} from last month
            </span>
          </div>
        </div>

        {/* Resource Summary */}
        <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg">
          <div className="flex items-center space-x-3">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <div>
              <div className="font-medium text-sm">Resource Status</div>
              <div className="text-xs text-muted-foreground">
                All resources are operating within normal parameters
              </div>
            </div>
          </div>
          <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
            Optimal
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
}
