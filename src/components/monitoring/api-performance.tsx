'use client';

import * as React from 'react';
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { formatNumber, formatTimeAgo } from '@/lib/utils';
import { 
  Globe, 
  Zap, 
  TrendingUp, 
  TrendingDown,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock,
  Activity,
  BarChart3
} from 'lucide-react';

interface ApiEndpointMetrics {
  endpoint: string;
  method: string;
  requestCount: number;
  averageResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  errorRate: number;
  successRate: number;
  throughput: number;
  lastAccessed: string;
  status: 'healthy' | 'warning' | 'critical';
}

interface ApiPerformanceData {
  overallMetrics: {
    totalRequests: number;
    averageResponseTime: number;
    errorRate: number;
    uptime: number;
    throughput: number;
  };
  endpoints: ApiEndpointMetrics[];
  responseTimeDistribution: {
    fast: number; // < 100ms
    medium: number; // 100ms - 500ms
    slow: number; // > 500ms
  };
  errorBreakdown: {
    '4xx': number;
    '5xx': number;
    timeout: number;
    network: number;
  };
  topEndpoints: ApiEndpointMetrics[];
}

// Mock data - replace with actual API calls
const mockApiPerformance: ApiPerformanceData = {
  overallMetrics: {
    totalRequests: 1567890,
    averageResponseTime: 0.089,
    errorRate: 0.12,
    uptime: 99.97,
    throughput: 2847,
  },
  responseTimeDistribution: {
    fast: 78.5,
    medium: 19.2,
    slow: 2.3,
  },
  errorBreakdown: {
    '4xx': 65.2,
    '5xx': 28.1,
    timeout: 4.7,
    network: 2.0,
  },
  endpoints: [
    {
      endpoint: '/api/tokens',
      method: 'GET',
      requestCount: 456789,
      averageResponseTime: 0.067,
      p95ResponseTime: 0.156,
      p99ResponseTime: 0.234,
      errorRate: 0.08,
      successRate: 99.92,
      throughput: 1250,
      lastAccessed: '2025-01-10T11:45:00Z',
      status: 'healthy',
    },
    {
      endpoint: '/api/tokens/{id}',
      method: 'GET',
      requestCount: 234567,
      averageResponseTime: 0.089,
      p95ResponseTime: 0.198,
      p99ResponseTime: 0.345,
      errorRate: 0.15,
      successRate: 99.85,
      throughput: 890,
      lastAccessed: '2025-01-10T11:44:00Z',
      status: 'healthy',
    },
    {
      endpoint: '/api/analyze',
      method: 'POST',
      requestCount: 123456,
      averageResponseTime: 1.234,
      p95ResponseTime: 2.567,
      p99ResponseTime: 4.123,
      errorRate: 0.23,
      successRate: 99.77,
      throughput: 450,
      lastAccessed: '2025-01-10T11:43:00Z',
      status: 'warning',
    },
    {
      endpoint: '/api/system/metrics',
      method: 'GET',
      requestCount: 89012,
      averageResponseTime: 0.156,
      p95ResponseTime: 0.345,
      p99ResponseTime: 0.567,
      errorRate: 0.05,
      successRate: 99.95,
      throughput: 320,
      lastAccessed: '2025-01-10T11:42:00Z',
      status: 'healthy',
    },
    {
      endpoint: '/api/alerts',
      method: 'GET',
      requestCount: 67890,
      averageResponseTime: 0.234,
      p95ResponseTime: 0.456,
      p99ResponseTime: 0.789,
      errorRate: 0.18,
      successRate: 99.82,
      throughput: 280,
      lastAccessed: '2025-01-10T11:41:00Z',
      status: 'healthy',
    },
  ],
  topEndpoints: [],
};

// Sort endpoints by request count for top endpoints
mockApiPerformance.topEndpoints = [...mockApiPerformance.endpoints]
  .sort((a, b) => b.requestCount - a.requestCount)
  .slice(0, 5);

export function ApiPerformance() {
  const [refreshing, setRefreshing] = useState(false);
  const [timeRange, setTimeRange] = useState('24h');
  const [sortBy, setSortBy] = useState('requests');

  const { data: performance, isLoading, error, refetch } = useQuery({
    queryKey: ['api-performance', timeRange],
    queryFn: async () => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 700));
      return mockApiPerformance;
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'warning': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'critical': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'GET': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'POST': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'PUT': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'DELETE': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getResponseTimeColor = (time: number) => {
    if (time < 0.1) return 'text-green-600';
    if (time < 0.5) return 'text-yellow-600';
    return 'text-red-600';
  };

  const sortedEndpoints = performance?.endpoints.sort((a, b) => {
    switch (sortBy) {
      case 'requests': return b.requestCount - a.requestCount;
      case 'response-time': return b.averageResponseTime - a.averageResponseTime;
      case 'error-rate': return b.errorRate - a.errorRate;
      case 'throughput': return b.throughput - a.throughput;
      default: return 0;
    }
  }) || [];

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Globe className="h-5 w-5" />
            <span>API Performance</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <LoadingSpinner />
        </CardContent>
      </Card>
    );
  }

  if (error || !performance) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Globe className="h-5 w-5" />
            <span>API Performance</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground">
            <AlertTriangle className="h-5 w-5 mx-auto mb-2" />
            <span>Failed to load API performance data</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Globe className="h-5 w-5" />
            <span>API Performance</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-2 py-1 text-xs border rounded bg-background"
            >
              <option value="1h">1 Hour</option>
              <option value="24h">24 Hours</option>
              <option value="7d">7 Days</option>
            </select>
            <Button variant="outline" size="sm" onClick={handleRefresh} disabled={refreshing}>
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Overall Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {formatNumber(performance.overallMetrics.totalRequests)}
            </div>
            <div className="text-xs text-muted-foreground">Total Requests</div>
          </div>
          <div className="text-center">
            <div className={`text-2xl font-bold ${getResponseTimeColor(performance.overallMetrics.averageResponseTime)}`}>
              {performance.overallMetrics.averageResponseTime.toFixed(3)}s
            </div>
            <div className="text-xs text-muted-foreground">Avg Response Time</div>
          </div>
          <div className="text-center">
            <div className={`text-2xl font-bold ${performance.overallMetrics.errorRate < 1 ? 'text-green-600' : 'text-red-600'}`}>
              {performance.overallMetrics.errorRate.toFixed(2)}%
            </div>
            <div className="text-xs text-muted-foreground">Error Rate</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {performance.overallMetrics.uptime.toFixed(2)}%
            </div>
            <div className="text-xs text-muted-foreground">Uptime</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {formatNumber(performance.overallMetrics.throughput)}
            </div>
            <div className="text-xs text-muted-foreground">Req/s</div>
          </div>
        </div>

        {/* Response Time Distribution */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">Response Time Distribution</h4>
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Fast (&lt; 100ms)</span>
              <span className="font-medium text-green-600">{performance.responseTimeDistribution.fast.toFixed(1)}%</span>
            </div>
            <Progress value={performance.responseTimeDistribution.fast} className="h-2" />
            
            <div className="flex items-center justify-between text-sm">
              <span>Medium (100ms - 500ms)</span>
              <span className="font-medium text-yellow-600">{performance.responseTimeDistribution.medium.toFixed(1)}%</span>
            </div>
            <Progress value={performance.responseTimeDistribution.medium} className="h-2" />
            
            <div className="flex items-center justify-between text-sm">
              <span>Slow (&gt; 500ms)</span>
              <span className="font-medium text-red-600">{performance.responseTimeDistribution.slow.toFixed(1)}%</span>
            </div>
            <Progress value={performance.responseTimeDistribution.slow} className="h-2" />
          </div>
        </div>

        {/* Error Breakdown */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">Error Breakdown</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-lg font-bold text-orange-600">{performance.errorBreakdown['4xx'].toFixed(1)}%</div>
              <div className="text-xs text-muted-foreground">4xx Errors</div>
            </div>
            <div>
              <div className="text-lg font-bold text-red-600">{performance.errorBreakdown['5xx'].toFixed(1)}%</div>
              <div className="text-xs text-muted-foreground">5xx Errors</div>
            </div>
            <div>
              <div className="text-lg font-bold text-purple-600">{performance.errorBreakdown.timeout.toFixed(1)}%</div>
              <div className="text-xs text-muted-foreground">Timeouts</div>
            </div>
            <div>
              <div className="text-lg font-bold text-blue-600">{performance.errorBreakdown.network.toFixed(1)}%</div>
              <div className="text-xs text-muted-foreground">Network</div>
            </div>
          </div>
        </div>

        {/* Endpoint Performance */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">Endpoint Performance</h4>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-2 py-1 text-xs border rounded bg-background"
            >
              <option value="requests">Sort by Requests</option>
              <option value="response-time">Sort by Response Time</option>
              <option value="error-rate">Sort by Error Rate</option>
              <option value="throughput">Sort by Throughput</option>
            </select>
          </div>
          
          <div className="space-y-2">
            {sortedEndpoints.map((endpoint, index) => (
              <div key={index} className="p-3 border rounded-lg">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <Badge className={getMethodColor(endpoint.method)}>
                      {endpoint.method}
                    </Badge>
                    <code className="text-sm font-mono">{endpoint.endpoint}</code>
                  </div>
                  <Badge className={getStatusColor(endpoint.status)}>
                    {endpoint.status}
                  </Badge>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-xs">
                  <div>
                    <div className="text-muted-foreground">Requests</div>
                    <div className="font-medium">{formatNumber(endpoint.requestCount)}</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Avg Response</div>
                    <div className={`font-medium ${getResponseTimeColor(endpoint.averageResponseTime)}`}>
                      {endpoint.averageResponseTime.toFixed(3)}s
                    </div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">P95</div>
                    <div className="font-medium">{endpoint.p95ResponseTime.toFixed(3)}s</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Error Rate</div>
                    <div className={`font-medium ${endpoint.errorRate < 0.1 ? 'text-green-600' : 'text-red-600'}`}>
                      {endpoint.errorRate.toFixed(2)}%
                    </div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Throughput</div>
                    <div className="font-medium">{formatNumber(endpoint.throughput)} req/s</div>
                  </div>
                </div>
                
                <div className="mt-2 text-xs text-muted-foreground">
                  Last accessed: {formatTimeAgo(endpoint.lastAccessed)}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Performance Summary */}
        <div className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="flex items-center space-x-3">
            <CheckCircle className="h-5 w-5 text-blue-600" />
            <div>
              <div className="font-medium text-sm">API Performance Status</div>
              <div className="text-xs text-muted-foreground">
                {performance.overallMetrics.errorRate < 1 ? 'Excellent performance across all endpoints' : 'Some endpoints need attention'}
              </div>
            </div>
          </div>
          <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
            {performance.overallMetrics.errorRate < 1 ? 'Healthy' : 'Needs Attention'}
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
}
