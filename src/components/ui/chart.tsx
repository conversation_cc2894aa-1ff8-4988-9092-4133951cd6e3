import React from 'react';
import {
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  AreaChart,
  Area,
  BarChart,
  Bar,
  Cell
} from 'recharts';
import { cn } from '@/lib/utils';

interface ChartProps {
  data: any[];
  className?: string;
}

export const PriceChart: React.FC<ChartProps & { showArea?: boolean }> = ({ 
  data, 
  className, 
  showArea = false 
}) => {
  const Chart = showArea ? AreaChart : LineChart;
  const DataElement = showArea ? Area : Line;
  
  return (
    <div className={cn('w-full h-64', className)}>
      <ResponsiveContainer width="100%" height="100%">
        <Chart data={data}>
          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
          <XAxis 
            dataKey="timestamp" 
            tickFormatter={(value) => new Date(value).toLocaleDateString()}
            stroke="hsl(var(--muted-foreground))"
          />
          <YAxis 
            domain={['dataMin - 5', 'dataMax + 5']}
            tickFormatter={(value) => `$${value.toLocaleString()}`}
            stroke="hsl(var(--muted-foreground))"
          />
          <Tooltip 
            labelFormatter={(value) => new Date(value).toLocaleString()}
            formatter={(value: number) => [`$${value.toLocaleString()}`, 'Price']}
            contentStyle={{
              backgroundColor: 'hsl(var(--card))',
              border: '1px solid hsl(var(--border))',
              borderRadius: '6px'
            }}
          />
          <DataElement
            type="monotone"
            dataKey="close"
            stroke="hsl(var(--primary))"
            strokeWidth={2}
            dot={false}
            {...(showArea && {
              fill: 'url(#colorGradient)',
              fillOpacity: 0.1
            })}
          />
          {showArea && (
            <defs>
              <linearGradient id="colorGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="hsl(var(--primary))" stopOpacity={0.3}/>
                <stop offset="95%" stopColor="hsl(var(--primary))" stopOpacity={0}/>
              </linearGradient>
            </defs>
          )}
        </Chart>
      </ResponsiveContainer>
    </div>
  );
};

export const VolumeChart: React.FC<ChartProps> = ({ data, className }) => (
  <div className={cn('w-full h-32', className)}>
    <ResponsiveContainer width="100%" height="100%">
      <BarChart data={data}>
        <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
        <XAxis 
          dataKey="timestamp" 
          tickFormatter={(value) => new Date(value).toLocaleDateString()}
          stroke="hsl(var(--muted-foreground))"
        />
        <YAxis 
          tickFormatter={(value) => `${(value / 1000000).toFixed(1)}M`}
          stroke="hsl(var(--muted-foreground))"
        />
        <Tooltip 
          labelFormatter={(value) => new Date(value).toLocaleString()}
          formatter={(value: number) => [`${(value / 1000000).toFixed(2)}M`, 'Volume']}
          contentStyle={{
            backgroundColor: 'hsl(var(--card))',
            border: '1px solid hsl(var(--border))',
            borderRadius: '6px'
          }}
        />
        <Bar dataKey="volume" fill="hsl(var(--muted))" />
      </BarChart>
    </ResponsiveContainer>
  </div>
);

export const HeatmapChart: React.FC<{ data: Array<{ name: string; value: number; change: number }> }> = ({ data }) => (
  <div className="grid grid-cols-4 gap-2">
    {data.map((item, index) => (
      <div
        key={index}
        className={cn(
          'p-4 rounded-lg text-center transition-all hover:scale-105 cursor-pointer',
          item.change > 0 
            ? 'bg-emerald-500/20 hover:bg-emerald-500/30' 
            : 'bg-red-500/20 hover:bg-red-500/30'
        )}
      >
        <div className="font-mono text-sm font-bold">{item.name}</div>
        <div className="text-xs text-muted-foreground">{item.value.toFixed(3)}</div>
        <div className={cn(
          'text-xs font-medium',
          item.change > 0 ? 'text-emerald-400' : 'text-red-400'
        )}>
          {item.change > 0 ? '+' : ''}{item.change.toFixed(2)}%
        </div>
      </div>
    ))}
  </div>
);