'use client';

import * as React from 'react';
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useRealTimeData } from '@/hooks/useRealTimeData';
import { formatTimeAgo } from '@/lib/utils';
import { 
  Lock, 
  AlertTriangle, 
  CheckCircle, 
  Eye,
  RefreshCw,
  ExternalLink,
  Shield,
  User,
  Database
} from 'lucide-react';

interface BoplDetection {
  id: string;
  endpoint: string;
  method: string;
  vulnerability: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  description: string;
  detectedAt: string;
  affectedUsers: number;
  potentialImpact: string;
  status: 'DETECTED' | 'INVESTIGATING' | 'MITIGATED' | 'RESOLVED';
  evidence: {
    requestPattern: string;
    userAgent: string;
    ipAddress: string;
    accessAttempts: number;
  };
  recommendation: string;
}

// Mock data - replace with actual API calls
const mockBoplDetections: BoplDetection[] = [
  {
    id: 'bopl-1',
    endpoint: '/api/tokens/{id}',
    method: 'GET',
    vulnerability: 'Broken Object Level Authorization',
    severity: 'HIGH',
    description: 'User can access token details for tokens they do not own by manipulating the ID parameter',
    detectedAt: '2025-01-10T11:30:00Z',
    affectedUsers: 23,
    potentialImpact: 'Unauthorized access to sensitive token information',
    status: 'INVESTIGATING',
    evidence: {
      requestPattern: 'Sequential ID enumeration detected',
      userAgent: 'Mozilla/5.0 (automated scanner)',
      ipAddress: '*************',
      accessAttempts: 1247,
    },
    recommendation: 'Implement proper authorization checks to verify user ownership of requested resources',
  },
  {
    id: 'bopl-2',
    endpoint: '/api/analysis/{id}',
    method: 'GET',
    vulnerability: 'Insecure Direct Object Reference',
    severity: 'CRITICAL',
    description: 'Analysis results can be accessed by unauthorized users through direct object reference',
    detectedAt: '2025-01-10T10:45:00Z',
    affectedUsers: 8,
    potentialImpact: 'Exposure of proprietary analysis algorithms and sensitive financial data',
    status: 'MITIGATED',
    evidence: {
      requestPattern: 'GUID enumeration with valid tokens',
      userAgent: 'PostmanRuntime/7.32.3',
      ipAddress: '*********',
      accessAttempts: 89,
    },
    recommendation: 'Add user context validation and implement resource-level access controls',
  },
  {
    id: 'bopl-3',
    endpoint: '/api/users/{id}/portfolio',
    method: 'GET',
    vulnerability: 'Horizontal Privilege Escalation',
    severity: 'MEDIUM',
    description: 'Users can view other users\' portfolio data by changing the user ID parameter',
    detectedAt: '2025-01-10T09:15:00Z',
    affectedUsers: 156,
    potentialImpact: 'Privacy violation and potential financial information disclosure',
    status: 'RESOLVED',
    evidence: {
      requestPattern: 'Cross-user data access attempts',
      userAgent: 'Chrome/120.0.0.0',
      ipAddress: '************',
      accessAttempts: 34,
    },
    recommendation: 'Enforce user session validation and implement proper authorization middleware',
  },
];

export function BoplDetection() {
  const [refreshing, setRefreshing] = useState(false);

  const { data: detections, isLoading, error, refetch } = useQuery({
    queryKey: ['bopl-detections'],
    queryFn: async () => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 700));
      return mockBoplDetections;
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'LOW': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'HIGH': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      case 'CRITICAL': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DETECTED': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'INVESTIGATING': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'MITIGATED': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'RESOLVED': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'DETECTED': return AlertTriangle;
      case 'INVESTIGATING': return Eye;
      case 'MITIGATED': return Shield;
      case 'RESOLVED': return CheckCircle;
      default: return AlertTriangle;
    }
  };

  const activeDetections = detections?.filter(d => d.status !== 'RESOLVED') || [];
  const resolvedDetections = detections?.filter(d => d.status === 'RESOLVED') || [];

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Lock className="h-5 w-5" />
            <span>BOPLA Detection</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Badge variant={activeDetections.length > 0 ? 'destructive' : 'secondary'}>
              {activeDetections.length} active
            </Badge>
            <Button variant="outline" size="sm" onClick={handleRefresh} disabled={refreshing}>
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
        <p className="text-sm text-muted-foreground">
          Broken Object-Level Authorization detection and monitoring
        </p>
      </CardHeader>
      
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            {Array.from({ length: 2 }).map((_, i) => (
              <div key={i} className="p-4 border rounded-lg">
                <LoadingSpinner size="sm" />
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="text-center text-muted-foreground py-8">
            <AlertTriangle className="h-5 w-5 mx-auto mb-2" />
            <span>Failed to load BOPLA detections</span>
          </div>
        ) : !detections?.length ? (
          <div className="text-center text-muted-foreground py-8">
            <CheckCircle className="h-5 w-5 mx-auto mb-2 text-green-600" />
            <span>No BOPLA vulnerabilities detected</span>
            <p className="text-xs mt-1">All endpoints are secure</p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Active Detections */}
            {activeDetections.length > 0 && (
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-red-600">Active Vulnerabilities</h4>
                {activeDetections.map((detection) => {
                  const StatusIcon = getStatusIcon(detection.status);
                  
                  return (
                    <div
                      key={detection.id}
                      className="p-4 border border-red-200 rounded-lg bg-red-50/50 dark:bg-red-950/20 dark:border-red-800"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <StatusIcon className="h-4 w-4 text-red-600" />
                          <div>
                            <div className="font-medium text-sm">{detection.vulnerability}</div>
                            <code className="text-xs bg-muted px-1 py-0.5 rounded">
                              {detection.method} {detection.endpoint}
                            </code>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge className={getSeverityColor(detection.severity)}>
                            {detection.severity}
                          </Badge>
                          <Badge className={getStatusColor(detection.status)}>
                            {detection.status}
                          </Badge>
                        </div>
                      </div>
                      
                      <p className="text-sm text-muted-foreground mb-3">
                        {detection.description}
                      </p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                        <div className="space-y-2">
                          <div className="text-xs font-medium">Impact Assessment</div>
                          <div className="text-xs text-muted-foreground">
                            <div className="flex items-center space-x-1 mb-1">
                              <User className="h-3 w-3" />
                              <span>{detection.affectedUsers} users affected</span>
                            </div>
                            <div>{detection.potentialImpact}</div>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <div className="text-xs font-medium">Evidence</div>
                          <div className="text-xs text-muted-foreground space-y-1">
                            <div>Pattern: {detection.evidence.requestPattern}</div>
                            <div>IP: {detection.evidence.ipAddress}</div>
                            <div>Attempts: {detection.evidence.accessAttempts}</div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="bg-blue-50 dark:bg-blue-950/20 p-3 rounded border border-blue-200 dark:border-blue-800">
                        <div className="text-xs font-medium text-blue-800 dark:text-blue-300 mb-1">
                          Recommendation
                        </div>
                        <div className="text-xs text-blue-700 dark:text-blue-400">
                          {detection.recommendation}
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between mt-3 pt-3 border-t">
                        <div className="text-xs text-muted-foreground">
                          Detected {formatTimeAgo(detection.detectedAt)}
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button variant="outline" size="sm">
                            Investigate
                          </Button>
                          <Button variant="outline" size="sm">
                            <ExternalLink className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
            
            {/* Resolved Detections */}
            {resolvedDetections.length > 0 && (
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-green-600">Recently Resolved</h4>
                {resolvedDetections.slice(0, 2).map((detection) => (
                  <div
                    key={detection.id}
                    className="p-3 border border-green-200 rounded-lg bg-green-50/50 dark:bg-green-950/20 dark:border-green-800 opacity-75"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <div>
                          <div className="font-medium text-sm">{detection.vulnerability}</div>
                          <code className="text-xs bg-muted px-1 py-0.5 rounded">
                            {detection.method} {detection.endpoint}
                          </code>
                        </div>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Resolved {formatTimeAgo(detection.detectedAt)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
