'use client';

import * as React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useRealTimeData } from '@/hooks/useRealTimeData';
import { formatNumber } from '@/lib/utils';
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  Activity,
  Eye,
  Bot,
  Lock,
  FileText,
  TrendingUp,
  TrendingDown
} from 'lucide-react';

interface SecurityMetrics {
  overallSecurityScore: number;
  threatLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  activeThreats: number;
  blockedAttacks: number;
  apiEndpointsMonitored: number;
  vulnerableEndpoints: number;
  botRequestsBlocked: number;
  complianceScore: number;
  lastSecurityScan: string;
  uptime: number;
}

// Mock data - replace with actual API calls
const mockSecurityMetrics: SecurityMetrics = {
  overallSecurityScore: 94.2,
  threatLevel: 'LOW',
  activeThreats: 2,
  blockedAttacks: 1247,
  apiEndpointsMonitored: 156,
  vulnerableEndpoints: 3,
  botRequestsBlocked: 8934,
  complianceScore: 98.7,
  lastSecurityScan: '2025-01-10T11:30:00Z',
  uptime: 99.97,
};

export function SecurityOverview() {
  const { getActiveAlerts } = useRealTimeData();
  const activeAlerts = getActiveAlerts();

  const { data: metrics, isLoading, error } = useQuery({
    queryKey: ['security-metrics'],
    queryFn: async () => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 600));
      return mockSecurityMetrics;
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 8 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <LoadingSpinner />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error || !metrics) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            <span>Failed to load security metrics</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getThreatLevelColor = (level: string) => {
    switch (level) {
      case 'LOW': return 'text-green-600 bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800';
      case 'MEDIUM': return 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:bg-yellow-950 dark:border-yellow-800';
      case 'HIGH': return 'text-orange-600 bg-orange-50 border-orange-200 dark:bg-orange-950 dark:border-orange-800';
      case 'CRITICAL': return 'text-red-600 bg-red-50 border-red-200 dark:bg-red-950 dark:border-red-800';
      default: return 'text-gray-600 bg-gray-50 border-gray-200 dark:bg-gray-950 dark:border-gray-800';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const securityCards = [
    {
      title: 'Security Score',
      value: `${metrics.overallSecurityScore.toFixed(1)}%`,
      change: '+2.1% from last week',
      icon: Shield,
      trend: 'up' as const,
      color: getScoreColor(metrics.overallSecurityScore),
      progress: metrics.overallSecurityScore,
    },
    {
      title: 'Threat Level',
      value: metrics.threatLevel,
      change: `${metrics.activeThreats} active threats`,
      icon: AlertTriangle,
      trend: metrics.activeThreats > 5 ? 'up' : 'down',
      color: getThreatLevelColor(metrics.threatLevel).split(' ')[0],
      badge: true,
    },
    {
      title: 'Blocked Attacks',
      value: formatNumber(metrics.blockedAttacks),
      change: '+156 in last 24h',
      icon: Lock,
      trend: 'up' as const,
      color: 'text-blue-600',
    },
    {
      title: 'API Security',
      value: `${metrics.apiEndpointsMonitored}`,
      change: `${metrics.vulnerableEndpoints} vulnerable`,
      icon: Eye,
      trend: metrics.vulnerableEndpoints > 0 ? 'up' : 'down',
      color: 'text-purple-600',
    },
    {
      title: 'Bot Detection',
      value: formatNumber(metrics.botRequestsBlocked),
      change: 'Requests blocked',
      icon: Bot,
      trend: 'up' as const,
      color: 'text-indigo-600',
    },
    {
      title: 'Compliance Score',
      value: `${metrics.complianceScore.toFixed(1)}%`,
      change: 'GDPR, SOX, SOC2',
      icon: FileText,
      trend: 'up' as const,
      color: getScoreColor(metrics.complianceScore),
      progress: metrics.complianceScore,
    },
    {
      title: 'System Uptime',
      value: `${metrics.uptime.toFixed(2)}%`,
      change: 'Excellent reliability',
      icon: Activity,
      trend: 'up' as const,
      color: 'text-green-600',
      progress: metrics.uptime,
    },
    {
      title: 'Active Alerts',
      value: activeAlerts.length.toString(),
      change: 'Requires attention',
      icon: AlertTriangle,
      trend: activeAlerts.length > 5 ? 'up' : 'down',
      color: activeAlerts.length > 5 ? 'text-red-600' : 'text-green-600',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Main Security Status */}
      <Card className="border-2">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-6 w-6 text-green-600" />
            <span>Security Status</span>
            <Badge className={getThreatLevelColor(metrics.threatLevel)}>
              {metrics.threatLevel} THREAT LEVEL
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">Overall Security Score</div>
              <div className="text-3xl font-bold text-green-600">
                {metrics.overallSecurityScore.toFixed(1)}%
              </div>
              <Progress value={metrics.overallSecurityScore} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">Compliance Score</div>
              <div className="text-3xl font-bold text-blue-600">
                {metrics.complianceScore.toFixed(1)}%
              </div>
              <Progress value={metrics.complianceScore} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">System Uptime</div>
              <div className="text-3xl font-bold text-purple-600">
                {metrics.uptime.toFixed(2)}%
              </div>
              <Progress value={metrics.uptime} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Security Metrics Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {securityCards.map((card, index) => {
          const Icon = card.icon;
          const TrendIcon = card.trend === 'up' ? TrendingUp : TrendingDown;
          
          return (
            <Card key={index} className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {card.title}
                </CardTitle>
                <Icon className={`h-4 w-4 ${card.color}`} />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    {card.badge ? (
                      <Badge className={getThreatLevelColor(card.value)}>
                        {card.value}
                      </Badge>
                    ) : (
                      <div className="text-2xl font-bold">{card.value}</div>
                    )}
                  </div>
                  
                  {card.progress !== undefined && (
                    <Progress value={card.progress} className="h-1" />
                  )}
                  
                  <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                    <TrendIcon className={`h-3 w-3 ${
                      card.trend === 'up' ? 'text-green-600' : 'text-red-600'
                    }`} />
                    <span>{card.change}</span>
                  </div>
                </div>
              </CardContent>
              
              {/* Subtle gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-transparent to-black/5 pointer-events-none" />
            </Card>
          );
        })}
      </div>
    </div>
  );
}
