'use client';

import * as React from 'react';
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { formatTimeAgo } from '@/lib/utils';
import { 
  FileText, 
  CheckCircle, 
  AlertTriangle, 
  RefreshCw,
  Eye,
  Clock,
  Shield,
  Activity,
  TrendingUp
} from 'lucide-react';

interface ComplianceFramework {
  id: string;
  name: string;
  description: string;
  overallScore: number;
  status: 'COMPLIANT' | 'NON_COMPLIANT' | 'PARTIAL' | 'PENDING';
  lastAudit: string;
  nextAudit: string;
  requirements: Array<{
    id: string;
    title: string;
    description: string;
    status: 'COMPLIANT' | 'NON_COMPLIANT' | 'PARTIAL';
    lastChecked: string;
    evidence: string[];
    remediation?: string;
  }>;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

// Mock data - replace with actual API calls
const mockComplianceFrameworks: ComplianceFramework[] = [
  {
    id: 'gdpr',
    name: 'GDPR',
    description: 'General Data Protection Regulation',
    overallScore: 94.2,
    status: 'COMPLIANT',
    lastAudit: '2025-01-01T00:00:00Z',
    nextAudit: '2025-04-01T00:00:00Z',
    riskLevel: 'LOW',
    requirements: [
      {
        id: 'gdpr-1',
        title: 'Data Processing Lawfulness',
        description: 'Ensure all data processing has a lawful basis',
        status: 'COMPLIANT',
        lastChecked: '2025-01-10T10:00:00Z',
        evidence: ['Privacy policy updated', 'Consent mechanisms implemented'],
      },
      {
        id: 'gdpr-2',
        title: 'Data Subject Rights',
        description: 'Implement mechanisms for data subject rights',
        status: 'COMPLIANT',
        lastChecked: '2025-01-10T09:30:00Z',
        evidence: ['Data export functionality', 'Deletion procedures'],
      },
      {
        id: 'gdpr-3',
        title: 'Data Breach Notification',
        description: 'Implement 72-hour breach notification procedures',
        status: 'PARTIAL',
        lastChecked: '2025-01-10T08:00:00Z',
        evidence: ['Incident response plan'],
        remediation: 'Automate breach detection and notification system',
      },
    ],
  },
  {
    id: 'sox',
    name: 'SOX',
    description: 'Sarbanes-Oxley Act',
    overallScore: 87.5,
    status: 'PARTIAL',
    lastAudit: '2024-12-15T00:00:00Z',
    nextAudit: '2025-03-15T00:00:00Z',
    riskLevel: 'MEDIUM',
    requirements: [
      {
        id: 'sox-1',
        title: 'Internal Controls',
        description: 'Establish and maintain internal controls over financial reporting',
        status: 'COMPLIANT',
        lastChecked: '2025-01-10T11:00:00Z',
        evidence: ['Control documentation', 'Testing procedures'],
      },
      {
        id: 'sox-2',
        title: 'Management Assessment',
        description: 'Annual management assessment of internal controls',
        status: 'NON_COMPLIANT',
        lastChecked: '2025-01-10T10:30:00Z',
        evidence: [],
        remediation: 'Schedule annual management assessment for Q1 2025',
      },
      {
        id: 'sox-3',
        title: 'Auditor Independence',
        description: 'Ensure auditor independence and rotation',
        status: 'COMPLIANT',
        lastChecked: '2025-01-10T09:00:00Z',
        evidence: ['Auditor rotation schedule', 'Independence documentation'],
      },
    ],
  },
  {
    id: 'soc2',
    name: 'SOC 2',
    description: 'Service Organization Control 2',
    overallScore: 96.8,
    status: 'COMPLIANT',
    lastAudit: '2024-11-30T00:00:00Z',
    nextAudit: '2025-05-30T00:00:00Z',
    riskLevel: 'LOW',
    requirements: [
      {
        id: 'soc2-1',
        title: 'Security',
        description: 'Implement security controls to protect customer data',
        status: 'COMPLIANT',
        lastChecked: '2025-01-10T11:30:00Z',
        evidence: ['Security policies', 'Access controls', 'Encryption'],
      },
      {
        id: 'soc2-2',
        title: 'Availability',
        description: 'Ensure system availability and performance',
        status: 'COMPLIANT',
        lastChecked: '2025-01-10T11:15:00Z',
        evidence: ['Uptime monitoring', 'Disaster recovery plan'],
      },
      {
        id: 'soc2-3',
        title: 'Processing Integrity',
        description: 'Ensure system processing is complete and accurate',
        status: 'COMPLIANT',
        lastChecked: '2025-01-10T11:00:00Z',
        evidence: ['Data validation controls', 'Error handling procedures'],
      },
    ],
  },
];

export function ComplianceMonitoring() {
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFramework, setSelectedFramework] = useState<string | null>(null);

  const { data: frameworks, isLoading, error, refetch } = useQuery({
    queryKey: ['compliance-frameworks'],
    queryFn: async () => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800));
      return mockComplianceFrameworks;
    },
    refetchInterval: 300000, // Refetch every 5 minutes
  });

  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLIANT': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'NON_COMPLIANT': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'PARTIAL': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'PENDING': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'LOW': return 'text-green-600';
      case 'MEDIUM': return 'text-yellow-600';
      case 'HIGH': return 'text-orange-600';
      case 'CRITICAL': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLIANT': return CheckCircle;
      case 'NON_COMPLIANT': return AlertTriangle;
      case 'PARTIAL': return Clock;
      case 'PENDING': return Eye;
      default: return AlertTriangle;
    }
  };

  const overallComplianceScore = frameworks?.reduce((acc, framework) => acc + framework.overallScore, 0) / (frameworks?.length || 1);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Compliance Monitoring</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Badge variant="outline">
              {frameworks?.length || 0} frameworks
            </Badge>
            <Button variant="outline" size="sm" onClick={handleRefresh} disabled={refreshing}>
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="p-4 border rounded-lg">
                <LoadingSpinner size="sm" />
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="text-center text-muted-foreground py-8">
            <AlertTriangle className="h-5 w-5 mx-auto mb-2" />
            <span>Failed to load compliance data</span>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Overall Compliance Score */}
            <div className="p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <Shield className="h-5 w-5 text-blue-600" />
                  <span className="font-medium">Overall Compliance Score</span>
                </div>
                <div className="text-2xl font-bold text-blue-600">
                  {overallComplianceScore?.toFixed(1)}%
                </div>
              </div>
              <Progress value={overallComplianceScore} className="h-2" />
              <div className="text-xs text-blue-700 dark:text-blue-400 mt-2">
                Excellent compliance across all monitored frameworks
              </div>
            </div>

            {/* Compliance Frameworks */}
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Compliance Frameworks</h4>
              {frameworks?.map((framework) => {
                const StatusIcon = getStatusIcon(framework.status);
                const compliantRequirements = framework.requirements.filter(r => r.status === 'COMPLIANT').length;
                const totalRequirements = framework.requirements.length;
                
                return (
                  <div
                    key={framework.id}
                    className="p-4 border rounded-lg hover:bg-accent/50 transition-colors cursor-pointer"
                    onClick={() => setSelectedFramework(selectedFramework === framework.id ? null : framework.id)}
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <StatusIcon className={`h-5 w-5 ${
                          framework.status === 'COMPLIANT' ? 'text-green-600' :
                          framework.status === 'NON_COMPLIANT' ? 'text-red-600' :
                          framework.status === 'PARTIAL' ? 'text-yellow-600' : 'text-blue-600'
                        }`} />
                        <div>
                          <div className="font-medium">{framework.name}</div>
                          <div className="text-sm text-muted-foreground">{framework.description}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className={getStatusColor(framework.status)}>
                          {framework.status}
                        </Badge>
                        <div className={`text-sm font-medium ${getRiskColor(framework.riskLevel)}`}>
                          {framework.riskLevel} RISK
                        </div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                      <div>
                        <div className="text-xs text-muted-foreground">Compliance Score</div>
                        <div className="text-lg font-bold">{framework.overallScore.toFixed(1)}%</div>
                        <Progress value={framework.overallScore} className="h-1 mt-1" />
                      </div>
                      <div>
                        <div className="text-xs text-muted-foreground">Requirements</div>
                        <div className="text-lg font-bold">
                          {compliantRequirements}/{totalRequirements}
                        </div>
                        <div className="text-xs text-muted-foreground">compliant</div>
                      </div>
                      <div>
                        <div className="text-xs text-muted-foreground">Next Audit</div>
                        <div className="text-sm font-medium">
                          {formatTimeAgo(framework.nextAudit)}
                        </div>
                      </div>
                    </div>
                    
                    {selectedFramework === framework.id && (
                      <div className="mt-4 pt-4 border-t space-y-3">
                        <h5 className="font-medium text-sm">Requirements</h5>
                        {framework.requirements.map((requirement) => {
                          const ReqStatusIcon = getStatusIcon(requirement.status);
                          
                          return (
                            <div key={requirement.id} className="p-3 border rounded-lg">
                              <div className="flex items-start justify-between mb-2">
                                <div className="flex items-center space-x-2">
                                  <ReqStatusIcon className={`h-4 w-4 ${
                                    requirement.status === 'COMPLIANT' ? 'text-green-600' :
                                    requirement.status === 'NON_COMPLIANT' ? 'text-red-600' :
                                    'text-yellow-600'
                                  }`} />
                                  <div>
                                    <div className="font-medium text-sm">{requirement.title}</div>
                                    <div className="text-xs text-muted-foreground">{requirement.description}</div>
                                  </div>
                                </div>
                                <Badge className={getStatusColor(requirement.status)}>
                                  {requirement.status}
                                </Badge>
                              </div>
                              
                              {requirement.evidence.length > 0 && (
                                <div className="mb-2">
                                  <div className="text-xs font-medium mb-1">Evidence</div>
                                  <div className="flex flex-wrap gap-1">
                                    {requirement.evidence.map((evidence, i) => (
                                      <Badge key={i} variant="outline" className="text-xs">
                                        {evidence}
                                      </Badge>
                                    ))}
                                  </div>
                                </div>
                              )}
                              
                              {requirement.remediation && (
                                <div className="bg-yellow-50 dark:bg-yellow-950/20 p-2 rounded border border-yellow-200 dark:border-yellow-800">
                                  <div className="text-xs font-medium text-yellow-800 dark:text-yellow-300 mb-1">
                                    Remediation Required
                                  </div>
                                  <div className="text-xs text-yellow-700 dark:text-yellow-400">
                                    {requirement.remediation}
                                  </div>
                                </div>
                              )}
                              
                              <div className="text-xs text-muted-foreground mt-2">
                                Last checked: {formatTimeAgo(requirement.lastChecked)}
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
