'use client';

import * as React from 'react';
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useRealTimeData } from '@/hooks/useRealTimeData';
import { formatTimeAgo } from '@/lib/utils';
import { 
  Eye, 
  Search, 
  Filter, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Globe,
  Lock,
  Unlock,
  RefreshCw
} from 'lucide-react';

interface ApiEndpoint {
  id: string;
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  description: string;
  isPublic: boolean;
  requiresAuth: boolean;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  lastAccessed: string;
  requestCount24h: number;
  vulnerabilities: string[];
  responseTime: number;
  errorRate: number;
}

// Mock data - replace with actual API calls
const mockApiEndpoints: ApiEndpoint[] = [
  {
    id: 'api-1',
    path: '/api/tokens',
    method: 'GET',
    description: 'Get token list with pagination',
    isPublic: true,
    requiresAuth: false,
    riskLevel: 'LOW',
    lastAccessed: '2025-01-10T11:45:00Z',
    requestCount24h: 15420,
    vulnerabilities: [],
    responseTime: 0.089,
    errorRate: 0.12,
  },
  {
    id: 'api-2',
    path: '/api/tokens/{id}',
    method: 'GET',
    description: 'Get specific token details',
    isPublic: true,
    requiresAuth: false,
    riskLevel: 'MEDIUM',
    lastAccessed: '2025-01-10T11:40:00Z',
    requestCount24h: 8934,
    vulnerabilities: ['BOPLA - Broken Object Level Authorization'],
    responseTime: 0.156,
    errorRate: 0.08,
  },
  {
    id: 'api-3',
    path: '/api/analyze',
    method: 'POST',
    description: 'Analyze token for risks and opportunities',
    isPublic: false,
    requiresAuth: true,
    riskLevel: 'HIGH',
    lastAccessed: '2025-01-10T11:35:00Z',
    requestCount24h: 2341,
    vulnerabilities: ['Rate limiting bypass', 'Input validation'],
    responseTime: 2.456,
    errorRate: 1.23,
  },
  {
    id: 'api-4',
    path: '/api/system/metrics',
    method: 'GET',
    description: 'Get system performance metrics',
    isPublic: false,
    requiresAuth: true,
    riskLevel: 'CRITICAL',
    lastAccessed: '2025-01-10T11:30:00Z',
    requestCount24h: 567,
    vulnerabilities: ['Information disclosure', 'Insufficient access control'],
    responseTime: 0.234,
    errorRate: 0.05,
  },
  {
    id: 'api-5',
    path: '/api/alerts',
    method: 'GET',
    description: 'Get security alerts',
    isPublic: false,
    requiresAuth: true,
    riskLevel: 'LOW',
    lastAccessed: '2025-01-10T11:25:00Z',
    requestCount24h: 1234,
    vulnerabilities: [],
    responseTime: 0.123,
    errorRate: 0.02,
  },
];

export function ApiInventory() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRisk, setFilterRisk] = useState<string>('ALL');
  const [refreshing, setRefreshing] = useState(false);

  const { data: endpoints, isLoading, error, refetch } = useQuery({
    queryKey: ['api-inventory'],
    queryFn: async () => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800));
      return mockApiEndpoints;
    },
    refetchInterval: 60000, // Refetch every minute
  });

  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const filteredEndpoints = endpoints?.filter(endpoint => {
    const matchesSearch = endpoint.path.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         endpoint.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterRisk === 'ALL' || endpoint.riskLevel === filterRisk;
    return matchesSearch && matchesFilter;
  }) || [];

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'LOW': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'HIGH': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      case 'CRITICAL': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'GET': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'POST': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'PUT': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'DELETE': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'PATCH': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const riskLevels = ['ALL', 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL'];

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Eye className="h-5 w-5" />
            <span>API Inventory</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Badge variant="outline">
              {filteredEndpoints.length} endpoints
            </Badge>
            <Button variant="outline" size="sm" onClick={handleRefresh} disabled={refreshing}>
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
        
        {/* Search and Filter */}
        <div className="flex items-center space-x-2 mt-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search endpoints..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <select
            value={filterRisk}
            onChange={(e) => setFilterRisk(e.target.value)}
            className="px-3 py-2 border rounded-md bg-background text-foreground"
          >
            {riskLevels.map(level => (
              <option key={level} value={level}>{level}</option>
            ))}
          </select>
        </div>
      </CardHeader>
      
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
                <LoadingSpinner size="sm" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-muted rounded animate-pulse" />
                  <div className="h-3 bg-muted rounded animate-pulse w-2/3" />
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="text-center text-muted-foreground py-8">
            <AlertTriangle className="h-5 w-5 mx-auto mb-2" />
            <span>Failed to load API inventory</span>
          </div>
        ) : !filteredEndpoints.length ? (
          <div className="text-center text-muted-foreground py-8">
            <Search className="h-5 w-5 mx-auto mb-2" />
            <span>No endpoints found</span>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredEndpoints.map((endpoint) => (
              <div
                key={endpoint.id}
                className="p-4 border rounded-lg hover:bg-accent/50 transition-colors"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <Badge className={getMethodColor(endpoint.method)}>
                      {endpoint.method}
                    </Badge>
                    <code className="text-sm font-mono bg-muted px-2 py-1 rounded">
                      {endpoint.path}
                    </code>
                    {endpoint.isPublic ? (
                      <Globe className="h-4 w-4 text-blue-600" title="Public endpoint" />
                    ) : (
                      <Lock className="h-4 w-4 text-gray-600" title="Private endpoint" />
                    )}
                    {endpoint.requiresAuth && (
                      <CheckCircle className="h-4 w-4 text-green-600" title="Requires authentication" />
                    )}
                  </div>
                  
                  <Badge className={getRiskColor(endpoint.riskLevel)}>
                    {endpoint.riskLevel}
                  </Badge>
                </div>
                
                <p className="text-sm text-muted-foreground mb-3">
                  {endpoint.description}
                </p>
                
                {endpoint.vulnerabilities.length > 0 && (
                  <div className="mb-3">
                    <div className="text-xs font-medium text-red-600 mb-1">Vulnerabilities:</div>
                    <div className="flex flex-wrap gap-1">
                      {endpoint.vulnerabilities.map((vuln, i) => (
                        <Badge key={i} variant="destructive" className="text-xs">
                          {vuln}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
                  <div>
                    <div className="text-muted-foreground">Requests (24h)</div>
                    <div className="font-medium">{endpoint.requestCount24h.toLocaleString()}</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Response Time</div>
                    <div className="font-medium">{endpoint.responseTime.toFixed(3)}s</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Error Rate</div>
                    <div className={`font-medium ${endpoint.errorRate > 1 ? 'text-red-600' : 'text-green-600'}`}>
                      {endpoint.errorRate.toFixed(2)}%
                    </div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Last Accessed</div>
                    <div className="font-medium">{formatTimeAgo(endpoint.lastAccessed)}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
