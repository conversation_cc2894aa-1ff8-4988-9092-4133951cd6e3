'use client';

import * as React from 'react';
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useRealTimeData } from '@/hooks/useRealTimeData';
import { formatTimeAgo } from '@/lib/utils';
import { 
  Shield, 
  AlertTriangle, 
  Globe, 
  RefreshCw,
  ExternalLink,
  Eye,
  Target,
  Activity,
  TrendingUp
} from 'lucide-react';

interface ThreatIntelligence {
  id: string;
  threatType: 'MALWARE' | 'PHISHING' | 'BOTNET' | 'APT' | 'VULNERABILITY' | 'FRAUD';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  title: string;
  description: string;
  source: string;
  confidence: number;
  detectedAt: string;
  indicators: Array<{
    type: 'IP' | 'DOMAIN' | 'URL' | 'HASH' | 'EMAIL';
    value: string;
    context: string;
  }>;
  affectedSystems: string[];
  mitigationStatus: 'NONE' | 'PARTIAL' | 'COMPLETE';
  references: string[];
  tags: string[];
}

interface ThreatFeed {
  feedName: string;
  status: 'ACTIVE' | 'INACTIVE' | 'ERROR';
  lastUpdate: string;
  threatCount: number;
  reliability: number;
}

// Mock data - replace with actual API calls
const mockThreatIntelligence: ThreatIntelligence[] = [
  {
    id: 'threat-1',
    threatType: 'APT',
    severity: 'CRITICAL',
    title: 'Advanced Persistent Threat Targeting Crypto Platforms',
    description: 'Sophisticated attack campaign targeting cryptocurrency analytics platforms with custom malware and social engineering tactics.',
    source: 'CyberThreat Intelligence Feed',
    confidence: 95,
    detectedAt: '2025-01-10T10:30:00Z',
    indicators: [
      { type: 'IP', value: '************', context: 'Command and control server' },
      { type: 'DOMAIN', value: 'crypto-analytics-secure.com', context: 'Typosquatting domain' },
      { type: 'HASH', value: 'a1b2c3d4e5f6...', context: 'Malware payload hash' },
    ],
    affectedSystems: ['API Gateway', 'User Authentication'],
    mitigationStatus: 'PARTIAL',
    references: ['https://threat-intel.example.com/apt-2025-001'],
    tags: ['APT', 'Cryptocurrency', 'Social Engineering'],
  },
  {
    id: 'threat-2',
    threatType: 'VULNERABILITY',
    severity: 'HIGH',
    title: 'Zero-Day Vulnerability in Web Framework',
    description: 'Critical vulnerability discovered in popular web framework used by crypto platforms, allowing remote code execution.',
    source: 'Security Research Community',
    confidence: 88,
    detectedAt: '2025-01-10T09:15:00Z',
    indicators: [
      { type: 'URL', value: '/api/vulnerable-endpoint', context: 'Exploitation vector' },
    ],
    affectedSystems: ['Web Application', 'API Services'],
    mitigationStatus: 'COMPLETE',
    references: ['https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2025-0001'],
    tags: ['Zero-Day', 'RCE', 'Web Framework'],
  },
  {
    id: 'threat-3',
    threatType: 'PHISHING',
    severity: 'MEDIUM',
    title: 'Phishing Campaign Targeting Crypto Analysts',
    description: 'Email phishing campaign impersonating legitimate crypto analytics services to steal credentials.',
    source: 'Email Security Provider',
    confidence: 76,
    detectedAt: '2025-01-10T08:45:00Z',
    indicators: [
      { type: 'EMAIL', value: '<EMAIL>', context: 'Sender address' },
      { type: 'DOMAIN', value: 'crypto-analytics.secure', context: 'Phishing domain' },
    ],
    affectedSystems: ['Email System', 'User Accounts'],
    mitigationStatus: 'COMPLETE',
    references: ['https://phishtank.org/phish_detail.php?phish_id=123456'],
    tags: ['Phishing', 'Email', 'Credential Theft'],
  },
];

const mockThreatFeeds: ThreatFeed[] = [
  {
    feedName: 'CyberThreat Intelligence',
    status: 'ACTIVE',
    lastUpdate: '2025-01-10T11:45:00Z',
    threatCount: 1247,
    reliability: 95,
  },
  {
    feedName: 'MITRE ATT&CK',
    status: 'ACTIVE',
    lastUpdate: '2025-01-10T11:30:00Z',
    threatCount: 892,
    reliability: 98,
  },
  {
    feedName: 'AlienVault OTX',
    status: 'ACTIVE',
    lastUpdate: '2025-01-10T11:15:00Z',
    threatCount: 2156,
    reliability: 87,
  },
  {
    feedName: 'Custom Threat Feed',
    status: 'ERROR',
    lastUpdate: '2025-01-10T10:00:00Z',
    threatCount: 0,
    reliability: 0,
  },
];

export function ThreatIntelligence() {
  const [refreshing, setRefreshing] = useState(false);
  const [selectedThreat, setSelectedThreat] = useState<string | null>(null);

  const { data: threats, isLoading, error, refetch } = useQuery({
    queryKey: ['threat-intelligence'],
    queryFn: async () => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 700));
      return { threats: mockThreatIntelligence, feeds: mockThreatFeeds };
    },
    refetchInterval: 60000, // Refetch every minute
  });

  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'LOW': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'HIGH': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      case 'CRITICAL': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getThreatTypeColor = (type: string) => {
    switch (type) {
      case 'MALWARE': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'PHISHING': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      case 'BOTNET': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      case 'APT': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'VULNERABILITY': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'FRAUD': return 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getMitigationColor = (status: string) => {
    switch (status) {
      case 'NONE': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'PARTIAL': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'COMPLETE': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getFeedStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'text-green-600';
      case 'INACTIVE': return 'text-yellow-600';
      case 'ERROR': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Threat Intelligence</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Badge variant="outline">
              {threats?.threats.length || 0} threats
            </Badge>
            <Button variant="outline" size="sm" onClick={handleRefresh} disabled={refreshing}>
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            {Array.from({ length: 2 }).map((_, i) => (
              <div key={i} className="p-4 border rounded-lg">
                <LoadingSpinner size="sm" />
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="text-center text-muted-foreground py-8">
            <AlertTriangle className="h-5 w-5 mx-auto mb-2" />
            <span>Failed to load threat intelligence</span>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Threat Feeds Status */}
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Threat Feeds</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {threats?.feeds.map((feed, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Activity className={`h-4 w-4 ${getFeedStatusColor(feed.status)}`} />
                      <div>
                        <div className="font-medium text-sm">{feed.feedName}</div>
                        <div className="text-xs text-muted-foreground">
                          {feed.threatCount} threats • {feed.reliability}% reliability
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge className={
                        feed.status === 'ACTIVE' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                        feed.status === 'INACTIVE' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                        'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                      }>
                        {feed.status}
                      </Badge>
                      <div className="text-xs text-muted-foreground mt-1">
                        {formatTimeAgo(feed.lastUpdate)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Active Threats */}
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Active Threats</h4>
              {threats?.threats.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  <Shield className="h-5 w-5 mx-auto mb-2 text-green-600" />
                  <span>No active threats detected</span>
                </div>
              ) : (
                <div className="space-y-3">
                  {threats?.threats.map((threat) => (
                    <div
                      key={threat.id}
                      className="p-4 border rounded-lg hover:bg-accent/50 transition-colors cursor-pointer"
                      onClick={() => setSelectedThreat(selectedThreat === threat.id ? null : threat.id)}
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <Target className="h-4 w-4 text-red-600" />
                          <div>
                            <div className="font-medium text-sm">{threat.title}</div>
                            <div className="text-xs text-muted-foreground">{threat.source}</div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge className={getThreatTypeColor(threat.threatType)}>
                            {threat.threatType}
                          </Badge>
                          <Badge className={getSeverityColor(threat.severity)}>
                            {threat.severity}
                          </Badge>
                        </div>
                      </div>
                      
                      <p className="text-sm text-muted-foreground mb-3">
                        {threat.description}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4 text-xs">
                          <div>
                            <span className="text-muted-foreground">Confidence: </span>
                            <span className="font-medium">{threat.confidence}%</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Mitigation: </span>
                            <Badge className={getMitigationColor(threat.mitigationStatus)}>
                              {threat.mitigationStatus}
                            </Badge>
                          </div>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {formatTimeAgo(threat.detectedAt)}
                        </div>
                      </div>
                      
                      {selectedThreat === threat.id && (
                        <div className="mt-4 pt-4 border-t space-y-3">
                          {/* Indicators */}
                          <div>
                            <div className="text-xs font-medium mb-2">Indicators of Compromise</div>
                            <div className="space-y-1">
                              {threat.indicators.map((indicator, i) => (
                                <div key={i} className="flex items-center justify-between text-xs">
                                  <div className="flex items-center space-x-2">
                                    <Badge variant="outline">{indicator.type}</Badge>
                                    <code className="bg-muted px-1 py-0.5 rounded">{indicator.value}</code>
                                  </div>
                                  <span className="text-muted-foreground">{indicator.context}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                          
                          {/* Affected Systems */}
                          <div>
                            <div className="text-xs font-medium mb-2">Affected Systems</div>
                            <div className="flex flex-wrap gap-1">
                              {threat.affectedSystems.map((system, i) => (
                                <Badge key={i} variant="secondary" className="text-xs">
                                  {system}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          
                          {/* Tags */}
                          <div>
                            <div className="text-xs font-medium mb-2">Tags</div>
                            <div className="flex flex-wrap gap-1">
                              {threat.tags.map((tag, i) => (
                                <Badge key={i} variant="outline" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          
                          {/* References */}
                          {threat.references.length > 0 && (
                            <div>
                              <div className="text-xs font-medium mb-2">References</div>
                              <div className="space-y-1">
                                {threat.references.map((ref, i) => (
                                  <a
                                    key={i}
                                    href={ref}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="flex items-center space-x-1 text-xs text-blue-600 hover:underline"
                                  >
                                    <ExternalLink className="h-3 w-3" />
                                    <span>{ref}</span>
                                  </a>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
