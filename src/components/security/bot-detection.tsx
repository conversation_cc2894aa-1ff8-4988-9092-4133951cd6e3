'use client';

import * as React from 'react';
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useRealTimeData } from '@/hooks/useRealTimeData';
import { formatTimeAgo, formatNumber } from '@/lib/utils';
import { 
  Bot, 
  Shield, 
  AlertTriangle, 
  CheckCircle,
  RefreshCw,
  Activity,
  Eye,
  Clock,
  TrendingUp,
  TrendingDown
} from 'lucide-react';

interface BotDetectionMetrics {
  totalRequestsAnalyzed: number;
  botRequestsDetected: number;
  botRequestsBlocked: number;
  legitimateRequests: number;
  detectionAccuracy: number;
  falsePositiveRate: number;
  topBotSources: Array<{
    ipAddress: string;
    country: string;
    requestCount: number;
    botScore: number;
    userAgent: string;
    blocked: boolean;
  }>;
  detectionPatterns: Array<{
    pattern: string;
    description: string;
    detectionCount: number;
    severity: 'LOW' | 'MEDIUM' | 'HIGH';
  }>;
  hourlyStats: Array<{
    hour: number;
    requests: number;
    botRequests: number;
    blocked: number;
  }>;
}

// Mock data - replace with actual API calls
const mockBotMetrics: BotDetectionMetrics = {
  totalRequestsAnalyzed: 156420,
  botRequestsDetected: 23847,
  botRequestsBlocked: 22156,
  legitimateRequests: 132573,
  detectionAccuracy: 97.8,
  falsePositiveRate: 1.2,
  topBotSources: [
    {
      ipAddress: '*************',
      country: 'Unknown',
      requestCount: 5420,
      botScore: 98.5,
      userAgent: 'Mozilla/5.0 (automated scanner)',
      blocked: true,
    },
    {
      ipAddress: '*********',
      country: 'US',
      requestCount: 3210,
      botScore: 89.2,
      userAgent: 'PostmanRuntime/7.32.3',
      blocked: true,
    },
    {
      ipAddress: '************',
      country: 'CN',
      requestCount: 2890,
      botScore: 76.8,
      userAgent: 'python-requests/2.28.1',
      blocked: false,
    },
    {
      ipAddress: '*************',
      country: 'RU',
      requestCount: 1567,
      botScore: 94.3,
      userAgent: 'curl/7.68.0',
      blocked: true,
    },
  ],
  detectionPatterns: [
    {
      pattern: 'High Request Rate',
      description: 'Requests exceeding normal human interaction patterns',
      detectionCount: 8934,
      severity: 'HIGH',
    },
    {
      pattern: 'Sequential Parameter Testing',
      description: 'Systematic enumeration of API parameters',
      detectionCount: 5621,
      severity: 'MEDIUM',
    },
    {
      pattern: 'Automated User Agent',
      description: 'User agents indicating automated tools',
      detectionCount: 4523,
      severity: 'MEDIUM',
    },
    {
      pattern: 'No JavaScript Execution',
      description: 'Requests without JavaScript fingerprints',
      detectionCount: 3456,
      severity: 'LOW',
    },
    {
      pattern: 'Suspicious Headers',
      description: 'HTTP headers indicating bot behavior',
      detectionCount: 1313,
      severity: 'HIGH',
    },
  ],
  hourlyStats: Array.from({ length: 24 }, (_, i) => ({
    hour: i,
    requests: Math.floor(Math.random() * 10000) + 5000,
    botRequests: Math.floor(Math.random() * 2000) + 500,
    blocked: Math.floor(Math.random() * 1800) + 400,
  })),
};

export function BotDetection() {
  const [refreshing, setRefreshing] = useState(false);
  const [timeRange, setTimeRange] = useState('24h');

  const { data: metrics, isLoading, error, refetch } = useQuery({
    queryKey: ['bot-detection-metrics', timeRange],
    queryFn: async () => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 600));
      return mockBotMetrics;
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'LOW': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'HIGH': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getBotScoreColor = (score: number) => {
    if (score >= 90) return 'text-red-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-green-600';
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Bot className="h-5 w-5" />
            <span>AI Bot Detection</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <LoadingSpinner />
        </CardContent>
      </Card>
    );
  }

  if (error || !metrics) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Bot className="h-5 w-5" />
            <span>AI Bot Detection</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground">
            <AlertTriangle className="h-5 w-5 mx-auto mb-2" />
            <span>Failed to load bot detection metrics</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const botDetectionRate = (metrics.botRequestsDetected / metrics.totalRequestsAnalyzed) * 100;
  const blockingEffectiveness = (metrics.botRequestsBlocked / metrics.botRequestsDetected) * 100;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Bot className="h-5 w-5" />
            <span>AI Bot Detection</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-2 py-1 text-xs border rounded bg-background"
            >
              <option value="1h">1 Hour</option>
              <option value="24h">24 Hours</option>
              <option value="7d">7 Days</option>
            </select>
            <Button variant="outline" size="sm" onClick={handleRefresh} disabled={refreshing}>
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Key Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {formatNumber(metrics.totalRequestsAnalyzed)}
            </div>
            <div className="text-xs text-muted-foreground">Total Requests</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {formatNumber(metrics.botRequestsDetected)}
            </div>
            <div className="text-xs text-muted-foreground">Bots Detected</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {formatNumber(metrics.botRequestsBlocked)}
            </div>
            <div className="text-xs text-muted-foreground">Bots Blocked</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {metrics.detectionAccuracy.toFixed(1)}%
            </div>
            <div className="text-xs text-muted-foreground">Accuracy</div>
          </div>
        </div>

        {/* Performance Metrics */}
        <div className="space-y-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Bot Detection Rate</span>
              <span className="font-medium">{botDetectionRate.toFixed(1)}%</span>
            </div>
            <Progress value={botDetectionRate} className="h-2" />
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Blocking Effectiveness</span>
              <span className="font-medium">{blockingEffectiveness.toFixed(1)}%</span>
            </div>
            <Progress value={blockingEffectiveness} className="h-2" />
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>False Positive Rate</span>
              <span className={`font-medium ${metrics.falsePositiveRate < 2 ? 'text-green-600' : 'text-yellow-600'}`}>
                {metrics.falsePositiveRate.toFixed(1)}%
              </span>
            </div>
            <Progress value={metrics.falsePositiveRate} className="h-2" />
          </div>
        </div>

        {/* Top Bot Sources */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">Top Bot Sources</h4>
          <div className="space-y-2">
            {metrics.topBotSources.map((source, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <code className="text-sm font-mono">{source.ipAddress}</code>
                    <Badge variant="outline" className="text-xs">{source.country}</Badge>
                    {source.blocked ? (
                      <Shield className="h-3 w-3 text-green-600" title="Blocked" />
                    ) : (
                      <Eye className="h-3 w-3 text-yellow-600" title="Monitoring" />
                    )}
                  </div>
                  <div className="text-xs text-muted-foreground truncate">
                    {source.userAgent}
                  </div>
                </div>
                <div className="text-right">
                  <div className={`text-sm font-medium ${getBotScoreColor(source.botScore)}`}>
                    {source.botScore.toFixed(1)}%
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {formatNumber(source.requestCount)} req
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Detection Patterns */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">Detection Patterns</h4>
          <div className="space-y-2">
            {metrics.detectionPatterns.map((pattern, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="font-medium text-sm">{pattern.pattern}</span>
                    <Badge className={getSeverityColor(pattern.severity)}>
                      {pattern.severity}
                    </Badge>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {pattern.description}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">
                    {formatNumber(pattern.detectionCount)}
                  </div>
                  <div className="text-xs text-muted-foreground">detections</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Status Summary */}
        <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg">
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <div>
              <div className="font-medium text-sm text-green-800 dark:text-green-300">
                Bot Detection Active
              </div>
              <div className="text-xs text-green-700 dark:text-green-400">
                AI-powered protection is monitoring all requests
              </div>
            </div>
          </div>
          <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
            Operational
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
}
