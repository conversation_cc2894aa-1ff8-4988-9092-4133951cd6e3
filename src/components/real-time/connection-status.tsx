'use client';

import * as React from 'react';
import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useRealTimeData } from '@/hooks/useRealTimeData';
import { formatTimeAgo } from '@/lib/utils';
import { 
  Wifi, 
  WifiOff, 
  Activity, 
  Clock, 
  Zap,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Signal
} from 'lucide-react';

interface ConnectionStatusProps {
  className?: string;
  showDetails?: boolean;
  showMetrics?: boolean;
}

export function ConnectionStatus({ 
  className, 
  showDetails = false, 
  showMetrics = false 
}: ConnectionStatusProps) {
  const { 
    isConnected, 
    isSubscribed, 
    connectionState, 
    lastUpdate,
    subscribeToStreams,
    unsubscribeFromStreams 
  } = useRealTimeData();

  const [connectionTime, setConnectionTime] = useState<number>(0);
  const [reconnectAttempts, setReconnectAttempts] = useState<number>(0);
  const [latency, setLatency] = useState<number>(0);
  const [messageCount, setMessageCount] = useState<number>(0);

  // Simulate connection metrics (in real app, these would come from WebSocket manager)
  useEffect(() => {
    if (isConnected) {
      const interval = setInterval(() => {
        setConnectionTime(prev => prev + 1);
        setLatency(Math.random() * 50 + 10); // 10-60ms
        setMessageCount(prev => prev + Math.floor(Math.random() * 3));
      }, 1000);

      return () => clearInterval(interval);
    } else {
      setConnectionTime(0);
    }
  }, [isConnected]);

  const getStatusIcon = () => {
    switch (connectionState) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'connecting':
      case 'reconnecting':
        return <RefreshCw className="h-4 w-4 text-yellow-600 animate-spin" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <WifiOff className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = () => {
    switch (connectionState) {
      case 'connected':
        return 'bg-green-500';
      case 'connecting':
      case 'reconnecting':
        return 'bg-yellow-500 animate-pulse';
      case 'error':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusText = () => {
    switch (connectionState) {
      case 'connected':
        return 'Connected';
      case 'connecting':
        return 'Connecting...';
      case 'reconnecting':
        return 'Reconnecting...';
      case 'error':
        return 'Connection Error';
      case 'disconnected':
        return 'Disconnected';
      default:
        return 'Unknown';
    }
  };

  const handleReconnect = () => {
    if (!isConnected) {
      setReconnectAttempts(prev => prev + 1);
      subscribeToStreams();
    }
  };

  const handleDisconnect = () => {
    if (isConnected) {
      unsubscribeFromStreams();
    }
  };

  if (!showDetails && !showMetrics) {
    // Simple status indicator
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className={`h-2 w-2 rounded-full ${getStatusColor()}`} />
        <span className="text-sm text-muted-foreground">
          {getStatusText()}
        </span>
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Signal className="h-5 w-5" />
          <span>Connection Status</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Main Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getStatusIcon()}
            <div>
              <div className="font-medium">{getStatusText()}</div>
              {lastUpdate && (
                <div className="text-xs text-muted-foreground">
                  Last update: {formatTimeAgo(lastUpdate)}
                </div>
              )}
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Badge variant={isSubscribed ? 'default' : 'secondary'}>
              {isSubscribed ? 'Subscribed' : 'Not Subscribed'}
            </Badge>
            
            {isConnected ? (
              <Button variant="outline" size="sm" onClick={handleDisconnect}>
                Disconnect
              </Button>
            ) : (
              <Button variant="outline" size="sm" onClick={handleReconnect}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Reconnect
              </Button>
            )}
          </div>
        </div>

        {/* Connection Details */}
        {showDetails && (
          <div className="grid grid-cols-2 gap-4 pt-4 border-t">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Connection Time</span>
              </div>
              <div className="text-lg font-medium">
                {connectionTime > 0 ? `${Math.floor(connectionTime / 60)}:${(connectionTime % 60).toString().padStart(2, '0')}` : '--:--'}
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <RefreshCw className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Reconnect Attempts</span>
              </div>
              <div className="text-lg font-medium">{reconnectAttempts}</div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Zap className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Latency</span>
              </div>
              <div className="text-lg font-medium">
                {isConnected ? `${latency.toFixed(0)}ms` : '--'}
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Activity className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Messages</span>
              </div>
              <div className="text-lg font-medium">{messageCount}</div>
            </div>
          </div>
        )}

        {/* Performance Metrics */}
        {showMetrics && isConnected && (
          <div className="space-y-3 pt-4 border-t">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Connection Quality</span>
                <span className="font-medium">
                  {latency < 50 ? 'Excellent' : latency < 100 ? 'Good' : 'Fair'}
                </span>
              </div>
              <Progress 
                value={Math.max(0, 100 - latency)} 
                className="h-2"
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Data Flow</span>
                <span className="font-medium">
                  {messageCount > 100 ? 'High' : messageCount > 50 ? 'Medium' : 'Low'}
                </span>
              </div>
              <Progress 
                value={Math.min(100, (messageCount / 200) * 100)} 
                className="h-2"
              />
            </div>

            <div className="grid grid-cols-3 gap-2 text-xs text-muted-foreground">
              <div className="text-center">
                <div className="font-medium text-foreground">{(messageCount * 0.8).toFixed(0)}</div>
                <div>Received</div>
              </div>
              <div className="text-center">
                <div className="font-medium text-foreground">{(messageCount * 0.2).toFixed(0)}</div>
                <div>Sent</div>
              </div>
              <div className="text-center">
                <div className="font-medium text-foreground">0</div>
                <div>Errors</div>
              </div>
            </div>
          </div>
        )}

        {/* Connection Health Indicator */}
        <div className="flex items-center space-x-2 pt-2 border-t">
          <div className={`h-3 w-3 rounded-full ${getStatusColor()}`} />
          <span className="text-sm">
            {isConnected && isSubscribed 
              ? 'Real-time data streaming active'
              : isConnected 
                ? 'Connected but not receiving data'
                : 'No real-time connection'
            }
          </span>
        </div>
      </CardContent>
    </Card>
  );
}
