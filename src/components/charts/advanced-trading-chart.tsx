'use client';

import * as React from 'react';
import { useEffect, useRef, useState, useCallback } from 'react';
import { 
  create<PERSON>hart, 
  IChartApi, 
  ISeriesApi, 
  CandlestickData,
  LineData,
  HistogramData,
  ColorType,
  CrosshairMode,
  UTCTimestamp
} from 'lightweight-charts';
import { useTheme } from 'next-themes';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useWebSocket } from '@/hooks/useWebSocket';
import { useTechnicalIndicators } from './technical-indicators';
import { formatCurrency, formatPercentage, getChangeColor } from '@/lib/utils';
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3, 
  Maximize2,
  Settings,
  RefreshCw,
  Plus,
  Minus,
  Activity,
  Target
} from 'lucide-react';

interface AdvancedTradingChartProps {
  tokenAddress: string;
  symbol: string;
  className?: string;
  height?: number;
  showControls?: boolean;
  enableIndicators?: boolean;
  timeframe?: '1m' | '5m' | '15m' | '1h' | '4h' | '1d';
}

interface ChartData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface IndicatorConfig {
  id: string;
  name: string;
  enabled: boolean;
  color: string;
  paneId?: string;
}

const DEFAULT_INDICATORS: IndicatorConfig[] = [
  { id: 'sma20', name: 'SMA 20', enabled: false, color: '#ff6b6b' },
  { id: 'sma50', name: 'SMA 50', enabled: false, color: '#4ecdc4' },
  { id: 'ema12', name: 'EMA 12', enabled: false, color: '#45b7d1' },
  { id: 'ema26', name: 'EMA 26', enabled: false, color: '#f9ca24' },
  { id: 'rsi', name: 'RSI', enabled: false, color: '#6c5ce7', paneId: 'rsi' },
  { id: 'macd', name: 'MACD', enabled: false, color: '#a29bfe', paneId: 'macd' },
  { id: 'bollinger', name: 'Bollinger Bands', enabled: false, color: '#fd79a8' },
];

// Enhanced mock data generator with more realistic patterns
const generateAdvancedMockData = (days: number = 30, timeframe: string = '1d'): ChartData[] => {
  const data: ChartData[] = [];
  let price = 100 + Math.random() * 50;
  
  const now = Date.now();
  const intervals = {
    '1m': 60 * 1000,
    '5m': 5 * 60 * 1000,
    '15m': 15 * 60 * 1000,
    '1h': 60 * 60 * 1000,
    '4h': 4 * 60 * 60 * 1000,
    '1d': 24 * 60 * 60 * 1000,
  };
  
  const interval = intervals[timeframe as keyof typeof intervals] || intervals['1d'];
  const totalPoints = Math.floor((days * 24 * 60 * 60 * 1000) / interval);
  
  for (let i = totalPoints; i >= 0; i--) {
    const time = now - i * interval;
    const volatility = 0.01 + Math.random() * 0.02;
    
    // Add some trend and pattern
    const trendFactor = Math.sin((totalPoints - i) / totalPoints * Math.PI * 2) * 0.1;
    const noiseFactor = (Math.random() - 0.5) * volatility;
    
    const open = price;
    const change = price * (trendFactor + noiseFactor);
    const close = Math.max(0.01, open + change);
    
    // Generate realistic high/low based on volatility
    const range = Math.abs(close - open) + price * volatility * Math.random();
    const high = Math.max(open, close) + range * Math.random();
    const low = Math.min(open, close) - range * Math.random();
    
    // Volume with some correlation to price movement
    const priceChangePercent = Math.abs((close - open) / open);
    const baseVolume = 1000000 + Math.random() * 2000000;
    const volumeMultiplier = 1 + priceChangePercent * 5; // Higher volume on bigger moves
    const volume = baseVolume * volumeMultiplier;
    
    data.push({
      timestamp: Math.floor(time / 1000),
      open,
      high,
      low,
      close,
      volume,
    });
    
    price = close;
  }
  
  return data.sort((a, b) => a.timestamp - b.timestamp);
};

export function AdvancedTradingChart({
  tokenAddress,
  symbol,
  className,
  height = 600,
  showControls = true,
  enableIndicators = true,
  timeframe = '1d',
}: AdvancedTradingChartProps) {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const candlestickSeriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null);
  const volumeSeriesRef = useRef<ISeriesApi<'Histogram'> | null>(null);
  const indicatorSeriesRef = useRef<Map<string, ISeriesApi<any>>>(new Map());
  
  const { theme } = useTheme();
  const [isLoading, setIsLoading] = useState(true);
  const [chartData, setChartData] = useState<ChartData[]>([]);
  const [indicators, setIndicators] = useState<IndicatorConfig[]>(DEFAULT_INDICATORS);
  const [currentTimeframe, setCurrentTimeframe] = useState(timeframe);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [currentPrice, setCurrentPrice] = useState<number>(0);
  const [priceChange24h, setPriceChange24h] = useState<number>(0);

  // Calculate technical indicators
  const enabledIndicators = indicators.filter(ind => ind.enabled).map(ind => ind.id);
  const technicalData = useTechnicalIndicators(chartData, enabledIndicators);

  // WebSocket connection for real-time updates
  const { lastMessage, isConnected } = useWebSocket({
    url: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000/ws',
    onMessage: (data) => {
      if (data.type === 'price_update' && data.tokenAddress === tokenAddress) {
        updateRealTimePrice(data);
      }
    },
  });

  const updateRealTimePrice = useCallback((priceData: any) => {
    if (!candlestickSeriesRef.current || chartData.length === 0) return;

    const lastCandle = chartData[chartData.length - 1];
    const newPrice = priceData.price;
    
    const updatedCandle: CandlestickData = {
      time: Math.floor(Date.now() / 1000) as UTCTimestamp,
      open: lastCandle.close,
      high: Math.max(lastCandle.close, newPrice),
      low: Math.min(lastCandle.close, newPrice),
      close: newPrice,
    };

    candlestickSeriesRef.current.update(updatedCandle);
    setCurrentPrice(newPrice);
    setPriceChange24h(priceData.change24h || 0);
  }, [chartData]);

  const initializeChart = useCallback(() => {
    if (!chartContainerRef.current) return;

    const isDark = theme === 'dark';
    
    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      height,
      layout: {
        background: { type: ColorType.Solid, color: isDark ? '#0a0a0a' : '#ffffff' },
        textColor: isDark ? '#d1d5db' : '#374151',
      },
      grid: {
        vertLines: { color: isDark ? '#1f2937' : '#f3f4f6' },
        horzLines: { color: isDark ? '#1f2937' : '#f3f4f6' },
      },
      crosshair: {
        mode: CrosshairMode.Normal,
        vertLine: {
          width: 1,
          color: isDark ? '#6b7280' : '#9ca3af',
          style: 1,
        },
        horzLine: {
          width: 1,
          color: isDark ? '#6b7280' : '#9ca3af',
          style: 1,
        },
      },
      rightPriceScale: {
        borderColor: isDark ? '#374151' : '#d1d5db',
        scaleMargins: {
          top: 0.1,
          bottom: 0.3,
        },
      },
      timeScale: {
        borderColor: isDark ? '#374151' : '#d1d5db',
        timeVisible: true,
        secondsVisible: false,
        rightOffset: 12,
        barSpacing: 8,
        fixLeftEdge: true,
        lockVisibleTimeRangeOnResize: true,
      },
      handleScroll: {
        mouseWheel: true,
        pressedMouseMove: true,
        horzTouchDrag: true,
        vertTouchDrag: true,
      },
      handleScale: {
        axisPressedMouseMove: true,
        mouseWheel: true,
        pinch: true,
      },
    });

    // Add candlestick series
    const candlestickSeries = chart.addCandlestickSeries({
      upColor: '#22c55e',
      downColor: '#ef4444',
      borderDownColor: '#ef4444',
      borderUpColor: '#22c55e',
      wickDownColor: '#ef4444',
      wickUpColor: '#22c55e',
      priceFormat: {
        type: 'price',
        precision: 6,
        minMove: 0.000001,
      },
    });

    // Add volume series
    const volumeSeries = chart.addHistogramSeries({
      color: '#6b7280',
      priceFormat: {
        type: 'volume',
      },
      priceScaleId: 'volume',
      scaleMargins: {
        top: 0.7,
        bottom: 0,
      },
    });

    // Configure volume price scale
    chart.priceScale('volume').applyOptions({
      scaleMargins: {
        top: 0.7,
        bottom: 0,
      },
    });

    chartRef.current = chart;
    candlestickSeriesRef.current = candlestickSeries;
    volumeSeriesRef.current = volumeSeries;

    // Load initial data
    loadChartData();
  }, [theme, height]);

  const loadChartData = useCallback(async () => {
    setIsLoading(true);
    
    try {
      // Simulate API call with more realistic data
      await new Promise(resolve => setTimeout(resolve, 800));
      const data = generateAdvancedMockData(30, currentTimeframe);
      
      if (candlestickSeriesRef.current && volumeSeriesRef.current) {
        // Set candlestick data
        const candlestickData = data.map(d => ({
          time: d.timestamp as UTCTimestamp,
          open: d.open,
          high: d.high,
          low: d.low,
          close: d.close,
        }));
        
        candlestickSeriesRef.current.setData(candlestickData);
        
        // Set volume data with colors based on price movement
        const volumeData = data.map(d => ({
          time: d.timestamp as UTCTimestamp,
          value: d.volume,
          color: d.close > d.open ? '#22c55e80' : '#ef444480',
        }));
        
        volumeSeriesRef.current.setData(volumeData);
      }
      
      setChartData(data);
      
      if (data.length > 0) {
        const lastPrice = data[data.length - 1].close;
        const firstPrice = data[0].close;
        setCurrentPrice(lastPrice);
        setPriceChange24h(((lastPrice - firstPrice) / firstPrice) * 100);
      }
    } catch (error) {
      console.error('Failed to load chart data:', error);
    } finally {
      setIsLoading(false);
    }
  }, [currentTimeframe]);

  const toggleIndicator = useCallback((indicatorId: string) => {
    setIndicators(prev => prev.map(ind => 
      ind.id === indicatorId ? { ...ind, enabled: !ind.enabled } : ind
    ));
  }, []);

  const handleTimeframeChange = useCallback((newTimeframe: typeof timeframe) => {
    setCurrentTimeframe(newTimeframe);
  }, []);

  // Update indicators when they change
  useEffect(() => {
    if (!chartRef.current || !technicalData) return;

    // Clear existing indicator series
    indicatorSeriesRef.current.forEach(series => {
      chartRef.current?.removeSeries(series);
    });
    indicatorSeriesRef.current.clear();

    // Add enabled indicators
    indicators.forEach(indicator => {
      if (!indicator.enabled || !technicalData[indicator.id]) return;

      const data = technicalData[indicator.id];
      
      if (indicator.id === 'bollinger' && data.upper && data.middle && data.lower) {
        // Bollinger Bands - three lines
        const upperSeries = chartRef.current!.addLineSeries({
          color: indicator.color,
          lineWidth: 1,
          lineStyle: 2, // Dashed
        });
        const middleSeries = chartRef.current!.addLineSeries({
          color: indicator.color,
          lineWidth: 2,
        });
        const lowerSeries = chartRef.current!.addLineSeries({
          color: indicator.color,
          lineWidth: 1,
          lineStyle: 2, // Dashed
        });
        
        upperSeries.setData(data.upper);
        middleSeries.setData(data.middle);
        lowerSeries.setData(data.lower);
        
        indicatorSeriesRef.current.set(`${indicator.id}_upper`, upperSeries);
        indicatorSeriesRef.current.set(`${indicator.id}_middle`, middleSeries);
        indicatorSeriesRef.current.set(`${indicator.id}_lower`, lowerSeries);
      } else if (Array.isArray(data)) {
        // Simple line indicators
        const series = chartRef.current!.addLineSeries({
          color: indicator.color,
          lineWidth: 2,
          priceScaleId: indicator.paneId || 'right',
        });
        
        series.setData(data);
        indicatorSeriesRef.current.set(indicator.id, series);
      }
    });
  }, [indicators, technicalData]);

  // Initialize chart
  useEffect(() => {
    initializeChart();
    
    return () => {
      if (chartRef.current) {
        chartRef.current.remove();
        chartRef.current = null;
      }
    };
  }, [initializeChart]);

  // Handle resize
  useEffect(() => {
    const handleResize = () => {
      if (chartRef.current && chartContainerRef.current) {
        chartRef.current.applyOptions({
          width: chartContainerRef.current.clientWidth,
        });
      }
    };

    const resizeObserver = new ResizeObserver(handleResize);
    if (chartContainerRef.current) {
      resizeObserver.observe(chartContainerRef.current);
    }
    
    return () => resizeObserver.disconnect();
  }, []);

  // Reload data when timeframe changes
  useEffect(() => {
    if (chartRef.current) {
      loadChartData();
    }
  }, [loadChartData]);

  const timeframes = [
    { value: '1m', label: '1m' },
    { value: '5m', label: '5m' },
    { value: '15m', label: '15m' },
    { value: '1h', label: '1h' },
    { value: '4h', label: '4h' },
    { value: '1d', label: '1d' },
  ];

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>{symbol} Advanced Chart</span>
            </CardTitle>
            <div className="flex items-center space-x-4 mt-2">
              <div className="text-2xl font-bold">
                {formatCurrency(currentPrice)}
              </div>
              <div className={`flex items-center space-x-1 ${getChangeColor(priceChange24h)}`}>
                {priceChange24h > 0 ? (
                  <TrendingUp className="h-4 w-4" />
                ) : (
                  <TrendingDown className="h-4 w-4" />
                )}
                <span>{formatPercentage(Math.abs(priceChange24h))}</span>
              </div>
              <div className="flex items-center space-x-1 text-xs">
                <div className={`h-2 w-2 rounded-full ${
                  isConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'
                }`} />
                <span className="text-muted-foreground">
                  {isConnected ? 'Live' : 'Static'}
                </span>
              </div>
            </div>
          </div>
          
          {showControls && (
            <div className="flex items-center space-x-2">
              {/* Timeframe selector */}
              <div className="flex items-center space-x-1">
                {timeframes.map(tf => (
                  <Button
                    key={tf.value}
                    variant={currentTimeframe === tf.value ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleTimeframeChange(tf.value as any)}
                  >
                    {tf.label}
                  </Button>
                ))}
              </div>
              
              <Button variant="outline" size="sm" onClick={loadChartData}>
                <RefreshCw className="h-4 w-4" />
              </Button>
              
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
              
              <Button variant="outline" size="sm" onClick={() => setIsFullscreen(!isFullscreen)}>
                <Maximize2 className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
        
        {/* Technical Indicators */}
        {enableIndicators && (
          <div className="flex flex-wrap gap-2 mt-4">
            {indicators.map(indicator => (
              <Button
                key={indicator.id}
                variant={indicator.enabled ? 'default' : 'outline'}
                size="sm"
                onClick={() => toggleIndicator(indicator.id)}
                className="text-xs"
              >
                <div 
                  className="w-2 h-2 rounded-full mr-2" 
                  style={{ backgroundColor: indicator.color }}
                />
                {indicator.name}
              </Button>
            ))}
          </div>
        )}
      </CardHeader>
      
      <CardContent>
        <div className="relative">
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-background/80 z-10">
              <LoadingSpinner />
            </div>
          )}
          
          <div
            ref={chartContainerRef}
            className="w-full"
            style={{ height: `${height}px` }}
          />
        </div>
      </CardContent>
    </Card>
  );
}
