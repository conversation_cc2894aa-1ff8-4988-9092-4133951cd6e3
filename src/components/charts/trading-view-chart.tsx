'use client';

import * as React from 'react';
import { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import {
  create<PERSON>hart,
  IChartApi,
  ISeriesApi,
  CandlestickData,
  LineData,
  HistogramData,
  ColorType,
  CrosshairMode,
  LineStyle,
  PriceScaleMode,
  Time,
  UTCTimestamp
} from 'lightweight-charts';
import { useTheme } from 'next-themes';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useWebSocket } from '@/hooks/useWebSocket';
import { formatCurrency, formatPercentage, getChangeColor } from '@/lib/utils';
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3, 
  Maximize2,
  Settings,
  RefreshCw
} from 'lucide-react';
import type { CandlestickData as CandlestickDataType } from '@/lib/types';

interface TradingViewChartProps {
  tokenAddress: string;
  symbol: string;
  className?: string;
  height?: number;
  showControls?: boolean;
  autoResize?: boolean;
}

interface ChartData {
  candlesticks: CandlestickDataType[];
  volume: { time: number; value: number; color?: string }[];
  currentPrice: number;
  priceChange24h: number;
  volume24h: number;
}

// Mock data generator for demonstration
const generateMockData = (days: number = 30): ChartData => {
  const data: CandlestickDataType[] = [];
  const volume: { time: number; value: number; color?: string }[] = [];
  let price = 100 + Math.random() * 50;
  
  const now = Date.now();
  const dayMs = 24 * 60 * 60 * 1000;
  
  for (let i = days; i >= 0; i--) {
    const time = Math.floor((now - i * dayMs) / 1000);
    const volatility = 0.02 + Math.random() * 0.03;
    
    const open = price;
    const change = (Math.random() - 0.5) * price * volatility;
    const close = Math.max(0.01, open + change);
    const high = Math.max(open, close) * (1 + Math.random() * 0.02);
    const low = Math.min(open, close) * (1 - Math.random() * 0.02);
    const vol = 1000000 + Math.random() * 5000000;
    
    data.push({
      timestamp: time,
      open,
      high,
      low,
      close,
      volume: vol,
    });
    
    volume.push({
      time,
      value: vol,
      color: close > open ? '#22c55e' : '#ef4444',
    });
    
    price = close;
  }
  
  const currentPrice = data[data.length - 1]?.close || 0;
  const previousPrice = data[data.length - 2]?.close || currentPrice;
  const priceChange24h = ((currentPrice - previousPrice) / previousPrice) * 100;
  const volume24h = data[data.length - 1]?.volume || 0;
  
  return {
    candlesticks: data,
    volume,
    currentPrice,
    priceChange24h,
    volume24h,
  };
};

export function TradingViewChart({
  tokenAddress,
  symbol,
  className,
  height = 400,
  showControls = true,
  autoResize = true,
}: TradingViewChartProps) {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const candlestickSeriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null);
  const volumeSeriesRef = useRef<ISeriesApi<'Histogram'> | null>(null);
  
  const { theme } = useTheme();
  const [isLoading, setIsLoading] = useState(true);
  const [chartData, setChartData] = useState<ChartData | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // WebSocket connection for real-time price updates
  const { lastMessage, isConnected } = useWebSocket({
    url: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000/ws',
    onMessage: (data) => {
      if (data.type === 'price_update' && data.tokenAddress === tokenAddress) {
        updateRealTimePrice(data);
      }
    },
  });

  const updateRealTimePrice = (priceData: any) => {
    if (!candlestickSeriesRef.current || !chartData) return;

    const newCandle: CandlestickData = {
      time: Math.floor(Date.now() / 1000),
      open: chartData.currentPrice,
      high: Math.max(chartData.currentPrice, priceData.price),
      low: Math.min(chartData.currentPrice, priceData.price),
      close: priceData.price,
    };

    candlestickSeriesRef.current.update(newCandle);
    
    setChartData(prev => prev ? {
      ...prev,
      currentPrice: priceData.price,
      priceChange24h: priceData.change24h || prev.priceChange24h,
    } : null);
  };

  const initializeChart = () => {
    if (!chartContainerRef.current) return;

    const isDark = theme === 'dark';
    
    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      height,
      layout: {
        background: { type: ColorType.Solid, color: isDark ? '#0a0a0a' : '#ffffff' },
        textColor: isDark ? '#d1d5db' : '#374151',
      },
      grid: {
        vertLines: { color: isDark ? '#1f2937' : '#f3f4f6' },
        horzLines: { color: isDark ? '#1f2937' : '#f3f4f6' },
      },
      crosshair: {
        mode: CrosshairMode.Normal,
      },
      rightPriceScale: {
        borderColor: isDark ? '#374151' : '#d1d5db',
        scaleMargins: {
          top: 0.1,
          bottom: 0.3,
        },
      },
      timeScale: {
        borderColor: isDark ? '#374151' : '#d1d5db',
        timeVisible: true,
        secondsVisible: false,
      },
      handleScroll: {
        mouseWheel: true,
        pressedMouseMove: true,
      },
      handleScale: {
        axisPressedMouseMove: true,
        mouseWheel: true,
        pinch: true,
      },
    });

    // Add candlestick series
    const candlestickSeries = chart.addCandlestickSeries({
      upColor: '#22c55e',
      downColor: '#ef4444',
      borderDownColor: '#ef4444',
      borderUpColor: '#22c55e',
      wickDownColor: '#ef4444',
      wickUpColor: '#22c55e',
    });

    // Add volume series
    const volumeSeries = chart.addHistogramSeries({
      color: '#6b7280',
      priceFormat: {
        type: 'volume',
      },
      priceScaleId: 'volume',
      scaleMargins: {
        top: 0.7,
        bottom: 0,
      },
    });

    chart.priceScale('volume').applyOptions({
      scaleMargins: {
        top: 0.7,
        bottom: 0,
      },
    });

    chartRef.current = chart;
    candlestickSeriesRef.current = candlestickSeries;
    volumeSeriesRef.current = volumeSeries;

    // Load initial data
    loadChartData();
  };

  const loadChartData = async () => {
    setIsLoading(true);
    
    try {
      // Simulate API call - replace with actual data fetching
      await new Promise(resolve => setTimeout(resolve, 1000));
      const data = generateMockData();
      
      if (candlestickSeriesRef.current && volumeSeriesRef.current) {
        candlestickSeriesRef.current.setData(
          data.candlesticks.map(d => ({
            time: d.timestamp,
            open: d.open,
            high: d.high,
            low: d.low,
            close: d.close,
          }))
        );
        
        volumeSeriesRef.current.setData(data.volume);
      }
      
      setChartData(data);
    } catch (error) {
      console.error('Failed to load chart data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResize = () => {
    if (chartRef.current && chartContainerRef.current) {
      chartRef.current.applyOptions({
        width: chartContainerRef.current.clientWidth,
      });
    }
  };

  const refreshChart = () => {
    loadChartData();
  };

  // Initialize chart
  useEffect(() => {
    initializeChart();
    
    return () => {
      if (chartRef.current) {
        chartRef.current.remove();
        chartRef.current = null;
      }
    };
  }, [theme]);

  // Handle resize
  useEffect(() => {
    if (!autoResize) return;
    
    const resizeObserver = new ResizeObserver(handleResize);
    if (chartContainerRef.current) {
      resizeObserver.observe(chartContainerRef.current);
    }
    
    return () => resizeObserver.disconnect();
  }, [autoResize]);

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>{symbol} Price Chart</span>
            </CardTitle>
            {chartData && (
              <div className="flex items-center space-x-4 mt-2">
                <div className="text-2xl font-bold">
                  {formatCurrency(chartData.currentPrice)}
                </div>
                <div className={`flex items-center space-x-1 ${getChangeColor(chartData.priceChange24h)}`}>
                  {chartData.priceChange24h > 0 ? (
                    <TrendingUp className="h-4 w-4" />
                  ) : (
                    <TrendingDown className="h-4 w-4" />
                  )}
                  <span>{formatPercentage(Math.abs(chartData.priceChange24h))}</span>
                </div>
                <Badge variant="outline">
                  Vol: {formatCurrency(chartData.volume24h)}
                </Badge>
              </div>
            )}
          </div>
          
          {showControls && (
            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-1 text-xs">
                <div className={`h-2 w-2 rounded-full ${
                  isConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'
                }`} />
                <span className="text-muted-foreground">
                  {isConnected ? 'Live' : 'Static'}
                </span>
              </div>
              
              <Button variant="outline" size="sm" onClick={refreshChart}>
                <RefreshCw className="h-4 w-4" />
              </Button>
              
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
              
              <Button variant="outline" size="sm" onClick={() => setIsFullscreen(!isFullscreen)}>
                <Maximize2 className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="relative">
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-background/80 z-10">
              <LoadingSpinner />
            </div>
          )}
          
          <div
            ref={chartContainerRef}
            className="w-full"
            style={{ height: `${height}px` }}
          />
        </div>
      </CardContent>
    </Card>
  );
}
