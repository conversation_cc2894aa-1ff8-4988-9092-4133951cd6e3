'use client';

import * as React from 'react';
import { useMemo } from 'react';

// Technical Analysis Calculations
export interface TechnicalIndicators {
  sma: (data: number[], period: number) => number[];
  ema: (data: number[], period: number) => number[];
  rsi: (data: number[], period?: number) => number[];
  macd: (data: number[], fastPeriod?: number, slowPeriod?: number, signalPeriod?: number) => {
    macd: number[];
    signal: number[];
    histogram: number[];
  };
  bollinger: (data: number[], period?: number, stdDev?: number) => {
    upper: number[];
    middle: number[];
    lower: number[];
  };
  stochastic: (high: number[], low: number[], close: number[], kPeriod?: number, dPeriod?: number) => {
    k: number[];
    d: number[];
  };
}

export const technicalIndicators: TechnicalIndicators = {
  // Simple Moving Average
  sma: (data: number[], period: number): number[] => {
    const result: number[] = [];
    for (let i = period - 1; i < data.length; i++) {
      const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
      result.push(sum / period);
    }
    return result;
  },

  // Exponential Moving Average
  ema: (data: number[], period: number): number[] => {
    const result: number[] = [];
    const multiplier = 2 / (period + 1);
    
    // Start with SMA for first value
    const sma = data.slice(0, period).reduce((a, b) => a + b, 0) / period;
    result.push(sma);
    
    for (let i = period; i < data.length; i++) {
      const ema = (data[i] - result[result.length - 1]) * multiplier + result[result.length - 1];
      result.push(ema);
    }
    
    return result;
  },

  // Relative Strength Index
  rsi: (data: number[], period: number = 14): number[] => {
    const result: number[] = [];
    const gains: number[] = [];
    const losses: number[] = [];
    
    // Calculate price changes
    for (let i = 1; i < data.length; i++) {
      const change = data[i] - data[i - 1];
      gains.push(change > 0 ? change : 0);
      losses.push(change < 0 ? Math.abs(change) : 0);
    }
    
    // Calculate RSI
    for (let i = period - 1; i < gains.length; i++) {
      const avgGain = gains.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period;
      const avgLoss = losses.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period;
      
      if (avgLoss === 0) {
        result.push(100);
      } else {
        const rs = avgGain / avgLoss;
        const rsi = 100 - (100 / (1 + rs));
        result.push(rsi);
      }
    }
    
    return result;
  },

  // MACD (Moving Average Convergence Divergence)
  macd: (data: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9) => {
    const fastEMA = technicalIndicators.ema(data, fastPeriod);
    const slowEMA = technicalIndicators.ema(data, slowPeriod);
    
    // Align arrays (slowEMA is shorter)
    const alignedFastEMA = fastEMA.slice(slowPeriod - fastPeriod);
    
    // Calculate MACD line
    const macdLine = alignedFastEMA.map((fast, i) => fast - slowEMA[i]);
    
    // Calculate Signal line (EMA of MACD)
    const signalLine = technicalIndicators.ema(macdLine, signalPeriod);
    
    // Calculate Histogram (MACD - Signal)
    const alignedMacdLine = macdLine.slice(signalPeriod - 1);
    const histogram = alignedMacdLine.map((macd, i) => macd - signalLine[i]);
    
    return {
      macd: alignedMacdLine,
      signal: signalLine,
      histogram: histogram,
    };
  },

  // Bollinger Bands
  bollinger: (data: number[], period: number = 20, stdDev: number = 2) => {
    const sma = technicalIndicators.sma(data, period);
    const upper: number[] = [];
    const lower: number[] = [];
    
    for (let i = period - 1; i < data.length; i++) {
      const slice = data.slice(i - period + 1, i + 1);
      const mean = sma[i - period + 1];
      const variance = slice.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / period;
      const standardDeviation = Math.sqrt(variance);
      
      upper.push(mean + (standardDeviation * stdDev));
      lower.push(mean - (standardDeviation * stdDev));
    }
    
    return {
      upper,
      middle: sma,
      lower,
    };
  },

  // Stochastic Oscillator
  stochastic: (high: number[], low: number[], close: number[], kPeriod: number = 14, dPeriod: number = 3) => {
    const k: number[] = [];
    
    for (let i = kPeriod - 1; i < close.length; i++) {
      const highestHigh = Math.max(...high.slice(i - kPeriod + 1, i + 1));
      const lowestLow = Math.min(...low.slice(i - kPeriod + 1, i + 1));
      const currentClose = close[i];
      
      const kValue = ((currentClose - lowestLow) / (highestHigh - lowestLow)) * 100;
      k.push(kValue);
    }
    
    const d = technicalIndicators.sma(k, dPeriod);
    
    return { k, d };
  },
};

// Hook for calculating technical indicators
export function useTechnicalIndicators(
  data: Array<{ open: number; high: number; low: number; close: number; timestamp: number }>,
  indicators: string[] = ['sma', 'ema', 'rsi']
) {
  return useMemo(() => {
    if (!data || data.length === 0) return {};
    
    const closes = data.map(d => d.close);
    const highs = data.map(d => d.high);
    const lows = data.map(d => d.low);
    const timestamps = data.map(d => d.timestamp);
    
    const results: Record<string, any> = {};
    
    indicators.forEach(indicator => {
      switch (indicator) {
        case 'sma20':
          results.sma20 = technicalIndicators.sma(closes, 20).map((value, i) => ({
            time: timestamps[i + 19] as UTCTimestamp,
            value,
          }));
          break;
        case 'sma50':
          results.sma50 = technicalIndicators.sma(closes, 50).map((value, i) => ({
            time: timestamps[i + 49] as UTCTimestamp,
            value,
          }));
          break;
        case 'ema12':
          results.ema12 = technicalIndicators.ema(closes, 12).map((value, i) => ({
            time: timestamps[i + 11] as UTCTimestamp,
            value,
          }));
          break;
        case 'ema26':
          results.ema26 = technicalIndicators.ema(closes, 26).map((value, i) => ({
            time: timestamps[i + 25] as UTCTimestamp,
            value,
          }));
          break;
        case 'rsi':
          results.rsi = technicalIndicators.rsi(closes).map((value, i) => ({
            time: timestamps[i + 14] as UTCTimestamp,
            value,
          }));
          break;
        case 'macd':
          const macdData = technicalIndicators.macd(closes);
          results.macd = {
            macd: macdData.macd.map((value, i) => ({
              time: timestamps[i + 33] as UTCTimestamp,
              value,
            })),
            signal: macdData.signal.map((value, i) => ({
              time: timestamps[i + 41] as UTCTimestamp,
              value,
            })),
            histogram: macdData.histogram.map((value, i) => ({
              time: timestamps[i + 41] as UTCTimestamp,
              value,
            })),
          };
          break;
        case 'bollinger':
          const bollingerData = technicalIndicators.bollinger(closes);
          results.bollinger = {
            upper: bollingerData.upper.map((value, i) => ({
              time: timestamps[i + 19] as UTCTimestamp,
              value,
            })),
            middle: bollingerData.middle.map((value, i) => ({
              time: timestamps[i + 19] as UTCTimestamp,
              value,
            })),
            lower: bollingerData.lower.map((value, i) => ({
              time: timestamps[i + 19] as UTCTimestamp,
              value,
            })),
          };
          break;
      }
    });
    
    return results;
  }, [data, indicators]);
}
