import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { api, MockWebSocket } from '@/services/api';
import { Alert } from '@/types';
import { 
  Bell, 
  AlertTriangle, 
  Info, 
  TrendingUp, 
  Activity, 
  Heart, 
  Shield,
  ChevronDown,
  CheckCircle,
  Clock,
  MoreVertical
} from 'lucide-react';
import { cn } from '@/lib/utils';

export const Alerts: React.FC = () => {
  const [realtimeAlerts, setRealtimeAlerts] = useState<any[]>([]);
  const queryClient = useQueryClient();

  const { data: alerts = [] } = useQuery({
    queryKey: ['alerts'],
    queryFn: api.getAlerts,
  });

  const markAsReadMutation = useMutation({
    mutationFn: api.markAlertAsRead,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['alerts'] });
    },
  });

  // WebSocket for real-time alerts
  useEffect(() => {
    const ws = new MockWebSocket();
    ws.connect();
    
    ws.onMessage((data) => {
      if (data.type === 'price_update') {
        setRealtimeAlerts(prev => [
          {
            id: `realtime-${Date.now()}`,
            type: 'price',
            severity: Math.abs(data.change) > 5 ? 'critical' : 'info',
            title: `${data.tokenId.toUpperCase()} Price Update`,
            message: `Price ${data.change > 0 ? 'increased' : 'decreased'} by ${Math.abs(data.change).toFixed(2)}%`,
            timestamp: data.timestamp,
            isRealtime: true,
            tokenId: data.tokenId
          },
          ...prev.slice(0, 4) // Keep only 5 recent alerts
        ]);
      }
    });

    return () => ws.disconnect();
  }, []);

  const getAlertIcon = (type: string, severity: string) => {
    if (severity === 'critical') return <AlertTriangle className="w-5 h-5 text-red-500" />;
    
    switch (type) {
      case 'price': return <TrendingUp className="w-5 h-5 text-blue-500" />;
      case 'volume': return <Activity className="w-5 h-5 text-purple-500" />;
      case 'sentiment': return <Heart className="w-5 h-5 text-pink-500" />;
      case 'risk': return <Shield className="w-5 h-5 text-amber-500" />;
      default: return <Info className="w-5 h-5 text-gray-500" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-500/20 text-red-300 border-red-500/30';
      case 'warning': return 'bg-amber-500/20 text-amber-300 border-amber-500/30';
      case 'info': return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
  };

  const unreadAlerts = alerts.filter(alert => !alert.isRead);
  const readAlerts = alerts.filter(alert => alert.isRead);

  const AlertCard: React.FC<{ alert: Alert | any; isRealtime?: boolean }> = ({ alert, isRealtime = false }) => (
    <Card className={cn(
      "transition-all hover:bg-accent/50",
      !alert.isRead && !isRealtime && "border-l-4 border-l-primary"
    )}>
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3 flex-1">
            {getAlertIcon(alert.type, alert.severity)}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1">
                <h3 className="font-medium truncate">{alert.title}</h3>
                <Badge 
                  variant="secondary" 
                  className={cn("text-xs", getSeverityColor(alert.severity))}
                >
                  {alert.severity}
                </Badge>
                {isRealtime && (
                  <Badge variant="secondary" className="bg-emerald-500/20 text-emerald-300 text-xs">
                    LIVE
                  </Badge>
                )}
              </div>
              <p className="text-sm text-muted-foreground mb-2">{alert.message}</p>
              <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                <div className="flex items-center space-x-1">
                  <Clock className="w-3 h-3" />
                  <span>{new Date(alert.timestamp).toLocaleString()}</span>
                </div>
                {alert.tokenId && (
                  <div className="text-primary font-medium">
                    #{alert.tokenId.toUpperCase()}
                  </div>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {!isRealtime && !alert.isRead && (
              <Button
                size="sm"
                variant="ghost"
                onClick={() => markAsReadMutation.mutate(alert.id)}
                disabled={markAsReadMutation.isPending}
              >
                <CheckCircle className="w-4 h-4" />
              </Button>
            )}
            
            {alert.diagnostics && (
              <Collapsible>
                <CollapsibleTrigger asChild>
                  <Button size="sm" variant="ghost">
                    <MoreVertical className="w-4 h-4" />
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2">
                  <div className="bg-secondary/50 rounded-md p-3 text-xs">
                    <div className="font-medium mb-2">Diagnostic Information:</div>
                    <pre className="text-muted-foreground whitespace-pre-wrap">
                      {JSON.stringify(alert.diagnostics, null, 2)}
                    </pre>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center">
            <Bell className="w-8 h-8 mr-2 text-primary" />
            AI Alert Center
          </h1>
          <p className="text-muted-foreground">
            Real-time notifications and market intelligence
          </p>
        </div>

        <div className="flex items-center space-x-4">
          <Badge variant="secondary" className="bg-red-500/20 text-red-300">
            <AlertTriangle className="w-3 h-3 mr-1" />
            {unreadAlerts.length} unread
          </Badge>
          <Badge variant="secondary" className="bg-emerald-500/20 text-emerald-300">
            <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse mr-1" />
            Live monitoring
          </Badge>
        </div>
      </div>

      {/* Real-time Alerts */}
      {realtimeAlerts.length > 0 && (
        <Card className="bg-gradient-to-r from-primary/10 to-secondary/10 border-primary/20">
          <CardHeader>
            <CardTitle className="flex items-center">
              <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse mr-2" />
              Live Alerts
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {realtimeAlerts.map((alert) => (
              <AlertCard key={alert.id} alert={alert} isRealtime />
            ))}
          </CardContent>
        </Card>
      )}

      {/* Alert Management */}
      <Tabs defaultValue="unread" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="unread" className="flex items-center space-x-2">
            <Bell className="w-4 h-4" />
            <span>Unread ({unreadAlerts.length})</span>
          </TabsTrigger>
          <TabsTrigger value="all" className="flex items-center space-x-2">
            <Activity className="w-4 h-4" />
            <span>All Alerts</span>
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center space-x-2">
            <Shield className="w-4 h-4" />
            <span>Settings</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="unread" className="space-y-4">
          {unreadAlerts.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <CheckCircle className="w-12 h-12 mx-auto mb-4 text-emerald-500" />
                <h3 className="text-lg font-medium mb-2">All caught up!</h3>
                <p className="text-muted-foreground">You have no unread alerts.</p>
              </CardContent>
            </Card>
          ) : (
            unreadAlerts.map((alert) => (
              <AlertCard key={alert.id} alert={alert} />
            ))
          )}
        </TabsContent>

        <TabsContent value="all" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">All Alerts</h3>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => {
                alerts.forEach(alert => {
                  if (!alert.isRead) {
                    markAsReadMutation.mutate(alert.id);
                  }
                });
              }}
            >
              Mark All as Read
            </Button>
          </div>
          
          {alerts.map((alert) => (
            <AlertCard key={alert.id} alert={alert} />
          ))}
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Alert Preferences</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Price Alerts</div>
                    <div className="text-sm text-muted-foreground">
                      Get notified when token prices cross thresholds
                    </div>
                  </div>
                  <div className="w-2 h-2 bg-emerald-500 rounded-full" />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Volume Alerts</div>
                    <div className="text-sm text-muted-foreground">
                      Notifications for unusual trading volume
                    </div>
                  </div>
                  <div className="w-2 h-2 bg-emerald-500 rounded-full" />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Sentiment Alerts</div>
                    <div className="text-sm text-muted-foreground">
                      Social sentiment change notifications
                    </div>
                  </div>
                  <div className="w-2 h-2 bg-emerald-500 rounded-full" />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Risk Alerts</div>
                    <div className="text-sm text-muted-foreground">
                      Risk level change notifications
                    </div>
                  </div>
                  <div className="w-2 h-2 bg-emerald-500 rounded-full" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Notification Channels</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm text-muted-foreground mb-4">
                Configure how you receive alerts
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span>In-app notifications</span>
                  <Badge variant="secondary" className="bg-emerald-500/20 text-emerald-300">
                    Enabled
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>Email notifications</span>
                  <Badge variant="secondary" className="bg-gray-500/20 text-gray-300">
                    Disabled
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>SMS notifications</span>
                  <Badge variant="secondary" className="bg-gray-500/20 text-gray-300">
                    Disabled
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};