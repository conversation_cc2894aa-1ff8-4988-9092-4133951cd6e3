import React, { useState, useEffect } from 'react';
import { useInfiniteQuery } from '@tanstack/react-query';
import { useVirtualizer } from '@tanstack/react-virtual';
import { TokenCard } from '@/components/TokenCard';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { api } from '@/services/api';
import { Token } from '@/types';
import { Search, Filter, TrendingUp, TrendingDown, Zap } from 'lucide-react';
import { cn } from '@/lib/utils';

const CHAIN_FILTERS = [
  { id: 'all', name: 'All Chains', icon: '🌐' },
  { id: 'ethereum', name: 'Ethereum', icon: '⟠' },
  { id: 'solana', name: '<PERSON><PERSON>', icon: '◎' },
  { id: 'cosmos', name: 'Cosmos', icon: '⚛️' },
];

const RISK_FILTERS = [
  { id: 'all', name: 'All Risk Levels' },
  { id: 'low', name: 'Low Risk' },
  { id: 'medium', name: 'Medium Risk' },
  { id: 'high', name: 'High Risk' },
];

export const Discovery: React.FC = () => {
  const [search, setSearch] = useState('');
  const [selectedChain, setSelectedChain] = useState('all');
  const [selectedRisk, setSelectedRisk] = useState('all');
  const [sortBy, setSortBy] = useState('aiScore');

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error
  } = useInfiniteQuery({
    queryKey: ['tokens', selectedChain, selectedRisk, sortBy],
    queryFn: ({ pageParam = 1 }) => api.getTokens(pageParam, 50),
    getNextPageParam: (lastPage, pages) => 
      lastPage.hasMore ? pages.length + 1 : undefined,
    initialPageParam: 1,
  });

  const allTokens = data?.pages.flatMap(page => page.tokens) || [];
  
  // Filter and sort tokens
  const filteredTokens = allTokens.filter(token => {
    const matchesSearch = token.name.toLowerCase().includes(search.toLowerCase()) ||
                         token.symbol.toLowerCase().includes(search.toLowerCase());
    const matchesChain = selectedChain === 'all' || token.chain === selectedChain;
    const matchesRisk = selectedRisk === 'all' || token.riskLevel === selectedRisk;
    return matchesSearch && matchesChain && matchesRisk;
  }).sort((a, b) => {
    switch (sortBy) {
      case 'aiScore': return b.aiScore - a.aiScore;
      case 'price': return b.price - a.price;
      case 'change24h': return b.change24h - a.change24h;
      case 'volume24h': return b.volume24h - a.volume24h;
      case 'marketCap': return b.marketCap - a.marketCap;
      default: return 0;
    }
  });

  const parentRef = React.useRef<HTMLDivElement>(null);
  
  const virtualizer = useVirtualizer({
    count: filteredTokens.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 200,
    overscan: 5,
  });

  useEffect(() => {
    const [lastItem] = [...virtualizer.getVirtualItems()].reverse();
    if (!lastItem) return;

    if (
      lastItem.index >= filteredTokens.length - 1 &&
      hasNextPage &&
      !isFetchingNextPage
    ) {
      fetchNextPage();
    }
  }, [
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    virtualizer.getVirtualItems(),
    filteredTokens.length,
  ]);

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-500 mb-2">Error loading tokens</div>
        <Button onClick={() => window.location.reload()}>Retry</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div>
          <h1 className="text-3xl font-bold">Token Discovery</h1>
          <p className="text-muted-foreground">
            Discover trending tokens with AI-powered insights
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="secondary" className="bg-emerald-500/20 text-emerald-300">
            <TrendingUp className="w-3 h-3 mr-1" />
            {filteredTokens.length} tokens
          </Badge>
          <Badge variant="secondary">
            <Zap className="w-3 h-3 mr-1" />
            Live data
          </Badge>
        </div>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5">
        <div className="relative lg:col-span-2">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <Input
            placeholder="Search tokens..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-10"
          />
        </div>

        <Select value={selectedChain} onValueChange={setSelectedChain}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {CHAIN_FILTERS.map((chain) => (
              <SelectItem key={chain.id} value={chain.id}>
                <span className="flex items-center">
                  <span className="mr-2">{chain.icon}</span>
                  {chain.name}
                </span>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={selectedRisk} onValueChange={setSelectedRisk}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {RISK_FILTERS.map((risk) => (
              <SelectItem key={risk.id} value={risk.id}>
                {risk.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={sortBy} onValueChange={setSortBy}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="aiScore">AI Score</SelectItem>
            <SelectItem value="price">Price</SelectItem>
            <SelectItem value="change24h">24h Change</SelectItem>
            <SelectItem value="volume24h">Volume</SelectItem>
            <SelectItem value="marketCap">Market Cap</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Token Grid */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {Array.from({ length: 9 }).map((_, i) => (
            <div key={i} className="bg-card rounded-lg p-6 animate-pulse">
              <div className="h-4 bg-muted rounded w-3/4 mb-4" />
              <div className="h-8 bg-muted rounded w-1/2 mb-2" />
              <div className="h-4 bg-muted rounded w-full" />
            </div>
          ))}
        </div>
      ) : (
        <div 
          ref={parentRef}
          className="h-[calc(100vh-300px)] overflow-auto"
        >
          <div
            style={{
              height: `${virtualizer.getTotalSize()}px`,
              width: '100%',
              position: 'relative',
            }}
          >
            {virtualizer.getVirtualItems().map((virtualItem) => {
              const token = filteredTokens[virtualItem.index];
              return (
                <div
                  key={virtualItem.key}
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: `${virtualItem.size}px`,
                    transform: `translateY(${virtualItem.start}px)`,
                  }}
                >
                  <div className="p-2">
                    <TokenCard token={token} />
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {isFetchingNextPage && (
        <div className="text-center py-4">
          <div className="inline-flex items-center space-x-2">
            <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
            <span>Loading more tokens...</span>
          </div>
        </div>
      )}
    </div>
  );
};