import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { TokenCard } from '@/components/TokenCard';
import { PriceChart } from '@/components/ui/chart';
import { api } from '@/services/api';
import { mockTokens, generatePriceData } from '@/data/mockData';
import { Shield, TrendingUp, Target, Award, BarChart3 } from 'lucide-react';
import { cn } from '@/lib/utils';

export const BlueChip: React.FC = () => {
  const [compareWith, setCompareWith] = useState('bitcoin');

  const { data: blueChipTokens = [] } = useQuery({
    queryKey: ['tokens', 'bluechip'],
    queryFn: () => api.getTokensByCategory('bluechip'),
  });

  const { data: compareData = [] } = useQuery({
    queryKey: ['priceData', compareWith, '30d'],
    queryFn: () => api.getPriceData(compareWith, '30d'),
  });

  const baseToken = blueChipTokens.find(t => t.id === compareWith) || blueChipTokens[0];
  
  // Calculate comparative metrics
  const totalMarketCap = blueChipTokens.reduce((sum, token) => sum + token.marketCap, 0);
  const averageAiScore = blueChipTokens.reduce((sum, token) => sum + token.aiScore, 0) / blueChipTokens.length;
  const bestPerformer = blueChipTokens.reduce((best, token) => 
    token.change24h > (best?.change24h || -Infinity) ? token : best, null
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center">
            <Shield className="w-8 h-8 mr-2 text-blue-500" />
            Blue Chip Dashboard
          </h1>
          <p className="text-muted-foreground">
            Track performance of established, low-risk cryptocurrency assets
          </p>
        </div>

        <div className="flex items-center space-x-4">
          <Badge variant="secondary" className="bg-blue-500/20 text-blue-300">
            <Shield className="w-3 h-3 mr-1" />
            {blueChipTokens.length} blue chips
          </Badge>
          <Badge variant="secondary" className="bg-emerald-500/20 text-emerald-300">
            <Award className="w-3 h-3 mr-1" />
            Low risk
          </Badge>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2 mb-2">
              <BarChart3 className="w-5 h-5 text-blue-500" />
              <span className="text-sm font-medium">Total Market Cap</span>
            </div>
            <div className="text-2xl font-bold font-mono">
              ${(totalMarketCap / 1000000000000).toFixed(1)}T
            </div>
            <div className="text-xs text-muted-foreground">
              Combined market capitalization
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2 mb-2">
              <Target className="w-5 h-5 text-emerald-500" />
              <span className="text-sm font-medium">Avg AI Score</span>
            </div>
            <div className="text-2xl font-bold font-mono">
              {averageAiScore.toFixed(0)}/100
            </div>
            <div className="text-xs text-muted-foreground">
              Average confidence rating
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2 mb-2">
              <TrendingUp className="w-5 h-5 text-purple-500" />
              <span className="text-sm font-medium">Best Performer</span>
            </div>
            <div className="text-lg font-bold">
              {bestPerformer?.symbol || 'N/A'}
            </div>
            <div className="text-xs text-emerald-400 font-medium">
              +{bestPerformer?.change24h.toFixed(2)}% (24h)
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2 mb-2">
              <Shield className="w-5 h-5 text-amber-500" />
              <span className="text-sm font-medium">Risk Level</span>
            </div>
            <div className="text-2xl font-bold text-emerald-400">
              LOW
            </div>
            <div className="text-xs text-muted-foreground">
              Established assets
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Comparative Analysis */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Price Performance Comparison</CardTitle>
          <Select value={compareWith} onValueChange={setCompareWith}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {blueChipTokens.map((token) => (
                <SelectItem key={token.id} value={token.id}>
                  <div className="flex items-center space-x-2">
                    <span>{token.logo}</span>
                    <span>{token.symbol}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardHeader>
        <CardContent>
          <PriceChart data={compareData} showArea className="h-80" />
          <div className="mt-4 text-sm text-muted-foreground">
            30-day price performance for {baseToken?.name} ({baseToken?.symbol})
          </div>
        </CardContent>
      </Card>

      {/* Performance Table */}
      <Card>
        <CardHeader>
          <CardTitle>Blue Chip Performance Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {blueChipTokens.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Shield className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <div>Loading blue chip tokens...</div>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {blueChipTokens.map((token) => (
                  <TokenCard key={token.id} token={token} compact />
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Metrics Comparison */}
      <Card>
        <CardHeader>
          <CardTitle>Detailed Metrics Comparison</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-border">
                  <th className="text-left p-2 font-medium">Token</th>
                  <th className="text-right p-2 font-medium">Price</th>
                  <th className="text-right p-2 font-medium">24h Change</th>
                  <th className="text-right p-2 font-medium">Volume</th>
                  <th className="text-right p-2 font-medium">Market Cap</th>
                  <th className="text-right p-2 font-medium">AI Score</th>
                </tr>
              </thead>
              <tbody>
                {blueChipTokens.map((token) => (
                  <tr key={token.id} className="border-b border-border/50 hover:bg-accent/30">
                    <td className="p-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{token.logo}</span>
                        <div>
                          <div className="font-medium">{token.symbol}</div>
                          <div className="text-xs text-muted-foreground">{token.name}</div>
                        </div>
                      </div>
                    </td>
                    <td className="p-2 text-right font-mono">
                      ${token.price.toLocaleString()}
                    </td>
                    <td className={cn(
                      "p-2 text-right font-mono font-medium",
                      token.change24h > 0 ? "text-emerald-400" : "text-red-400"
                    )}>
                      {token.change24h > 0 ? '+' : ''}{token.change24h.toFixed(2)}%
                    </td>
                    <td className="p-2 text-right font-mono">
                      ${(token.volume24h / 1000000).toFixed(1)}M
                    </td>
                    <td className="p-2 text-right font-mono">
                      ${(token.marketCap / 1000000000).toFixed(1)}B
                    </td>
                    <td className="p-2 text-right">
                      <div className="flex items-center justify-end space-x-2">
                        <div className="w-12 h-1.5 bg-secondary rounded-full overflow-hidden">
                          <div 
                            className={cn(
                              "h-full transition-all",
                              token.aiScore >= 80 ? "bg-emerald-500" : 
                              token.aiScore >= 60 ? "bg-blue-500" : "bg-amber-500"
                            )}
                            style={{ width: `${token.aiScore}%` }}
                          />
                        </div>
                        <span className="font-mono text-xs">{token.aiScore}</span>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};