import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { PriceChart, VolumeChart } from '@/components/ui/chart';
import { api } from '@/services/api';
import { mockTokens } from '@/data/mockData';
import { TrendingUp, TrendingDown, Activity, Users, Target, Zap } from 'lucide-react';
import { cn } from '@/lib/utils';

export const Analytics: React.FC = () => {
  const [selectedToken, setSelectedToken] = useState('bitcoin');
  const [timeframe, setTimeframe] = useState('24h');

  const { data: token } = useQuery({
    queryKey: ['token', selectedToken],
    queryFn: () => api.getToken(selectedToken),
  });

  const { data: priceData = [] } = useQuery({
    queryKey: ['priceData', selectedToken, timeframe],
    queryFn: () => api.getPriceData(selectedToken, timeframe),
  });

  if (!token) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
      </div>
    );
  }

  const isPositive = token.change24h > 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold">Token Analytics</h1>
          <p className="text-muted-foreground">
            Deep dive into token performance and metrics
          </p>
        </div>

        <div className="flex items-center space-x-4">
          <Select value={selectedToken} onValueChange={setSelectedToken}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {mockTokens.map((token) => (
                <SelectItem key={token.id} value={token.id}>
                  <div className="flex items-center space-x-2">
                    <span>{token.logo}</span>
                    <span>{token.symbol}</span>
                    <span className="text-muted-foreground">({token.name})</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={timeframe} onValueChange={setTimeframe}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="24h">24H</SelectItem>
              <SelectItem value="7d">7D</SelectItem>
              <SelectItem value="30d">30D</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Token Overview */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-4xl">{token.logo}</span>
              <div>
                <h2 className="text-2xl font-bold">{token.name}</h2>
                <div className="flex items-center space-x-2">
                  <span className="text-lg text-muted-foreground">{token.symbol}</span>
                  <Badge variant="secondary">{token.chain}</Badge>
                  <Badge 
                    variant="secondary"
                    className={cn(
                      token.riskLevel === 'low' ? 'bg-emerald-500/20 text-emerald-300' :
                      token.riskLevel === 'medium' ? 'bg-amber-500/20 text-amber-300' :
                      'bg-red-500/20 text-red-300'
                    )}
                  >
                    {token.riskLevel} risk
                  </Badge>
                </div>
              </div>
            </div>

            <div className="text-right">
              <div className="text-3xl font-bold font-mono">
                ${token.price.toLocaleString()}
              </div>
              <div className={cn(
                'text-lg font-medium flex items-center justify-end',
                isPositive ? 'text-emerald-400' : 'text-red-400'
              )}>
                {isPositive ? <TrendingUp className="w-5 h-5 mr-1" /> : <TrendingDown className="w-5 h-5 mr-1" />}
                {isPositive ? '+' : ''}{token.change24h.toFixed(2)}% (24h)
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Analytics */}
      <Tabs defaultValue="price" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="price">Price Analysis</TabsTrigger>
          <TabsTrigger value="metrics">Key Metrics</TabsTrigger>
          <TabsTrigger value="sentiment">Sentiment</TabsTrigger>
          <TabsTrigger value="risk">Risk Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="price" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Price Chart</CardTitle>
            </CardHeader>
            <CardContent>
              <PriceChart data={priceData} showArea className="h-80" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Volume</CardTitle>
            </CardHeader>
            <CardContent>
              <VolumeChart data={priceData} className="h-48" />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Activity className="w-5 h-5 text-blue-500" />
                  <span className="text-sm font-medium">Volume 24h</span>
                </div>
                <div className="mt-2">
                  <div className="text-2xl font-bold font-mono">
                    ${(token.volume24h / 1000000).toFixed(1)}M
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Trading volume in last 24h
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Users className="w-5 h-5 text-emerald-500" />
                  <span className="text-sm font-medium">Holders</span>
                </div>
                <div className="mt-2">
                  <div className="text-2xl font-bold font-mono">
                    {(token.metrics.holders / 1000).toFixed(0)}K
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Total token holders
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Zap className="w-5 h-5 text-amber-500" />
                  <span className="text-sm font-medium">Transactions</span>
                </div>
                <div className="mt-2">
                  <div className="text-2xl font-bold font-mono">
                    {(token.metrics.transactions24h / 1000).toFixed(1)}K
                  </div>
                  <div className="text-xs text-muted-foreground">
                    24h transaction count
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Target className="w-5 h-5 text-purple-500" />
                  <span className="text-sm font-medium">AI Score</span>
                </div>
                <div className="mt-2">
                  <div className="text-2xl font-bold font-mono">
                    {token.aiScore}/100
                  </div>
                  <div className="text-xs text-muted-foreground">
                    AI confidence rating
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Advanced Metrics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">Liquidity Score</span>
                    <span className="font-mono">{(token.metrics.liquidity * 100).toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-secondary rounded-full h-2">
                    <div 
                      className="bg-blue-500 h-2 rounded-full transition-all"
                      style={{ width: `${token.metrics.liquidity * 100}%` }}
                    />
                  </div>
                </div>

                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">Volatility</span>
                    <span className="font-mono">{(token.metrics.volatility * 100).toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-secondary rounded-full h-2">
                    <div 
                      className={cn(
                        "h-2 rounded-full transition-all",
                        token.metrics.volatility > 0.1 ? "bg-red-500" :
                        token.metrics.volatility > 0.05 ? "bg-amber-500" : "bg-emerald-500"
                      )}
                      style={{ width: `${Math.min(token.metrics.volatility * 500, 100)}%` }}
                    />
                  </div>
                </div>

                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">Velocity</span>
                    <span className="font-mono">{(token.metrics.velocity * 100).toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-secondary rounded-full h-2">
                    <div 
                      className="bg-purple-500 h-2 rounded-full transition-all"
                      style={{ width: `${token.metrics.velocity * 100}%` }}
                    />
                  </div>
                </div>

                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">Market Cap Rank</span>
                    <span className="font-mono">#{Math.floor(Math.random() * 500) + 1}</span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    ${(token.marketCap / 1000000000).toFixed(2)}B market cap
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sentiment" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardContent className="p-6 text-center">
                <div className="text-3xl font-bold text-emerald-400 mb-2">
                  {(token.sentiment.positive * 100).toFixed(1)}%
                </div>
                <div className="text-sm text-muted-foreground mb-4">Positive Sentiment</div>
                <div className="w-full bg-secondary rounded-full h-2">
                  <div 
                    className="bg-emerald-500 h-2 rounded-full"
                    style={{ width: `${token.sentiment.positive * 100}%` }}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="text-3xl font-bold text-red-400 mb-2">
                  {(token.sentiment.negative * 100).toFixed(1)}%
                </div>
                <div className="text-sm text-muted-foreground mb-4">Negative Sentiment</div>
                <div className="w-full bg-secondary rounded-full h-2">
                  <div 
                    className="bg-red-500 h-2 rounded-full"
                    style={{ width: `${token.sentiment.negative * 100}%` }}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="text-3xl font-bold text-gray-400 mb-2">
                  {(token.sentiment.neutral * 100).toFixed(1)}%
                </div>
                <div className="text-sm text-muted-foreground mb-4">Neutral Sentiment</div>
                <div className="w-full bg-secondary rounded-full h-2">
                  <div 
                    className="bg-gray-500 h-2 rounded-full"
                    style={{ width: `${token.sentiment.neutral * 100}%` }}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Sentiment Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center text-muted-foreground">
                Sentiment chart would be displayed here with real-time social media analysis
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="risk" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Risk Assessment</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="text-center">
                  <div className={cn(
                    "text-4xl font-bold mb-2",
                    token.riskLevel === 'low' ? 'text-emerald-400' :
                    token.riskLevel === 'medium' ? 'text-amber-400' : 'text-red-400'
                  )}>
                    {token.riskLevel.toUpperCase()} RISK
                  </div>
                  <div className="text-muted-foreground">
                    Overall risk assessment based on multiple factors
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between mb-2">
                        <span>Liquidity Risk</span>
                        <span className="font-mono">{token.riskLevel === 'low' ? 'Low' : token.riskLevel === 'medium' ? 'Medium' : 'High'}</span>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Based on trading volume and market depth
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between mb-2">
                        <span>Volatility Risk</span>
                        <span className="font-mono">{token.metrics.volatility > 0.1 ? 'High' : token.metrics.volatility > 0.05 ? 'Medium' : 'Low'}</span>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Price volatility over the last 30 days
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between mb-2">
                        <span>Market Risk</span>
                        <span className="font-mono">{token.marketCap > 1000000000 ? 'Low' : token.marketCap > 100000000 ? 'Medium' : 'High'}</span>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Market capitalization and adoption
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between mb-2">
                        <span>Sentiment Risk</span>
                        <span className="font-mono">{token.sentiment.negative < 0.3 ? 'Low' : token.sentiment.negative < 0.5 ? 'Medium' : 'High'}</span>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Social sentiment and community health
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};