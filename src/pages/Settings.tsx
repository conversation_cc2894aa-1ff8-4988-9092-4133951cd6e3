import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Settings as SettingsIcon, 
  Palette, 
  Zap, 
  Keyboard, 
  Shield, 
  Database,
  Monitor,
  Smartphone,
  Sun,
  Moon,
  Activity
} from 'lucide-react';
import { cn } from '@/lib/utils';

export const Settings: React.FC = () => {
  const [theme, setTheme] = useState('dark');
  const [refreshRate, setRefreshRate] = useState('5');
  const [apiEndpoint, setApiEndpoint] = useState('https://api.cipherscope.io');

  const shortcuts = [
    { key: 'Ctrl + K', action: 'Quick search' },
    { key: 'Ctrl + 1', action: 'Go to Discovery' },
    { key: 'Ctrl + 2', action: 'Go to Analytics' },
    { key: 'Ctrl + 3', action: 'Go to Meme Tokens' },
    { key: 'Ctrl + 4', action: 'Go to Blue Chips' },
    { key: 'Ctrl + 5', action: 'Go to Alerts' },
    { key: 'Escape', action: 'Close modal/drawer' },
    { key: 'R', action: 'Refresh data' },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center">
            <SettingsIcon className="w-8 h-8 mr-2 text-primary" />
            Settings
          </h1>
          <p className="text-muted-foreground">
            Customize your CipherScope experience
          </p>
        </div>

        <Badge variant="secondary" className="bg-blue-500/20 text-blue-300">
          <Shield className="w-3 h-3 mr-1" />
          All settings synced
        </Badge>
      </div>

      {/* Settings Tabs */}
      <Tabs defaultValue="appearance" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="appearance" className="flex items-center space-x-2">
            <Palette className="w-4 h-4" />
            <span className="hidden sm:inline">Appearance</span>
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center space-x-2">
            <Zap className="w-4 h-4" />
            <span className="hidden sm:inline">Performance</span>
          </TabsTrigger>
          <TabsTrigger value="shortcuts" className="flex items-center space-x-2">
            <Keyboard className="w-4 h-4" />
            <span className="hidden sm:inline">Shortcuts</span>
          </TabsTrigger>
          <TabsTrigger value="api" className="flex items-center space-x-2">
            <Database className="w-4 h-4" />
            <span className="hidden sm:inline">API</span>
          </TabsTrigger>
          <TabsTrigger value="about" className="flex items-center space-x-2">
            <Activity className="w-4 h-4" />
            <span className="hidden sm:inline">About</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="appearance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Theme Preferences</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-3">
                <Label>Color Theme</Label>
                <div className="grid grid-cols-3 gap-3">
                  <button
                    onClick={() => setTheme('dark')}
                    className={cn(
                      "p-4 rounded-lg border-2 transition-all text-left",
                      theme === 'dark' ? "border-primary bg-primary/10" : "border-border hover:border-border/80"
                    )}
                  >
                    <div className="flex items-center space-x-2 mb-2">
                      <Moon className="w-4 h-4" />
                      <span className="font-medium">Dark</span>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Optimized for trading environments
                    </div>
                  </button>

                  <button
                    onClick={() => setTheme('light')}
                    className={cn(
                      "p-4 rounded-lg border-2 transition-all text-left",
                      theme === 'light' ? "border-primary bg-primary/10" : "border-border hover:border-border/80"
                    )}
                  >
                    <div className="flex items-center space-x-2 mb-2">
                      <Sun className="w-4 h-4" />
                      <span className="font-medium">Light</span>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Clean and minimal interface
                    </div>
                  </button>

                  <button
                    onClick={() => setTheme('auto')}
                    className={cn(
                      "p-4 rounded-lg border-2 transition-all text-left",
                      theme === 'auto' ? "border-primary bg-primary/10" : "border-border hover:border-border/80"
                    )}
                  >
                    <div className="flex items-center space-x-2 mb-2">
                      <Monitor className="w-4 h-4" />
                      <span className="font-medium">Auto</span>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Follows system preference
                    </div>
                  </button>
                </div>
              </div>

              <div className="space-y-3">
                <Label>Display Density</Label>
                <Select defaultValue="comfortable">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="compact">Compact - More data per screen</SelectItem>
                    <SelectItem value="comfortable">Comfortable - Balanced layout</SelectItem>
                    <SelectItem value="spacious">Spacious - Easier to read</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-3">
                <Label>Animations</Label>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Reduce motion</div>
                    <div className="text-sm text-muted-foreground">
                      Minimize animations for better performance
                    </div>
                  </div>
                  <div className="w-2 h-2 bg-gray-500 rounded-full" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Chart Preferences</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Default Timeframe</Label>
                  <Select defaultValue="24h">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1h">1 Hour</SelectItem>
                      <SelectItem value="24h">24 Hours</SelectItem>
                      <SelectItem value="7d">7 Days</SelectItem>
                      <SelectItem value="30d">30 Days</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Chart Type</Label>
                  <Select defaultValue="line">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="line">Line Chart</SelectItem>
                      <SelectItem value="area">Area Chart</SelectItem>
                      <SelectItem value="candlestick">Candlestick</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Data Refresh</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Refresh Rate (seconds)</Label>
                <Select value={refreshRate} onValueChange={setRefreshRate}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 second (High CPU usage)</SelectItem>
                    <SelectItem value="5">5 seconds (Recommended)</SelectItem>
                    <SelectItem value="10">10 seconds</SelectItem>
                    <SelectItem value="30">30 seconds</SelectItem>
                    <SelectItem value="60">1 minute</SelectItem>
                  </SelectContent>
                </Select>
                <div className="text-xs text-muted-foreground mt-1">
                  Lower values provide more real-time data but use more resources
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Offline Mode</div>
                  <div className="text-sm text-muted-foreground">
                    Cache data for offline viewing
                  </div>
                </div>
                <div className="w-2 h-2 bg-emerald-500 rounded-full" />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Background Updates</div>
                  <div className="text-sm text-muted-foreground">
                    Keep data fresh when app is in background
                  </div>
                </div>
                <div className="w-2 h-2 bg-emerald-500 rounded-full" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Memory Usage</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold font-mono">47MB</div>
                  <div className="text-sm text-muted-foreground">Current Usage</div>
                </div>
                <div>
                  <div className="text-2xl font-bold font-mono">128MB</div>
                  <div className="text-sm text-muted-foreground">Peak Usage</div>
                </div>
                <div>
                  <div className="text-2xl font-bold font-mono">512MB</div>
                  <div className="text-sm text-muted-foreground">Available</div>
                </div>
              </div>

              <Button variant="outline" className="w-full">
                Clear Cache
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="shortcuts" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Keyboard Shortcuts</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {shortcuts.map((shortcut, index) => (
                  <div key={index} className="flex items-center justify-between py-2">
                    <span className="text-sm">{shortcut.action}</span>
                    <Badge variant="secondary" className="font-mono">
                      {shortcut.key}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Accessibility</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Tab Navigation</div>
                  <div className="text-sm text-muted-foreground">
                    Enhanced keyboard navigation
                  </div>
                </div>
                <div className="w-2 h-2 bg-emerald-500 rounded-full" />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Screen Reader Support</div>
                  <div className="text-sm text-muted-foreground">
                    ARIA labels and descriptions
                  </div>
                </div>
                <div className="w-2 h-2 bg-emerald-500 rounded-full" />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">High Contrast Mode</div>
                  <div className="text-sm text-muted-foreground">
                    Enhanced visibility for better accessibility
                  </div>
                </div>
                <div className="w-2 h-2 bg-gray-500 rounded-full" />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="api" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>API Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>API Endpoint</Label>
                <Input 
                  value={apiEndpoint}
                  onChange={(e) => setApiEndpoint(e.target.value)}
                  placeholder="https://api.cipherscope.io"
                />
                <div className="text-xs text-muted-foreground mt-1">
                  Custom API endpoint for enterprise users
                </div>
              </div>

              <div>
                <Label>WebSocket URL</Label>
                <Input 
                  defaultValue="wss://ws.cipherscope.io"
                  placeholder="wss://ws.cipherscope.io"
                />
                <div className="text-xs text-muted-foreground mt-1">
                  Real-time data connection endpoint
                </div>
              </div>

              <div className="flex space-x-2">
                <Button variant="outline">Test Connection</Button>
                <Button>Save Configuration</Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Data Sources</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Price Data</div>
                    <div className="text-sm text-muted-foreground">CoinGecko API</div>
                  </div>
                  <Badge variant="secondary" className="bg-emerald-500/20 text-emerald-300">
                    Connected
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Social Sentiment</div>
                    <div className="text-sm text-muted-foreground">Twitter API v2</div>
                  </div>
                  <Badge variant="secondary" className="bg-emerald-500/20 text-emerald-300">
                    Connected
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">On-chain Data</div>
                    <div className="text-sm text-muted-foreground">Etherscan, Solscan</div>
                  </div>
                  <Badge variant="secondary" className="bg-emerald-500/20 text-emerald-300">
                    Connected
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">AI Analytics</div>
                    <div className="text-sm text-muted-foreground">CipherScope ML Pipeline</div>
                  </div>
                  <Badge variant="secondary" className="bg-emerald-500/20 text-emerald-300">
                    Connected
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="about" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Application Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center space-y-4">
                <div className="flex items-center justify-center space-x-2">
                  <Activity className="w-8 h-8 text-primary" />
                  <span className="text-2xl font-bold">CipherScope</span>
                </div>
                
                <div className="space-y-2">
                  <div className="text-lg font-semibold">Version 1.0.0</div>
                  <div className="text-sm text-muted-foreground">
                    Production-ready crypto analytics platform
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 pt-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold font-mono">99.9%</div>
                    <div className="text-sm text-muted-foreground">Uptime</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold font-mono">2.1s</div>
                    <div className="text-sm text-muted-foreground">Avg Response</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>System Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span>Application Status</span>
                  <Badge variant="secondary" className="bg-emerald-500/20 text-emerald-300">
                    Operational
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>Data Pipeline</span>
                  <Badge variant="secondary" className="bg-emerald-500/20 text-emerald-300">
                    Healthy
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>WebSocket Connection</span>
                  <Badge variant="secondary" className="bg-emerald-500/20 text-emerald-300">
                    Connected
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>AI Analytics Engine</span>
                  <Badge variant="secondary" className="bg-emerald-500/20 text-emerald-300">
                    Running
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Technical Stack</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="font-medium mb-2">Frontend</div>
                  <div className="space-y-1 text-muted-foreground">
                    <div>React 18.3.1</div>
                    <div>TypeScript 5.5.3</div>
                    <div>Vite 5.4.8</div>
                    <div>Tailwind CSS 3.4.13</div>
                  </div>
                </div>
                <div>
                  <div className="font-medium mb-2">Libraries</div>
                  <div className="space-y-1 text-muted-foreground">
                    <div>React Query 5.x</div>
                    <div>Recharts 2.12.7</div>
                    <div>Framer Motion</div>
                    <div>Radix UI</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};