import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { HeatmapChart } from '@/components/ui/chart';
import { TokenCard } from '@/components/TokenCard';
import { api } from '@/services/api';
import { mockHeatmapData } from '@/data/mockData';
import { Flame, TrendingUp, TrendingDown, Zap, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';

export const MemeTokens: React.FC = () => {
  const [sortBy, setSortBy] = useState('volatility');

  const { data: memeTokens = [] } = useQuery({
    queryKey: ['tokens', 'meme'],
    queryFn: () => api.getTokensByCategory('meme'),
  });

  const sortedTokens = [...memeTokens].sort((a, b) => {
    switch (sortBy) {
      case 'volatility': return b.metrics.volatility - a.metrics.volatility;
      case 'velocity': return b.metrics.velocity - a.metrics.velocity;
      case 'change24h': return b.change24h - a.change24h;
      case 'volume24h': return b.volume24h - a.volume24h;
      default: return 0;
    }
  });

  const heatmapData = mockHeatmapData.map(item => ({
    name: item.symbol,
    value: item.volatility,
    change: item.change24h
  }));

  const averageVolatility = memeTokens.reduce((sum, token) => sum + token.metrics.volatility, 0) / memeTokens.length;
  const averageVelocity = memeTokens.reduce((sum, token) => sum + token.metrics.velocity, 0) / memeTokens.length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center">
            <Flame className="w-8 h-8 mr-2 text-orange-500" />
            Meme Token Analytics
          </h1>
          <p className="text-muted-foreground">
            Track volatility and velocity of trending meme tokens
          </p>
        </div>

        <div className="flex items-center space-x-4">
          <Badge variant="secondary" className="bg-orange-500/20 text-orange-300">
            <Flame className="w-3 h-3 mr-1" />
            {memeTokens.length} meme tokens
          </Badge>
          <Badge variant="secondary" className={cn(
            averageVolatility > 0.1 ? "bg-red-500/20 text-red-300" : "bg-amber-500/20 text-amber-300"
          )}>
            <AlertTriangle className="w-3 h-3 mr-1" />
            {averageVolatility > 0.1 ? 'High' : 'Medium'} volatility
          </Badge>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2 mb-2">
              <TrendingUp className="w-5 h-5 text-emerald-500" />
              <span className="text-sm font-medium">Avg Volatility</span>
            </div>
            <div className="text-2xl font-bold font-mono">
              {(averageVolatility * 100).toFixed(1)}%
            </div>
            <div className="text-xs text-muted-foreground">
              Average 24h volatility
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2 mb-2">
              <Zap className="w-5 h-5 text-purple-500" />
              <span className="text-sm font-medium">Avg Velocity</span>
            </div>
            <div className="text-2xl font-bold font-mono">
              {(averageVelocity * 100).toFixed(1)}%
            </div>
            <div className="text-xs text-muted-foreground">
              Average token velocity
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2 mb-2">
              <TrendingUp className="w-5 h-5 text-blue-500" />
              <span className="text-sm font-medium">Gainers</span>
            </div>
            <div className="text-2xl font-bold font-mono text-emerald-400">
              {memeTokens.filter(t => t.change24h > 0).length}
            </div>
            <div className="text-xs text-muted-foreground">
              Tokens with positive 24h change
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2 mb-2">
              <TrendingDown className="w-5 h-5 text-red-500" />
              <span className="text-sm font-medium">Losers</span>
            </div>
            <div className="text-2xl font-bold font-mono text-red-400">
              {memeTokens.filter(t => t.change24h < 0).length}
            </div>
            <div className="text-xs text-muted-foreground">
              Tokens with negative 24h change
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Volatility Heatmap */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Volatility Heatmap</CardTitle>
          <Badge variant="secondary">
            Real-time data
          </Badge>
        </CardHeader>
        <CardContent>
          <HeatmapChart data={heatmapData} />
          <div className="mt-4 text-sm text-muted-foreground">
            <div className="flex items-center justify-between">
              <span>Green = Positive performance</span>
              <span>Red = Negative performance</span>
            </div>
            <div className="mt-1">
              Size represents volatility level - larger boxes indicate higher volatility
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Token List */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Meme Token Rankings</CardTitle>
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="volatility">Sort by Volatility</SelectItem>
              <SelectItem value="velocity">Sort by Velocity</SelectItem>
              <SelectItem value="change24h">Sort by 24h Change</SelectItem>
              <SelectItem value="volume24h">Sort by Volume</SelectItem>
            </SelectContent>
          </Select>
        </CardHeader>
        <CardContent className="space-y-4">
          {sortedTokens.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Flame className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <div>Loading meme tokens...</div>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {sortedTokens.map((token) => (
                <TokenCard key={token.id} token={token} compact />
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Risk Warning */}
      <Card className="bg-amber-500/10 border-amber-500/20">
        <CardContent className="p-6">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="w-6 h-6 text-amber-500 mt-0.5" />
            <div>
              <h3 className="font-semibold text-amber-300 mb-2">Meme Token Risk Warning</h3>
              <p className="text-sm text-muted-foreground">
                Meme tokens are highly speculative and volatile investments. They can experience extreme 
                price swings in short periods. Only invest what you can afford to lose and always do your 
                own research before making investment decisions.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};