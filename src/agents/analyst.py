"""
Analyst Agent for generating comprehensive token analysis and recommendations.
Combines data from all other agents to provide investment insights.
"""

import json
import logging
from datetime import datetime
from typing import Any

import structlog

from ..core.cache import CacheManager
from ..core.config import get_settings
from ..core.database import DatabaseManager

logger = structlog.get_logger(__name__)


class AnalystAgent:
    """
    Analyst agent that synthesizes data from all other agents to generate
    comprehensive token analysis and investment recommendations.

    Features:
    - Risk assessment
    - Alpha scoring
    - Investment recommendations
    - Confidence scoring
    - Narrative generation
    """

    def __init__(
        self,
        db_manager: DatabaseManager,
        cache_manager: CacheManager,
        metrics_collector,
        coordinator=None,
    ):
        self.settings = get_settings()
        self.logger = logging.getLogger(__name__)
        self.db_manager = db_manager
        self.cache_manager = cache_manager
        self.metrics_collector = metrics_collector
        self.coordinator = coordinator

        # Scoring weights for different factors
        self.risk_weights = {
            "liquidity": 0.25,
            "volatility": 0.20,
            "holder_concentration": 0.15,
            "contract_security": 0.20,
            "market_cap": 0.10,
            "age": 0.10,
        }

        self.alpha_weights = {
            "volume_trend": 0.25,
            "price_momentum": 0.20,
            "social_sentiment": 0.15,
            "technical_signals": 0.25,
            "fundamental_growth": 0.15,
        }

    async def initialize(self) -> None:
        """Initialize the analyst agent."""
        try:
            self.logger.info("Initializing AnalystAgent")
            # Analyst-specific initialization
            self.logger.info("AnalystAgent initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize AnalystAgent: {e}")
            raise

    async def shutdown(self) -> None:
        """Shutdown the analyst agent."""
        try:
            self.logger.info("Shutting down AnalystAgent")
            # Cleanup if needed
            self.logger.info("AnalystAgent shutdown complete")
        except Exception as e:
            self.logger.error(f"Error during AnalystAgent shutdown: {e}")

    async def health_check(self) -> bool:
        """Check agent health."""
        try:
            # Perform basic health checks
            return True
        except Exception as e:
            self.logger.error(f"AnalystAgent health check failed: {e}")
            return False

    async def analyze_token(
        self,
        token_data: dict[str, Any],
        technical_indicators: dict[str, Any],
        sentiment_data: dict[str, Any],
    ) -> dict[str, Any]:
        """
        Generate comprehensive token analysis.

        Args:
            token_data: Basic token information
            technical_indicators: Technical analysis data
            sentiment_data: Sentiment analysis data

        Returns:
            Complete analysis with scores and recommendations
        """
        try:
            self.logger.info(f"Analyzing token {token_data.get('symbol', 'UNKNOWN')}")

            # Calculate risk score
            risk_score = await self._calculate_risk_score(
                token_data, technical_indicators, sentiment_data
            )

            # Calculate alpha score
            alpha_score = await self._calculate_alpha_score(
                token_data, technical_indicators, sentiment_data
            )

            # Generate investment recommendation
            recommendation = self._generate_recommendation(risk_score, alpha_score)

            # Calculate confidence score
            confidence = self._calculate_confidence(
                token_data, technical_indicators, sentiment_data
            )

            # Generate narrative summary
            summary = self._generate_summary(
                token_data, risk_score, alpha_score, recommendation, confidence
            )

            analysis = {
                "token_address": token_data.get("address"),
                "symbol": token_data.get("symbol"),
                "risk_score": risk_score,
                "alpha_score": alpha_score,
                "recommendation": recommendation,
                "confidence": confidence,
                "summary": summary,
                "analysis_timestamp": datetime.utcnow().isoformat(),
                "risk_factors": self._identify_risk_factors(
                    token_data, technical_indicators
                ),
                "opportunity_factors": self._identify_opportunities(
                    token_data, technical_indicators, sentiment_data
                ),
                "key_metrics": self._extract_key_metrics(
                    token_data, technical_indicators, sentiment_data
                ),
            }

            # Store analysis
            await self._store_analysis(analysis)

            return analysis

        except Exception as e:
            self.logger.error(f"Token analysis failed: {e}")
            raise

    async def _calculate_risk_score(
        self,
        token_data: dict[str, Any],
        technical_indicators: dict[str, Any],
        sentiment_data: dict[str, Any],
    ) -> float:
        """Calculate risk score (0-1, higher = more risky)."""
        try:
            risk_factors = {}

            # Liquidity risk
            liquidity = token_data.get("liquidity", {})
            if isinstance(liquidity, dict):
                liquidity_usd = liquidity.get("usd", 0)
                if liquidity_usd < 10000:
                    risk_factors["liquidity"] = 0.9
                elif liquidity_usd < 100000:
                    risk_factors["liquidity"] = 0.6
                elif liquidity_usd < 1000000:
                    risk_factors["liquidity"] = 0.3
                else:
                    risk_factors["liquidity"] = 0.1
            else:
                risk_factors["liquidity"] = 0.5  # Unknown liquidity

            # Volatility risk
            volatility = technical_indicators.get("volatility", {})
            if isinstance(volatility, dict):
                daily_volatility = volatility.get("daily", 0)
                if daily_volatility > 0.5:  # >50% daily volatility
                    risk_factors["volatility"] = 0.9
                elif daily_volatility > 0.3:
                    risk_factors["volatility"] = 0.6
                elif daily_volatility > 0.1:
                    risk_factors["volatility"] = 0.3
                else:
                    risk_factors["volatility"] = 0.1
            else:
                risk_factors["volatility"] = 0.5

            # Holder concentration risk
            holders = token_data.get("holder_count", 0)
            if holders < 100:
                risk_factors["holder_concentration"] = 0.9
            elif holders < 1000:
                risk_factors["holder_concentration"] = 0.6
            elif holders < 10000:
                risk_factors["holder_concentration"] = 0.3
            else:
                risk_factors["holder_concentration"] = 0.1

            # Contract security risk
            security = token_data.get("security_score", 0.5)
            risk_factors["contract_security"] = 1.0 - security

            # Market cap risk
            market_cap = token_data.get("market_cap", 0)
            if market_cap < 1000000:  # <$1M
                risk_factors["market_cap"] = 0.9
            elif market_cap < 10000000:  # <$10M
                risk_factors["market_cap"] = 0.6
            elif market_cap < 100000000:  # <$100M
                risk_factors["market_cap"] = 0.3
            else:
                risk_factors["market_cap"] = 0.1

            # Age risk
            age_days = token_data.get("age_days", 0)
            if age_days < 7:
                risk_factors["age"] = 0.9
            elif age_days < 30:
                risk_factors["age"] = 0.6
            elif age_days < 180:
                risk_factors["age"] = 0.3
            else:
                risk_factors["age"] = 0.1

            # Calculate weighted risk score
            weighted_score = sum(
                risk_factors.get(factor, 0.5) * weight
                for factor, weight in self.risk_weights.items()
            )

            return min(max(weighted_score, 0.0), 1.0)

        except Exception as e:
            self.logger.error(f"Error calculating risk score: {e}")
            return 0.5  # Default medium risk

    async def _calculate_alpha_score(
        self,
        token_data: dict[str, Any],
        technical_indicators: dict[str, Any],
        sentiment_data: dict[str, Any],
    ) -> float:
        """Calculate alpha score (0-1, higher = more potential)."""
        try:
            alpha_factors = {}

            # Volume trend
            volume = technical_indicators.get("volume", {})
            if isinstance(volume, dict):
                volume_trend = volume.get("trend", 0)
                if volume_trend > 0.5:
                    alpha_factors["volume_trend"] = 0.9
                elif volume_trend > 0.2:
                    alpha_factors["volume_trend"] = 0.6
                elif volume_trend > -0.2:
                    alpha_factors["volume_trend"] = 0.3
                else:
                    alpha_factors["volume_trend"] = 0.1
            else:
                alpha_factors["volume_trend"] = 0.5

            # Price momentum
            momentum = technical_indicators.get("momentum", {})
            if isinstance(momentum, dict):
                momentum_score = momentum.get("score", 0)
                alpha_factors["price_momentum"] = max(
                    min((momentum_score + 1) / 2, 1.0), 0.0
                )
            else:
                alpha_factors["price_momentum"] = 0.5

            # Social sentiment
            sentiment = sentiment_data.get("overall_sentiment", 0.5)
            alpha_factors["social_sentiment"] = sentiment

            # Technical signals
            signals = technical_indicators.get("signals", {})
            if isinstance(signals, dict):
                signal_strength = signals.get("strength", 0)
                alpha_factors["technical_signals"] = max(
                    min((signal_strength + 1) / 2, 1.0), 0.0
                )
            else:
                alpha_factors["technical_signals"] = 0.5

            # Fundamental growth indicators
            growth_score = 0.5
            if token_data.get("volume_24h_change", 0) > 0.2:  # >20% volume increase
                growth_score += 0.2
            if token_data.get("price_change_24h", 0) > 0.1:  # >10% price increase
                growth_score += 0.2
            if token_data.get("holder_count_change", 0) > 0.1:  # >10% holder increase
                growth_score += 0.1

            alpha_factors["fundamental_growth"] = min(growth_score, 1.0)

            # Calculate weighted alpha score
            weighted_score = sum(
                alpha_factors.get(factor, 0.5) * weight
                for factor, weight in self.alpha_weights.items()
            )

            return min(max(weighted_score, 0.0), 1.0)

        except Exception as e:
            self.logger.error(f"Error calculating alpha score: {e}")
            return 0.5  # Default medium alpha

    def _generate_recommendation(self, risk_score: float, alpha_score: float) -> str:
        """Generate investment recommendation based on risk and alpha scores."""
        # Risk-adjusted recommendation
        if risk_score > 0.8:  # Very high risk
            if alpha_score > 0.9:
                return "HIGH_RISK_HIGH_REWARD"
            else:
                return "AVOID"
        elif risk_score > 0.6:  # High risk
            if alpha_score > 0.7:
                return "SPECULATIVE_BUY"
            elif alpha_score > 0.4:
                return "CAUTIOUS_HOLD"
            else:
                return "SELL"
        elif risk_score > 0.4:  # Medium risk
            if alpha_score > 0.6:
                return "BUY"
            elif alpha_score > 0.3:
                return "HOLD"
            else:
                return "SELL"
        else:  # Low risk
            if alpha_score > 0.5:
                return "STRONG_BUY"
            elif alpha_score > 0.3:
                return "BUY"
            else:
                return "HOLD"

    def _calculate_confidence(
        self,
        token_data: dict[str, Any],
        technical_indicators: dict[str, Any],
        sentiment_data: dict[str, Any],
    ) -> float:
        """Calculate confidence in the analysis."""
        confidence_factors = []

        # Data completeness
        data_completeness = 0
        if token_data.get("market_cap"):
            data_completeness += 0.2
        if token_data.get("liquidity"):
            data_completeness += 0.2
        if technical_indicators:
            data_completeness += 0.3
        if sentiment_data:
            data_completeness += 0.3

        confidence_factors.append(data_completeness)

        # Data freshness (assume recent data is more reliable)
        confidence_factors.append(0.8)  # Default high confidence in freshness

        # Market conditions (stable markets = higher confidence)
        market_volatility = technical_indicators.get("volatility", {}).get("daily", 0.5)
        market_confidence = max(0.2, 1.0 - market_volatility)
        confidence_factors.append(market_confidence)

        return sum(confidence_factors) / len(confidence_factors)

    def _generate_summary(
        self,
        token_data: dict[str, Any],
        risk_score: float,
        alpha_score: float,
        recommendation: str,
        confidence: float,
    ) -> str:
        """Generate narrative summary of the analysis."""
        symbol = token_data.get("symbol", "TOKEN")
        name = token_data.get("name", "Unknown Token")

        risk_level = (
            "LOW" if risk_score < 0.3 else "MEDIUM" if risk_score < 0.7 else "HIGH"
        )
        alpha_level = (
            "LOW" if alpha_score < 0.3 else "MEDIUM" if alpha_score < 0.7 else "HIGH"
        )

        summary = f"{name} ({symbol}) Analysis:\n\n"
        summary += f"Risk Level: {risk_level} ({risk_score:.2f})\n"
        summary += f"Alpha Potential: {alpha_level} ({alpha_score:.2f})\n"
        summary += f"Recommendation: {recommendation}\n"
        summary += f"Confidence: {confidence:.2f}\n\n"

        # Add key insights based on scores
        if risk_score > 0.7 and alpha_score > 0.7:
            summary += "This is a high-risk, high-reward opportunity. Suitable only for risk-tolerant investors.\n"
        elif risk_score < 0.3 and alpha_score > 0.6:
            summary += "This appears to be a strong opportunity with manageable risk.\n"
        elif risk_score > 0.7:
            summary += "High risk profile suggests caution. Consider position sizing carefully.\n"
        elif alpha_score < 0.3:
            summary += "Limited upside potential identified. May be better opportunities available.\n"

        return summary

    def _identify_risk_factors(
        self, token_data: dict[str, Any], technical_indicators: dict[str, Any]
    ) -> list[str]:
        """Identify key risk factors."""
        risks = []

        if token_data.get("age_days", 0) < 7:
            risks.append("Very new token (less than 1 week old)")

        liquidity = token_data.get("liquidity", {})
        if isinstance(liquidity, dict) and liquidity.get("usd", 0) < 50000:
            risks.append("Low liquidity (less than $50k)")

        if token_data.get("holder_count", 0) < 100:
            risks.append("Low holder count (less than 100 holders)")

        volatility = technical_indicators.get("volatility", {})
        if isinstance(volatility, dict) and volatility.get("daily", 0) > 0.5:
            risks.append("High volatility (>50% daily)")

        return risks

    def _identify_opportunities(
        self,
        token_data: dict[str, Any],
        technical_indicators: dict[str, Any],
        sentiment_data: dict[str, Any],
    ) -> list[str]:
        """Identify key opportunity factors."""
        opportunities = []

        if token_data.get("volume_24h_change", 0) > 0.5:
            opportunities.append("Strong volume growth (>50% increase)")

        momentum = technical_indicators.get("momentum", {})
        if isinstance(momentum, dict) and momentum.get("score", 0) > 0.5:
            opportunities.append("Positive price momentum")

        sentiment = sentiment_data.get("overall_sentiment", 0)
        if sentiment > 0.7:
            opportunities.append("Strong positive sentiment")

        if token_data.get("holder_count_change", 0) > 0.2:
            opportunities.append("Growing holder base (>20% increase)")

        return opportunities

    def _extract_key_metrics(
        self,
        token_data: dict[str, Any],
        technical_indicators: dict[str, Any],
        sentiment_data: dict[str, Any],
    ) -> dict[str, Any]:
        """Extract key metrics for summary."""
        return {
            "market_cap": token_data.get("market_cap"),
            "volume_24h": token_data.get("volume_24h"),
            "price_change_24h": token_data.get("price_change_24h"),
            "holder_count": token_data.get("holder_count"),
            "liquidity_usd": token_data.get("liquidity", {}).get("usd")
            if isinstance(token_data.get("liquidity"), dict)
            else None,
            "volatility": technical_indicators.get("volatility", {}).get("daily")
            if isinstance(technical_indicators.get("volatility"), dict)
            else None,
            "sentiment_score": sentiment_data.get("overall_sentiment"),
            "age_days": token_data.get("age_days"),
        }

    async def _store_analysis(self, analysis: dict[str, Any]) -> None:
        """Store analysis results in database."""
        try:
            if not self.db_manager:
                return

            await self.db_manager.execute_query(
                """
                INSERT OR REPLACE INTO token_analyses (
                    token_address, symbol, risk_score, alpha_score,
                    recommendation, confidence, summary, analysis_data,
                    created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    analysis["token_address"],
                    analysis["symbol"],
                    analysis["risk_score"],
                    analysis["alpha_score"],
                    analysis["recommendation"],
                    analysis["confidence"],
                    analysis["summary"],
                    json.dumps(analysis),
                    datetime.utcnow().isoformat(),
                ),
            )

        except Exception as e:
            self.logger.error(f"Failed to store analysis: {e}")

    async def get_metrics(self) -> dict[str, Any]:
        """Get agent-specific metrics."""
        try:
            # Return basic metrics
            return {
                "analyses_completed": 0,  # Would track in production
                "average_confidence": 0.75,  # Would calculate from stored analyses
                "average_risk_score": 0.5,
                "average_alpha_score": 0.6,
            }
        except Exception as e:
            self.logger.error(f"Error getting analyst metrics: {e}")
            return {}
