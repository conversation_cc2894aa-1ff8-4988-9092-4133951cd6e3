"""
REST API layer for the Token Analyzer system.
Provides HTTP endpoints for external access to token analysis functionality.
"""

import asyncio
from contextlib import asynccontextmanager
from datetime import datetime, timezone
from typing import Any

from fastapi import BackgroundTasks, Depends, FastAPI, HTTPException, Request, WebSocket
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import uuid

from ..agents.coordinator import AgentCoordinator
from ..core.cache import CacheManager
from ..core.config import get_settings
from ..core.database import DatabaseManager
from ..core.logging_config import setup_logging, get_logger
from ..integrations.metrics import MetricsCollector
from ..security.api_inventory import api_inventory_manager
from ..security.bopla_detector import bopla_detector
from ..security.ai_bot_detector import ai_bot_detector
from ..security.threat_intelligence import threat_intelligence_engine
from ..security.threshold_calibration import threshold_calibrator, ThresholdType
from ..security.token_whitelist import token_whitelist_manager, WhitelistCategory
from ..ml.model_weight_optimizer import MLModelWeightOptimizer, OptimizationStrategy
from ..ml.risk_calibration import risk_calibration_system
from ..monitoring.accuracy_monitor import accuracy_monitor
from ..monitoring.performance_telemetry import performance_telemetry, PerformanceMiddleware
from ..compliance.compliance_automation import compliance_automation

# Initialize enhanced logging system (2025 best practices)
setup_logging()
logger = get_logger(__name__)


# ==================== PYDANTIC MODELS ====================


class TokenAnalysisRequest(BaseModel):
    """Request model for token analysis."""

    token_address: str = Field(..., description="Token contract address")
    chain_id: int = Field(default=1, description="Blockchain network ID")
    analysis_types: list[str] | None = Field(
        default=None, description="Specific analysis types to run"
    )
    priority: str = Field(default="medium", description="Analysis priority")


class BatchAnalysisRequest(BaseModel):
    """Request model for batch token analysis."""

    tokens: list[dict[str, Any]] = Field(..., description="List of tokens to analyze")
    analysis_types: list[str] | None = Field(
        default=None, description="Specific analysis types to run"
    )
    priority: str = Field(default="medium", description="Analysis priority")


class DiscoveryRequest(BaseModel):
    """Request model for token discovery."""

    sources: list[str] = Field(
        default=["defillama", "dexscreener"], description="Discovery sources"
    )
    limit: int = Field(default=50, description="Maximum number of tokens to discover")
    min_age_hours: int = Field(default=24, description="Minimum token age in hours")


class TokenAnalysisResponse(BaseModel):
    """Response model for token analysis."""

    token_address: str
    chain_id: int
    analysis_id: str
    timestamp: str
    overall_score: float | None = None
    risk_score: float | None = None
    investment_recommendation: str | None = None
    confidence_level: float | None = None
    execution_time_ms: int | None = None
    discovery_data: dict[str, Any] | None = None
    validation_results: dict[str, Any] | None = None
    chain_info: dict[str, Any] | None = None
    market_data: dict[str, Any] | None = None
    technical_analysis: dict[str, Any] | None = None
    sentiment_analysis: dict[str, Any] | None = None


class HealthResponse(BaseModel):
    """Response model for health check."""

    status: str
    timestamp: str
    version: str
    components: dict[str, Any]


# ==================== DEPENDENCY INJECTION ====================


class AppContext:
    """Application context for dependency injection."""

    def __init__(self):
        self.db_manager: DatabaseManager | None = None
        self.cache_manager: CacheManager | None = None
        self.coordinator: AgentCoordinator | None = None
        self.metrics_collector: MetricsCollector | None = None
        self.settings = get_settings()


app_context = AppContext()


async def get_coordinator() -> AgentCoordinator:
    """Get agent coordinator dependency."""
    if not app_context.coordinator:
        raise HTTPException(status_code=503, detail="Agent coordinator not available")
    return app_context.coordinator


async def get_metrics_collector() -> MetricsCollector:
    """Get metrics collector dependency."""
    if not app_context.metrics_collector:
        raise HTTPException(status_code=503, detail="Metrics collector not available")
    return app_context.metrics_collector


# ==================== LIFESPAN MANAGEMENT ====================


@asynccontextmanager
async def lifespan(_app: FastAPI):
    """Manage application lifecycle."""
    logger.info("Starting Token Analyzer API")

    try:
        # Initialize core components
        app_context.db_manager = DatabaseManager()
        app_context.cache_manager = CacheManager()
        app_context.metrics_collector = MetricsCollector()

        await app_context.db_manager.initialize()
        await app_context.cache_manager.initialize()
        await app_context.metrics_collector.start()

        # Initialize coordinator
        app_context.coordinator = AgentCoordinator(
            db_manager=app_context.db_manager,
            cache_manager=app_context.cache_manager,
            metrics_collector=app_context.metrics_collector,
        )
        await app_context.coordinator.initialize()

        # Initialize API inventory with initial discovery
        try:
            logger.info("Starting API inventory discovery...")
            discovery_result = await api_inventory_manager.discover_apis()
            logger.info("API inventory discovery completed", **discovery_result)

            # Start continuous monitoring
            asyncio.create_task(api_inventory_manager.start_continuous_monitoring())
        except Exception as e:
            logger.error(f"Failed to initialize API inventory: {e}")

        # Initialize threat intelligence feeds
        try:
            logger.info("Starting threat intelligence feeds...")
            await threat_intelligence_engine.start_threat_intelligence_feeds()
            logger.info("Threat intelligence feeds started successfully")
        except Exception as e:
            logger.error(f"Failed to initialize threat intelligence: {e}")

        # Initialize token whitelist validation
        try:
            logger.info("Starting token whitelist automatic validation...")
            asyncio.create_task(token_whitelist_manager.start_automatic_validation())
            logger.info("Token whitelist validation started successfully")
        except Exception as e:
            logger.error(f"Failed to initialize token whitelist: {e}")

        # Initialize ML weight optimizer
        try:
            logger.info("Initializing ML weight optimizer...")
            global weight_optimizer
            weight_optimizer = MLModelWeightOptimizer(risk_calibration_system.ensemble_model)
            risk_calibration_system.weight_optimizer = weight_optimizer

            # Start automatic optimization
            asyncio.create_task(weight_optimizer.start_automatic_optimization())
            logger.info("ML weight optimizer initialized and started")
        except Exception as e:
            logger.error(f"Failed to initialize weight optimizer: {e}")

        # Initialize accuracy monitoring
        try:
            logger.info("Starting real-time accuracy monitoring...")
            asyncio.create_task(accuracy_monitor.start_monitoring())
            logger.info("Real-time accuracy monitoring started successfully")
        except Exception as e:
            logger.error(f"Failed to initialize accuracy monitoring: {e}")

        # Initialize performance telemetry
        try:
            logger.info("Starting advanced performance telemetry...")
            asyncio.create_task(performance_telemetry.start_monitoring())
            logger.info("Advanced performance telemetry started successfully")
        except Exception as e:
            logger.error(f"Failed to initialize performance telemetry: {e}")

        # Initialize compliance automation
        try:
            logger.info("Starting compliance monitoring automation...")
            asyncio.create_task(compliance_automation.start_monitoring())
            logger.info("Compliance monitoring automation started successfully")
        except Exception as e:
            logger.error(f"Failed to initialize compliance automation: {e}")

        logger.info("Token Analyzer API started successfully")
        yield

    finally:
        # Cleanup
        logger.info("Shutting down Token Analyzer API")

        if app_context.coordinator:
            await app_context.coordinator.shutdown()
        if app_context.metrics_collector:
            await app_context.metrics_collector.stop()
        if app_context.cache_manager:
            await app_context.cache_manager.shutdown()
        if app_context.db_manager:
            await app_context.db_manager.close()


# ==================== FASTAPI APP ====================

app = FastAPI(
    title="Token Analyzer API",
    description="Advanced cryptocurrency token analysis and discovery API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add performance monitoring middleware
app.add_middleware(PerformanceMiddleware, telemetry=performance_telemetry)


# Add correlation tracking middleware (2025 best practice)
@app.middleware("http")
async def correlation_middleware(request: Request, call_next):
    """Add correlation ID to all requests for distributed tracing."""
    correlation_id_value = request.headers.get("X-Correlation-ID") or str(uuid.uuid4())
    request_id_value = request.headers.get("X-Request-ID") or str(uuid.uuid4())

    # Set context variables for logging
    from ..core.logging_config import correlation_id, request_id
    correlation_token = correlation_id.set(correlation_id_value)
    request_token = request_id.set(request_id_value)

    try:
        # Process request
        response = await call_next(request)

        # Add correlation headers to response
        response.headers["X-Correlation-ID"] = correlation_id_value
        response.headers["X-Request-ID"] = request_id_value

        return response
    finally:
        # Clean up context
        correlation_id.reset(correlation_token)
        request_id.reset(request_token)


# ==================== HEALTH & STATUS ENDPOINTS ====================


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    try:
        components = {}

        # Check coordinator health
        if app_context.coordinator:
            coordinator_health = await app_context.coordinator.health_check()
            components["coordinator"] = {
                "status": "healthy" if coordinator_health else "unhealthy"
            }

        # Check agent status
        if app_context.coordinator:
            agent_status = await app_context.coordinator.get_agent_status()
            components["agents"] = agent_status

        # Check database
        if app_context.db_manager:
            try:
                await app_context.db_manager.execute_query("SELECT 1")
                components["database"] = {"status": "healthy"}
            except Exception as e:
                components["database"] = {"status": "unhealthy", "error": str(e)}

        # Check cache
        if app_context.cache_manager:
            try:
                await app_context.cache_manager.set("health_check", "ok", ttl=60)
                components["cache"] = {"status": "healthy"}
            except Exception as e:
                components["cache"] = {"status": "unhealthy", "error": str(e)}

        # Determine overall status
        overall_status = "healthy"
        for component in components.values():
            if isinstance(component, dict) and component.get("status") != "healthy":
                if "agents" in str(component):
                    # Check if any agents are unhealthy
                    for agent_status in component.values():
                        if isinstance(agent_status, dict) and not agent_status.get(
                            "healthy", True
                        ):
                            overall_status = "degraded"
                            break
                else:
                    overall_status = "unhealthy"
                    break

        return HealthResponse(
            status=overall_status,
            timestamp=datetime.now(timezone.utc).isoformat(),
            version="1.0.0",
            components=components,
        )

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthResponse(
            status="error",
            timestamp=datetime.now(timezone.utc).isoformat(),
            version="1.0.0",
            components={"error": str(e)},
        )


@app.get("/metrics")
async def get_metrics(
    metrics_collector: MetricsCollector = Depends(get_metrics_collector),
):
    """Get system metrics."""
    try:
        return {
            "agent_metrics": metrics_collector.get_agent_metrics(),
            "counters": metrics_collector.get_counters(),
            "gauges": metrics_collector.get_gauges(),
            "system_health": metrics_collector.get_system_health(),
        }
    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== API INVENTORY ENDPOINTS ====================

@app.get("/api-inventory/report")
async def get_api_inventory_report():
    """Get comprehensive API inventory report."""
    try:
        report = api_inventory_manager.generate_inventory_report()
        return report
    except Exception as e:
        logger.error(f"Failed to get API inventory report: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api-inventory/endpoint/{endpoint_id}")
async def get_endpoint_details(endpoint_id: str):
    """Get detailed information about a specific API endpoint."""
    try:
        details = api_inventory_manager.get_endpoint_details(endpoint_id)
        if not details:
            raise HTTPException(status_code=404, detail="Endpoint not found")
        return details
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get endpoint details: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api-inventory/discover")
async def trigger_api_discovery(scan_paths: list[str] = None):
    """Trigger API discovery scan."""
    try:
        result = await api_inventory_manager.discover_apis(scan_paths)
        return result
    except Exception as e:
        logger.error(f"Failed to trigger API discovery: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== BOPLA SECURITY ENDPOINTS ====================

@app.get("/security/bopla/report")
async def get_bopla_report():
    """Get comprehensive BOPLA security report."""
    try:
        report = bopla_detector.generate_bopla_report()
        return report
    except Exception as e:
        logger.error(f"Failed to get BOPLA report: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/security/bopla/user/{user_id}")
async def get_user_bopla_summary(user_id: str):
    """Get BOPLA access summary for a specific user."""
    try:
        summary = bopla_detector.get_user_access_summary(user_id)
        if not summary:
            raise HTTPException(status_code=404, detail="User not found or no access data")
        return summary
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get user BOPLA summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/security/bopla/alerts")
async def get_bopla_alerts(limit: int = 100):
    """Get recent BOPLA security alerts."""
    try:
        with bopla_detector._lock:
            recent_alerts = sorted(
                bopla_detector.alerts,
                key=lambda x: x.timestamp,
                reverse=True
            )[:limit]

            return {
                "alerts": [
                    {
                        "alert_id": alert.alert_id,
                        "user_id": alert.user_id,
                        "user_role": alert.user_role.value,
                        "violation_type": alert.violation_type,
                        "field_path": alert.field_path,
                        "endpoint": alert.endpoint,
                        "severity": alert.severity,
                        "timestamp": alert.timestamp.isoformat(),
                        "risk_score": alert.risk_score,
                        "details": alert.details
                    }
                    for alert in recent_alerts
                ],
                "total_alerts": len(bopla_detector.alerts)
            }
    except Exception as e:
        logger.error(f"Failed to get BOPLA alerts: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== AI BOT DETECTION ENDPOINTS ====================

@app.get("/security/bot-detection/report")
async def get_bot_detection_report():
    """Get comprehensive AI bot detection report."""
    try:
        report = ai_bot_detector.generate_bot_report()
        return report
    except Exception as e:
        logger.error(f"Failed to get bot detection report: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/security/bot-detection/alerts")
async def get_bot_alerts(limit: int = 100):
    """Get recent bot detection alerts."""
    try:
        with ai_bot_detector._lock:
            recent_alerts = sorted(
                ai_bot_detector.alerts,
                key=lambda x: x.timestamp,
                reverse=True
            )[:limit]

            return {
                "alerts": [
                    {
                        "alert_id": alert.alert_id,
                        "ip_address": alert.ip_address,
                        "user_agent": alert.user_agent,
                        "bot_type": alert.bot_type.value,
                        "confidence": alert.confidence.value,
                        "risk_score": alert.risk_score,
                        "evasion_techniques": [t.value for t in alert.evasion_techniques],
                        "timestamp": alert.timestamp.isoformat(),
                        "mitigation_applied": alert.mitigation_applied
                    }
                    for alert in recent_alerts
                ],
                "total_alerts": len(ai_bot_detector.alerts)
            }
    except Exception as e:
        logger.error(f"Failed to get bot alerts: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/security/bot-detection/ip/{ip_address}")
async def get_ip_bot_analysis(ip_address: str):
    """Get bot analysis for a specific IP address."""
    try:
        patterns = ai_bot_detector.bot_patterns.get(ip_address)
        if not patterns:
            raise HTTPException(status_code=404, detail="IP address not found in analysis")

        # Get recent alerts for this IP
        ip_alerts = [alert for alert in ai_bot_detector.alerts if alert.ip_address == ip_address]

        return {
            "ip_address": ip_address,
            "analysis_summary": {
                "total_requests": len(patterns.get("path_sequence", [])),
                "unique_paths": len(set(patterns.get("path_sequence", []))),
                "user_agents_seen": len(patterns.get("user_agents", set())),
                "last_analysis": patterns.get("last_analysis").details if patterns.get("last_analysis") else None,
                "bot_probability_trend": patterns.get("bot_probability_history", [])[-10:]  # Last 10 scores
            },
            "recent_alerts": [
                {
                    "alert_id": alert.alert_id,
                    "bot_type": alert.bot_type.value,
                    "confidence": alert.confidence.value,
                    "risk_score": alert.risk_score,
                    "timestamp": alert.timestamp.isoformat()
                }
                for alert in ip_alerts[-5:]  # Last 5 alerts
            ]
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get IP bot analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== THREAT INTELLIGENCE ENDPOINTS ====================

@app.get("/security/threat-intelligence/report")
async def get_threat_intelligence_report():
    """Get comprehensive threat intelligence report."""
    try:
        report = threat_intelligence_engine.generate_threat_intelligence_report()
        return report
    except Exception as e:
        logger.error(f"Failed to get threat intelligence report: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/security/threat-intelligence/ioc/{ioc_id}")
async def get_ioc_details(ioc_id: str):
    """Get detailed information about a specific IOC."""
    try:
        details = threat_intelligence_engine.get_ioc_details(ioc_id)
        if not details:
            raise HTTPException(status_code=404, detail="IOC not found")
        return details
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get IOC details: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/security/threat-intelligence/check")
async def check_threat_indicators(indicators: dict):
    """Check indicators against threat intelligence database."""
    try:
        matches = await threat_intelligence_engine.check_threat_intelligence(indicators)
        return {
            "matches_found": len(matches),
            "matches": [
                {
                    "ioc_id": match.ioc.id,
                    "ioc_type": match.ioc.ioc_type.value,
                    "threat_type": match.ioc.threat_type.value,
                    "severity": match.ioc.severity.value,
                    "matched_value": match.matched_value,
                    "match_type": match.match_type,
                    "risk_score": match.risk_score,
                    "timestamp": match.timestamp.isoformat()
                }
                for match in matches
            ]
        }
    except Exception as e:
        logger.error(f"Failed to check threat indicators: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/security/threat-intelligence/stats")
async def get_threat_intelligence_stats():
    """Get threat intelligence statistics."""
    try:
        with threat_intelligence_engine._lock:
            stats = threat_intelligence_engine.stats.copy()

            # Add additional statistics
            from ..security.threat_intelligence import IOCType, ThreatType
            stats.update({
                "ioc_distribution": {
                    ioc_type.value: sum(1 for ioc in threat_intelligence_engine.iocs.values()
                                      if ioc.ioc_type == ioc_type)
                    for ioc_type in IOCType
                },
                "threat_distribution": {
                    threat_type.value: sum(1 for ioc in threat_intelligence_engine.iocs.values()
                                         if ioc.threat_type == threat_type)
                    for threat_type in ThreatType
                }
            })

        return stats
    except Exception as e:
        logger.error(f"Failed to get threat intelligence stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== THRESHOLD CALIBRATION ENDPOINTS ====================

@app.get("/security/threshold-calibration/report")
async def get_threshold_calibration_report():
    """Get comprehensive threshold calibration report."""
    try:
        report = threshold_calibrator.generate_calibration_report()
        return report
    except Exception as e:
        logger.error(f"Failed to get threshold calibration report: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/security/threshold-calibration/analyze/{threshold_type}")
async def analyze_threshold_performance(threshold_type: str, time_window_hours: int = 24):
    """Analyze threshold performance and get calibration recommendations."""
    try:
        # Convert string to enum
        threshold_enum = ThresholdType(threshold_type)
        result = await threshold_calibrator.analyze_threshold_performance(threshold_enum, time_window_hours)

        return {
            "threshold_type": result.threshold_type.value,
            "current_value": result.current_value,
            "recommended_value": result.recommended_value,
            "confidence": result.confidence,
            "expected_improvement": result.expected_improvement,
            "risk_assessment": result.risk_assessment,
            "requires_experiment": result.requires_experiment
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid threshold type: {threshold_type}")
    except Exception as e:
        logger.error(f"Failed to analyze threshold performance: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/security/threshold-calibration/experiment")
async def create_threshold_experiment(threshold_type: str, test_threshold: float, duration_hours: int = 48):
    """Create A/B test experiment for threshold optimization."""
    try:
        threshold_enum = ThresholdType(threshold_type)
        experiment_id = await threshold_calibrator.create_threshold_experiment(
            threshold_enum, test_threshold, duration_hours
        )
        return {"experiment_id": experiment_id, "status": "created"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to create threshold experiment: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/security/threshold-calibration/experiment/{experiment_id}")
async def get_experiment_analysis(experiment_id: str):
    """Get analysis of A/B test experiment."""
    try:
        analysis = await threshold_calibrator.analyze_experiment(experiment_id)
        return analysis
    except Exception as e:
        logger.error(f"Failed to get experiment analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/security/threshold-calibration/experiment/{experiment_id}/apply")
async def apply_experiment_results(experiment_id: str):
    """Apply results of successful A/B test experiment."""
    try:
        success = await threshold_calibrator.apply_experiment_results(experiment_id)
        return {"success": success, "message": "Results applied" if success else "Results not applied"}
    except Exception as e:
        logger.error(f"Failed to apply experiment results: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/security/threshold-calibration/threshold/{threshold_type}")
async def get_threshold_value(threshold_type: str, request_id: str = None):
    """Get current threshold value (considering A/B tests)."""
    try:
        threshold_enum = ThresholdType(threshold_type)
        value = threshold_calibrator.get_threshold_value(threshold_enum, request_id)
        return {"threshold_type": threshold_type, "value": value}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid threshold type: {threshold_type}")
    except Exception as e:
        logger.error(f"Failed to get threshold value: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== TOKEN WHITELIST ENDPOINTS ====================

@app.get("/security/token-whitelist/report")
async def get_token_whitelist_report():
    """Get comprehensive token whitelist report."""
    try:
        report = token_whitelist_manager.generate_whitelist_report()
        return report
    except Exception as e:
        logger.error(f"Failed to get token whitelist report: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/security/token-whitelist/check/{token_address}")
async def check_token_whitelist_status(token_address: str, chain_id: int):
    """Check if a token is whitelisted."""
    try:
        is_whitelisted = token_whitelist_manager.is_whitelisted(token_address, chain_id)
        entry = token_whitelist_manager.get_whitelist_entry(token_address, chain_id) if is_whitelisted else None

        result = {
            "token_address": token_address,
            "chain_id": chain_id,
            "is_whitelisted": is_whitelisted
        }

        if entry:
            result.update({
                "symbol": entry.symbol,
                "name": entry.name,
                "category": entry.category.value,
                "risk_adjustment": entry.risk_score_adjustment,
                "status": entry.status.value
            })

        return result
    except Exception as e:
        logger.error(f"Failed to check token whitelist status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/security/token-whitelist/add")
async def add_token_to_whitelist(
    token_address: str,
    chain_id: int,
    symbol: str,
    name: str,
    category: str,
    risk_adjustment: float = 0.1,
    notes: str = ""
):
    """Add a token to the whitelist."""
    try:
        # Convert string to enum
        category_enum = WhitelistCategory(category)

        success = await token_whitelist_manager.add_token_to_whitelist(
            token_address, chain_id, symbol, name, category_enum, risk_adjustment, notes
        )

        return {"success": success, "message": "Token added to whitelist" if success else "Failed to add token"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid category: {category}")
    except Exception as e:
        logger.error(f"Failed to add token to whitelist: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/security/token-whitelist/remove/{token_address}")
async def remove_token_from_whitelist(token_address: str, chain_id: int, reason: str = ""):
    """Remove a token from the whitelist."""
    try:
        success = await token_whitelist_manager.remove_token_from_whitelist(token_address, chain_id, reason)
        return {"success": success, "message": "Token removed from whitelist" if success else "Token not found"}
    except Exception as e:
        logger.error(f"Failed to remove token from whitelist: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/security/token-whitelist/validate")
async def validate_whitelist():
    """Validate all whitelisted tokens against current criteria."""
    try:
        results = await token_whitelist_manager.validate_whitelist()
        return results
    except Exception as e:
        logger.error(f"Failed to validate whitelist: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/security/token-whitelist/category/{category}")
async def get_tokens_by_category(category: str):
    """Get all whitelisted tokens by category."""
    try:
        category_enum = WhitelistCategory(category)
        tokens = token_whitelist_manager.get_whitelist_by_category(category_enum)

        return {
            "category": category,
            "tokens": [
                {
                    "token_address": token.token_address,
                    "chain_id": token.chain_id,
                    "symbol": token.symbol,
                    "name": token.name,
                    "risk_adjustment": token.risk_score_adjustment,
                    "status": token.status.value
                }
                for token in tokens
            ]
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid category: {category}")
    except Exception as e:
        logger.error(f"Failed to get tokens by category: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/security/token-whitelist/search")
async def search_whitelist(query: str):
    """Search whitelist by symbol or name."""
    try:
        tokens = token_whitelist_manager.search_whitelist(query)

        return {
            "query": query,
            "results": [
                {
                    "token_address": token.token_address,
                    "chain_id": token.chain_id,
                    "symbol": token.symbol,
                    "name": token.name,
                    "category": token.category.value,
                    "risk_adjustment": token.risk_score_adjustment
                }
                for token in tokens
            ]
        }
    except Exception as e:
        logger.error(f"Failed to search whitelist: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== ML MODEL WEIGHT OPTIMIZATION ENDPOINTS ====================

# Initialize weight optimizer
weight_optimizer = None


@app.get("/ml/weight-optimization/report")
async def get_weight_optimization_report():
    """Get comprehensive weight optimization report."""
    try:
        if not weight_optimizer:
            raise HTTPException(status_code=503, detail="Weight optimizer not initialized")

        report = weight_optimizer.generate_optimization_report()
        return report
    except Exception as e:
        logger.error(f"Failed to get weight optimization report: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/ml/weight-optimization/optimize")
async def optimize_model_weights(strategy: str = "multi_objective"):
    """Optimize ensemble model weights using specified strategy."""
    try:
        if not weight_optimizer:
            raise HTTPException(status_code=503, detail="Weight optimizer not initialized")

        # Convert string to enum
        strategy_enum = OptimizationStrategy(strategy)
        result = await weight_optimizer.optimize_weights(strategy_enum)

        return {
            "strategy": result.strategy.value,
            "optimization_score": result.optimization_score,
            "performance_improvement": result.performance_improvement,
            "convergence_achieved": result.convergence_achieved,
            "iterations": result.iterations,
            "confidence": result.confidence
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid strategy: {strategy}")
    except Exception as e:
        logger.error(f"Failed to optimize model weights: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/ml/weight-optimization/current-weights")
async def get_current_model_weights():
    """Get current ensemble model weights."""
    try:
        if not weight_optimizer:
            raise HTTPException(status_code=503, detail="Weight optimizer not initialized")

        weights = {model_type.value: weight for model_type, weight in weight_optimizer.ensemble_model.model_weights.items()}
        return {"current_weights": weights}
    except Exception as e:
        logger.error(f"Failed to get current model weights: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/ml/weight-optimization/performance/{model_type}")
async def get_model_performance(model_type: str):
    """Get performance metrics for a specific model."""
    try:
        if not weight_optimizer:
            raise HTTPException(status_code=503, detail="Weight optimizer not initialized")

        # Find model type
        from ..ml.risk_calibration import ModelType
        model_enum = None
        for mt in ModelType:
            if mt.value == model_type:
                model_enum = mt
                break

        if not model_enum or model_enum not in weight_optimizer.model_performances:
            raise HTTPException(status_code=404, detail=f"Model type {model_type} not found")

        performance = weight_optimizer.model_performances[model_enum]
        return {
            "model_type": model_type,
            "accuracy": performance.accuracy,
            "precision": performance.precision,
            "recall": performance.recall,
            "f1_score": performance.f1_score,
            "false_positive_rate": performance.false_positive_rate,
            "false_negative_rate": performance.false_negative_rate,
            "sample_count": performance.sample_count,
            "last_updated": performance.last_updated.isoformat()
        }
    except Exception as e:
        logger.error(f"Failed to get model performance: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/ml/weight-optimization/update-performance")
async def update_model_performance(model_type: str, is_correct: bool, is_false_positive: bool = False):
    """Update model performance with prediction result."""
    try:
        if not weight_optimizer:
            raise HTTPException(status_code=503, detail="Weight optimizer not initialized")

        # Find model type
        from ..ml.risk_calibration import ModelType
        model_enum = None
        for mt in ModelType:
            if mt.value == model_type:
                model_enum = mt
                break

        if not model_enum:
            raise HTTPException(status_code=404, detail=f"Model type {model_type} not found")

        # Update performance
        prediction_result = {
            "is_correct": is_correct,
            "is_false_positive": is_false_positive
        }
        weight_optimizer.update_model_performance(model_enum, prediction_result)

        return {"success": True, "message": "Model performance updated"}
    except Exception as e:
        logger.error(f"Failed to update model performance: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== ACCURACY MONITORING ENDPOINTS ====================

@app.get("/monitoring/accuracy/report")
async def get_accuracy_monitoring_report():
    """Get comprehensive accuracy monitoring report."""
    try:
        report = accuracy_monitor.generate_accuracy_report()
        return report
    except Exception as e:
        logger.error(f"Failed to get accuracy monitoring report: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/monitoring/accuracy/record")
async def record_prediction_accuracy(
    prediction_id: str,
    model_name: str,
    is_accurate: bool,
    confidence: float = 1.0,
    labels: dict = None
):
    """Record accuracy for a prediction."""
    try:
        accuracy_monitor.record_prediction_accuracy(
            prediction_id=prediction_id,
            model_name=model_name,
            is_accurate=is_accurate,
            confidence=confidence,
            labels=labels or {}
        )
        return {"success": True, "message": "Accuracy recorded"}
    except Exception as e:
        logger.error(f"Failed to record prediction accuracy: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/monitoring/accuracy/trends")
async def get_accuracy_trends(hours: int = 24):
    """Get accuracy trends analysis."""
    try:
        trends = accuracy_monitor.analyze_accuracy_trends(hours)
        return {
            "trends": [
                {
                    "model_name": trend.model_name,
                    "current_value": trend.current_value,
                    "trend_direction": trend.trend_direction,
                    "trend_strength": trend.trend_strength,
                    "predicted_value": trend.predicted_value,
                    "confidence": trend.confidence,
                    "prediction_horizon_hours": trend.prediction_horizon_hours
                }
                for trend in trends
            ]
        }
    except Exception as e:
        logger.error(f"Failed to get accuracy trends: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/monitoring/accuracy/alerts")
async def get_accuracy_alerts():
    """Get active accuracy alerts."""
    try:
        active_alerts = []
        for alert in accuracy_monitor.accuracy_alerts.values():
            if not alert.resolved:
                active_alerts.append({
                    "alert_id": alert.alert_id,
                    "alert_type": alert.alert_type.value,
                    "severity": alert.severity.value,
                    "message": alert.message,
                    "current_value": alert.current_value,
                    "threshold_value": alert.threshold_value,
                    "model_name": alert.model_name,
                    "timestamp": alert.timestamp.isoformat()
                })

        return {"active_alerts": active_alerts}
    except Exception as e:
        logger.error(f"Failed to get accuracy alerts: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/monitoring/accuracy/stats")
async def get_accuracy_stats():
    """Get current accuracy statistics."""
    try:
        return {
            "overall_accuracy": accuracy_monitor.stats["current_accuracy"],
            "total_predictions": accuracy_monitor.stats["total_predictions"],
            "accurate_predictions": accuracy_monitor.stats["accurate_predictions"],
            "active_alerts": len([a for a in accuracy_monitor.accuracy_alerts.values() if not a.resolved]),
            "total_alerts_triggered": accuracy_monitor.stats["accuracy_alerts_triggered"],
            "last_accuracy_check": accuracy_monitor.stats["last_accuracy_check"].isoformat() if accuracy_monitor.stats["last_accuracy_check"] else None,
            "model_accuracies": accuracy_monitor._get_current_model_accuracies(),
            "thresholds": accuracy_monitor.accuracy_thresholds
        }
    except Exception as e:
        logger.error(f"Failed to get accuracy stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.websocket("/monitoring/accuracy/ws")
async def accuracy_websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time accuracy updates."""
    try:
        await accuracy_monitor.handle_websocket(websocket)
    except Exception as e:
        logger.error(f"Accuracy WebSocket error: {e}")


# ==================== PERFORMANCE TELEMETRY ENDPOINTS ====================

@app.get("/monitoring/performance/report")
async def get_performance_telemetry_report():
    """Get comprehensive performance telemetry report."""
    try:
        report = performance_telemetry.generate_performance_report()
        return report
    except Exception as e:
        logger.error(f"Failed to get performance telemetry report: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/monitoring/performance/cache")
async def record_cache_performance(
    cache_name: str,
    operation: str,
    hit: bool,
    lookup_time_ms: float = 0.0,
    cache_size: int = 0
):
    """Record cache performance metrics."""
    try:
        performance_telemetry.record_cache_performance(
            cache_name=cache_name,
            operation=operation,
            hit=hit,
            lookup_time_ms=lookup_time_ms,
            cache_size=cache_size
        )
        return {"success": True, "message": "Cache performance recorded"}
    except Exception as e:
        logger.error(f"Failed to record cache performance: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/monitoring/performance/custom")
async def record_custom_metric(metric_name: str, value: float, labels: dict = None):
    """Record custom business metric."""
    try:
        performance_telemetry.record_custom_metric(
            metric_name=metric_name,
            value=value,
            labels=labels or {}
        )
        return {"success": True, "message": "Custom metric recorded"}
    except Exception as e:
        logger.error(f"Failed to record custom metric: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/monitoring/performance/trends")
async def get_performance_trends(hours: int = 24):
    """Get performance trends analysis."""
    try:
        trends = performance_telemetry.analyze_performance_trends(hours)
        return {"trends": trends, "analysis_period_hours": hours}
    except Exception as e:
        logger.error(f"Failed to get performance trends: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/monitoring/performance/api")
async def get_api_performance():
    """Get API endpoint performance metrics."""
    try:
        api_metrics = {}
        for endpoint_key, metrics in performance_telemetry.api_metrics.items():
            if metrics.response_times:
                import statistics
                api_metrics[endpoint_key] = {
                    "total_requests": metrics.total_requests,
                    "avg_response_time": statistics.mean(metrics.response_times),
                    "min_response_time": metrics.min_response_time,
                    "max_response_time": metrics.max_response_time,
                    "error_count": metrics.error_count,
                    "error_rate": metrics.error_count / metrics.total_requests if metrics.total_requests > 0 else 0,
                    "last_request": metrics.last_request.isoformat() if metrics.last_request else None
                }

        return {"api_performance": api_metrics}
    except Exception as e:
        logger.error(f"Failed to get API performance: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/monitoring/performance/resources")
async def get_resource_utilization():
    """Get current resource utilization metrics."""
    try:
        if performance_telemetry.resource_history:
            latest_resource = performance_telemetry.resource_history[-1]
            return {
                "cpu_percent": latest_resource.cpu_percent,
                "memory_percent": latest_resource.memory_percent,
                "disk_percent": latest_resource.disk_percent,
                "network_bytes_sent": latest_resource.network_bytes_sent,
                "network_bytes_recv": latest_resource.network_bytes_recv,
                "active_connections": latest_resource.active_connections,
                "process_count": latest_resource.process_count,
                "timestamp": latest_resource.timestamp.isoformat()
            }
        else:
            return {"message": "No resource data available yet"}
    except Exception as e:
        logger.error(f"Failed to get resource utilization: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== COMPLIANCE AUTOMATION ENDPOINTS ====================

@app.get("/compliance/automation/report")
async def get_compliance_automation_report():
    """Get comprehensive compliance automation report."""
    try:
        report = compliance_automation.generate_compliance_automation_report()
        return report
    except Exception as e:
        logger.error(f"Failed to get compliance automation report: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/compliance/automation/check/{rule_id}")
async def perform_compliance_check(rule_id: str):
    """Perform compliance check for a specific rule."""
    try:
        result = await compliance_automation.perform_compliance_check(rule_id)
        return {
            "rule_id": result.rule_id,
            "check_timestamp": result.check_timestamp.isoformat(),
            "status": result.status.value,
            "violations_found": result.violations_found,
            "remediation_applied": result.remediation_applied,
            "details": result.details,
            "next_check_time": result.next_check_time.isoformat() if result.next_check_time else None
        }
    except Exception as e:
        logger.error(f"Failed to perform compliance check: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/compliance/automation/rules")
async def get_compliance_rules():
    """Get all compliance rules and their status."""
    try:
        rules = {}
        for rule_id, rule in compliance_automation.compliance_rules.items():
            # Get latest check result
            latest_result = None
            if rule_id in compliance_automation.check_results and compliance_automation.check_results[rule_id]:
                latest_result = compliance_automation.check_results[rule_id][-1]

            rules[rule_id] = {
                "name": rule.name,
                "description": rule.description,
                "rule_type": rule.rule_type.value,
                "standard": rule.standard.value,
                "severity": rule.severity.value,
                "enabled": rule.enabled,
                "auto_remediation": rule.auto_remediation,
                "check_interval_hours": rule.check_interval_hours,
                "last_check": latest_result.check_timestamp.isoformat() if latest_result else None,
                "last_status": latest_result.status.value if latest_result else "never_checked",
                "violations_found": latest_result.violations_found if latest_result else 0
            }

        return {"compliance_rules": rules}
    except Exception as e:
        logger.error(f"Failed to get compliance rules: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/compliance/automation/alerts")
async def get_compliance_alerts():
    """Get active compliance alerts."""
    try:
        active_alerts = []
        for alert in compliance_automation.compliance_alerts.values():
            if not alert.resolved:
                active_alerts.append({
                    "alert_id": alert.alert_id,
                    "rule_id": alert.rule_id,
                    "alert_type": alert.alert_type,
                    "severity": alert.severity.value,
                    "message": alert.message,
                    "timestamp": alert.timestamp.isoformat(),
                    "affected_resources": alert.affected_resources,
                    "remediation_required": alert.remediation_required
                })

        return {"active_alerts": active_alerts}
    except Exception as e:
        logger.error(f"Failed to get compliance alerts: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/compliance/automation/stats")
async def get_compliance_automation_stats():
    """Get compliance automation statistics."""
    try:
        return {
            "total_rules": len(compliance_automation.compliance_rules),
            "enabled_rules": len([r for r in compliance_automation.compliance_rules.values() if r.enabled]),
            "total_checks_performed": compliance_automation.stats["total_checks_performed"],
            "compliance_violations_detected": compliance_automation.stats["compliance_violations_detected"],
            "automated_remediations_applied": compliance_automation.stats["automated_remediations_applied"],
            "compliance_alerts_triggered": compliance_automation.stats["compliance_alerts_triggered"],
            "last_monitoring_cycle": compliance_automation.stats["last_monitoring_cycle"].isoformat() if compliance_automation.stats["last_monitoring_cycle"] else None,
            "monitoring_enabled": compliance_automation.monitoring_enabled
        }
    except Exception as e:
        logger.error(f"Failed to get compliance automation stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/compliance/automation/rules/{rule_id}/enable")
async def enable_compliance_rule(rule_id: str):
    """Enable a compliance rule."""
    try:
        if rule_id not in compliance_automation.compliance_rules:
            raise HTTPException(status_code=404, detail=f"Compliance rule {rule_id} not found")

        compliance_automation.compliance_rules[rule_id].enabled = True
        return {"success": True, "message": f"Compliance rule {rule_id} enabled"}
    except Exception as e:
        logger.error(f"Failed to enable compliance rule: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/compliance/automation/rules/{rule_id}/disable")
async def disable_compliance_rule(rule_id: str):
    """Disable a compliance rule."""
    try:
        if rule_id not in compliance_automation.compliance_rules:
            raise HTTPException(status_code=404, detail=f"Compliance rule {rule_id} not found")

        compliance_automation.compliance_rules[rule_id].enabled = False
        return {"success": True, "message": f"Compliance rule {rule_id} disabled"}
    except Exception as e:
        logger.error(f"Failed to disable compliance rule: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== TOKEN ANALYSIS ENDPOINTS ====================


@app.post("/analyze/token", response_model=TokenAnalysisResponse)
async def analyze_token(
    request: TokenAnalysisRequest,
    _background_tasks: BackgroundTasks,
    coordinator: AgentCoordinator = Depends(get_coordinator),
):
    """Analyze a single token."""
    try:
        # For now, use the existing coordinator method
        # This would need to be adapted to work with our new coordinator
        result = await coordinator.run_analysis_pipeline(
            address=request.token_address,
            chain="ethereum",  # Would map chain_id to chain name
            include_technical=True,
            include_sentiment=True,
        )

        # Convert result to response model
        return TokenAnalysisResponse(
            token_address=request.token_address,
            chain_id=request.chain_id,
            analysis_id=result.get("pipeline_id", ""),
            timestamp=datetime.now(timezone.utc).isoformat(),
            overall_score=result.get("alpha_score"),
            risk_score=result.get("risk_score"),
            investment_recommendation="HOLD",  # Would be derived from scores
            confidence_level=0.8,  # Would be calculated
            execution_time_ms=int(result.get("duration_seconds", 0) * 1000),
            discovery_data={},
            validation_results={},
            chain_info=result.get("token", {}),
            market_data={},
            technical_analysis=result.get("technical_indicators", {}),
            sentiment_analysis=result.get("sentiment_data", {}),
        )

    except Exception as e:
        logger.error(f"Token analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/discover/tokens")
async def discover_tokens(
    request: DiscoveryRequest, coordinator: AgentCoordinator = Depends(get_coordinator)
):
    """Discover new tokens."""
    try:
        result = await coordinator.run_discovery_pipeline(
            sources=request.sources,
            min_age_hours=request.min_age_hours,
            limit=request.limit,
        )

        return result

    except Exception as e:
        logger.error(f"Token discovery failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== ERROR HANDLERS ====================


@app.exception_handler(Exception)
async def global_exception_handler(_request, exc):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": str(exc),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        },
    )


if __name__ == "__main__":
    import uvicorn

    # Run the API server
    uvicorn.run(
        "src.api.main:app", host="0.0.0.0", port=8000, reload=True, log_level="info"
    )
