"""
REST API layer for the Token Analyzer system.
Provides HTTP endpoints for external access to token analysis functionality.
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any, Dict, List, Optional

import structlog
from fastapi import BackgroundTasks, Depends, FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

from ..agents.coordinator import AgentCoordinator
from ..core.cache import CacheManager
from ..core.config import get_settings
from ..core.database import DatabaseManager
from ..integrations.metrics import MetricsCollector

logger = structlog.get_logger(__name__)


# ==================== PYDANTIC MODELS ====================


class TokenAnalysisRequest(BaseModel):
    """Request model for token analysis."""

    token_address: str = Field(..., description="Token contract address")
    chain_id: int = Field(default=1, description="Blockchain network ID")
    analysis_types: list[str] | None = Field(
        default=None, description="Specific analysis types to run"
    )
    priority: str = Field(default="medium", description="Analysis priority")


class BatchAnalysisRequest(BaseModel):
    """Request model for batch token analysis."""

    tokens: list[dict[str, Any]] = Field(..., description="List of tokens to analyze")
    analysis_types: list[str] | None = Field(
        default=None, description="Specific analysis types to run"
    )
    priority: str = Field(default="medium", description="Analysis priority")


class DiscoveryRequest(BaseModel):
    """Request model for token discovery."""

    sources: list[str] = Field(
        default=["defillama", "dexscreener"], description="Discovery sources"
    )
    limit: int = Field(default=50, description="Maximum number of tokens to discover")
    min_age_hours: int = Field(default=24, description="Minimum token age in hours")


class TokenAnalysisResponse(BaseModel):
    """Response model for token analysis."""

    token_address: str
    chain_id: int
    analysis_id: str
    timestamp: str
    overall_score: float | None = None
    risk_score: float | None = None
    investment_recommendation: str | None = None
    confidence_level: float | None = None
    execution_time_ms: int | None = None
    discovery_data: dict[str, Any] | None = None
    validation_results: dict[str, Any] | None = None
    chain_info: dict[str, Any] | None = None
    market_data: dict[str, Any] | None = None
    technical_analysis: dict[str, Any] | None = None
    sentiment_analysis: dict[str, Any] | None = None


class HealthResponse(BaseModel):
    """Response model for health check."""

    status: str
    timestamp: str
    version: str
    components: dict[str, Any]


# ==================== DEPENDENCY INJECTION ====================


class AppContext:
    """Application context for dependency injection."""

    def __init__(self):
        self.db_manager: DatabaseManager | None = None
        self.cache_manager: CacheManager | None = None
        self.coordinator: AgentCoordinator | None = None
        self.metrics_collector: MetricsCollector | None = None
        self.settings = get_settings()


app_context = AppContext()


async def get_coordinator() -> AgentCoordinator:
    """Get agent coordinator dependency."""
    if not app_context.coordinator:
        raise HTTPException(status_code=503, detail="Agent coordinator not available")
    return app_context.coordinator


async def get_metrics_collector() -> MetricsCollector:
    """Get metrics collector dependency."""
    if not app_context.metrics_collector:
        raise HTTPException(status_code=503, detail="Metrics collector not available")
    return app_context.metrics_collector


# ==================== LIFESPAN MANAGEMENT ====================


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifecycle."""
    logger.info("Starting Token Analyzer API")

    try:
        # Initialize core components
        app_context.db_manager = DatabaseManager()
        app_context.cache_manager = CacheManager()
        app_context.metrics_collector = MetricsCollector()

        await app_context.db_manager.initialize()
        await app_context.cache_manager.initialize()
        await app_context.metrics_collector.start()

        # Initialize coordinator
        app_context.coordinator = AgentCoordinator(
            db_manager=app_context.db_manager,
            cache_manager=app_context.cache_manager,
            metrics_collector=app_context.metrics_collector,
        )
        await app_context.coordinator.initialize()

        logger.info("Token Analyzer API started successfully")
        yield

    finally:
        # Cleanup
        logger.info("Shutting down Token Analyzer API")

        if app_context.coordinator:
            await app_context.coordinator.shutdown()
        if app_context.metrics_collector:
            await app_context.metrics_collector.stop()
        if app_context.cache_manager:
            await app_context.cache_manager.shutdown()
        if app_context.db_manager:
            await app_context.db_manager.close()


# ==================== FASTAPI APP ====================

app = FastAPI(
    title="Token Analyzer API",
    description="Advanced cryptocurrency token analysis and discovery API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# ==================== HEALTH & STATUS ENDPOINTS ====================


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    try:
        components = {}

        # Check coordinator health
        if app_context.coordinator:
            coordinator_health = await app_context.coordinator.health_check()
            components["coordinator"] = {
                "status": "healthy" if coordinator_health else "unhealthy"
            }

        # Check agent status
        if app_context.coordinator:
            agent_status = await app_context.coordinator.get_agent_status()
            components["agents"] = agent_status

        # Check database
        if app_context.db_manager:
            try:
                await app_context.db_manager.execute_query("SELECT 1")
                components["database"] = {"status": "healthy"}
            except Exception as e:
                components["database"] = {"status": "unhealthy", "error": str(e)}

        # Check cache
        if app_context.cache_manager:
            try:
                await app_context.cache_manager.set("health_check", "ok", ttl=60)
                components["cache"] = {"status": "healthy"}
            except Exception as e:
                components["cache"] = {"status": "unhealthy", "error": str(e)}

        # Determine overall status
        overall_status = "healthy"
        for component in components.values():
            if isinstance(component, dict) and component.get("status") != "healthy":
                if "agents" in str(component):
                    # Check if any agents are unhealthy
                    for agent_status in component.values():
                        if isinstance(agent_status, dict) and not agent_status.get(
                            "healthy", True
                        ):
                            overall_status = "degraded"
                            break
                else:
                    overall_status = "unhealthy"
                    break

        return HealthResponse(
            status=overall_status,
            timestamp=datetime.utcnow().isoformat(),
            version="1.0.0",
            components=components,
        )

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthResponse(
            status="error",
            timestamp=datetime.utcnow().isoformat(),
            version="1.0.0",
            components={"error": str(e)},
        )


@app.get("/metrics")
async def get_metrics(
    metrics_collector: MetricsCollector = Depends(get_metrics_collector),
):
    """Get system metrics."""
    try:
        return {
            "agent_metrics": metrics_collector.get_agent_metrics(),
            "counters": metrics_collector.get_counters(),
            "gauges": metrics_collector.get_gauges(),
            "system_health": metrics_collector.get_system_health(),
        }
    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== TOKEN ANALYSIS ENDPOINTS ====================


@app.post("/analyze/token", response_model=TokenAnalysisResponse)
async def analyze_token(
    request: TokenAnalysisRequest,
    background_tasks: BackgroundTasks,
    coordinator: AgentCoordinator = Depends(get_coordinator),
):
    """Analyze a single token."""
    try:
        # Convert priority string to enum
        from ..agents.coordinator import AnalysisPriority

        priority_map = {
            "low": AnalysisPriority.LOW,
            "medium": AnalysisPriority.MEDIUM,
            "high": AnalysisPriority.HIGH,
            "critical": AnalysisPriority.CRITICAL,
        }
        priority = priority_map.get(request.priority.lower(), AnalysisPriority.MEDIUM)

        # Convert analysis types to set
        analysis_types = set(request.analysis_types) if request.analysis_types else None

        # Perform analysis
        result = await coordinator.analyze_token(
            token_address=request.token_address,
            chain_id=request.chain_id,
            analysis_types=analysis_types,
            priority=priority,
        )

        # Convert result to response model
        return TokenAnalysisResponse(
            token_address=result.token_address,
            chain_id=result.chain_id,
            analysis_id=result.analysis_id,
            timestamp=result.timestamp.isoformat(),
            overall_score=result.overall_score,
            risk_score=result.risk_score,
            investment_recommendation=result.investment_recommendation,
            confidence_level=result.confidence_level,
            execution_time_ms=result.execution_time_ms,
            discovery_data=result.discovery_data,
            validation_results=result.validation_results,
            chain_info=result.chain_info,
            market_data=result.market_data,
            technical_analysis=result.technical_analysis,
            sentiment_analysis=result.sentiment_analysis,
        )

    except Exception as e:
        logger.error(f"Token analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/analyze/batch")
async def analyze_tokens_batch(
    request: BatchAnalysisRequest,
    background_tasks: BackgroundTasks,
    coordinator: AgentCoordinator = Depends(get_coordinator),
):
    """Analyze multiple tokens in batch."""
    try:
        # Convert priority string to enum
        from ..agents.coordinator import AnalysisPriority

        priority_map = {
            "low": AnalysisPriority.LOW,
            "medium": AnalysisPriority.MEDIUM,
            "high": AnalysisPriority.HIGH,
            "critical": AnalysisPriority.CRITICAL,
        }
        priority = priority_map.get(request.priority.lower(), AnalysisPriority.MEDIUM)

        # Convert analysis types to set
        analysis_types = set(request.analysis_types) if request.analysis_types else None

        # Extract token list
        token_list = []
        for token in request.tokens:
            token_address = token.get("address") or token.get("token_address")
            chain_id = token.get("chain_id", 1)
            if token_address:
                token_list.append((token_address, chain_id))

        if not token_list:
            raise HTTPException(status_code=400, detail="No valid tokens provided")

        # Perform batch analysis
        results = await coordinator.batch_analyze_tokens(
            token_list=token_list, analysis_types=analysis_types, priority=priority
        )

        # Convert results to response format
        response_results = []
        for result in results:
            response_results.append(
                TokenAnalysisResponse(
                    token_address=result.token_address,
                    chain_id=result.chain_id,
                    analysis_id=result.analysis_id,
                    timestamp=result.timestamp.isoformat(),
                    overall_score=result.overall_score,
                    risk_score=result.risk_score,
                    investment_recommendation=result.investment_recommendation,
                    confidence_level=result.confidence_level,
                    execution_time_ms=result.execution_time_ms,
                    discovery_data=result.discovery_data,
                    validation_results=result.validation_results,
                    chain_info=result.chain_info,
                    market_data=result.market_data,
                    technical_analysis=result.technical_analysis,
                    sentiment_analysis=result.sentiment_analysis,
                )
            )

        return {
            "results": response_results,
            "total_requested": len(token_list),
            "total_completed": len(response_results),
        }

    except Exception as e:
        logger.error(f"Batch analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== DISCOVERY ENDPOINTS ====================


@app.post("/discover/tokens")
async def discover_tokens(
    request: DiscoveryRequest, coordinator: AgentCoordinator = Depends(get_coordinator)
):
    """Discover new tokens."""
    try:
        result = await coordinator.run_discovery_pipeline(
            sources=request.sources,
            min_age_hours=request.min_age_hours,
            limit=request.limit,
        )

        return result

    except Exception as e:
        logger.error(f"Token discovery failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/discover/trending")
async def discover_trending_tokens(
    limit: int = Query(default=50, description="Maximum number of tokens"),
    coordinator: AgentCoordinator = Depends(get_coordinator),
):
    """Discover and analyze trending tokens."""
    try:
        results = await coordinator.discover_and_analyze_trending_tokens(limit=limit)

        # Convert results to response format
        response_results = []
        for result in results:
            response_results.append(
                TokenAnalysisResponse(
                    token_address=result.token_address,
                    chain_id=result.chain_id,
                    analysis_id=result.analysis_id,
                    timestamp=result.timestamp.isoformat(),
                    overall_score=result.overall_score,
                    risk_score=result.risk_score,
                    investment_recommendation=result.investment_recommendation,
                    confidence_level=result.confidence_level,
                    execution_time_ms=result.execution_time_ms,
                    discovery_data=result.discovery_data,
                    validation_results=result.validation_results,
                    chain_info=result.chain_info,
                    market_data=result.market_data,
                    technical_analysis=result.technical_analysis,
                    sentiment_analysis=result.sentiment_analysis,
                )
            )

        return {"trending_tokens": response_results, "count": len(response_results)}

    except Exception as e:
        logger.error(f"Trending token discovery failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== HISTORY & REPORTING ENDPOINTS ====================


@app.get("/token/{token_address}/history")
async def get_token_analysis_history(
    token_address: str,
    chain_id: int = Query(default=1, description="Blockchain network ID"),
    limit: int = Query(default=10, description="Maximum number of results"),
    coordinator: AgentCoordinator = Depends(get_coordinator),
):
    """Get analysis history for a token."""
    try:
        history = await coordinator.get_token_analysis_history(
            token_address=token_address, chain_id=chain_id, limit=limit
        )

        return {
            "token_address": token_address,
            "chain_id": chain_id,
            "history": history,
        }

    except Exception as e:
        logger.error(f"Failed to get token history: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/analytics/summary")
async def get_analytics_summary(
    coordinator: AgentCoordinator = Depends(get_coordinator),
):
    """Get analytics summary and metrics."""
    try:
        metrics = await coordinator.get_analysis_metrics()
        return metrics

    except Exception as e:
        logger.error(f"Failed to get analytics summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== ERROR HANDLERS ====================


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": str(exc),
            "timestamp": datetime.utcnow().isoformat(),
        },
    )


if __name__ == "__main__":
    import uvicorn

    # Run the API server
    uvicorn.run(
        "src.api.main:app", host="0.0.0.0", port=8000, reload=True, log_level="info"
    )
