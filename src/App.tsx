import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Layout } from '@/components/Layout';
import { Discovery } from '@/pages/Discovery';
import { Analytics } from '@/pages/Analytics';
import { MemeTokens } from '@/pages/MemeTokens';
import { BlueChip } from '@/pages/BlueChip';
import { Alerts } from '@/pages/Alerts';
import { Settings } from '@/pages/Settings';
import './App.css';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 30000, // 30 seconds
      refetchInterval: 30000, // Auto-refetch every 30 seconds
      retry: 3,
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<Discovery />} />
            <Route path="/analytics" element={<Analytics />} />
            <Route path="/meme" element={<MemeTokens />} />
            <Route path="/bluechip" element={<BlueChip />} />
            <Route path="/alerts" element={<Alerts />} />
            <Route path="/settings" element={<Settings />} />
          </Routes>
        </Layout>
      </Router>
    </QueryClientProvider>
  );
}

export default App;