#!/bin/bash

# CipherScope Environment Setup Script
# This script helps configure environment variables and deployment settings

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Default values
ENVIRONMENT="production"
DOMAIN=""
EMAIL=""
INTERACTIVE=true

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Configure CipherScope environment for deployment

OPTIONS:
    -e, --environment ENV    Target environment (staging|production) [default: production]
    -d, --domain DOMAIN     Your domain name (e.g., cipherscope.com)
    -m, --email EMAIL       Your email for SSL certificates
    -n, --non-interactive   Run in non-interactive mode
    -h, --help              Show this help message

EXAMPLES:
    $0 -e production -d cipherscope.com -m <EMAIL>
    $0 -e staging -d staging.cipherscope.com -m <EMAIL>

EOF
}

generate_secret() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-32
}

generate_jwt_secret() {
    openssl rand -base64 64 | tr -d "=+/" | cut -c1-64
}

prompt_input() {
    local prompt="$1"
    local default="$2"
    local secret="$3"
    
    if [[ "$INTERACTIVE" == false ]]; then
        echo "$default"
        return
    fi
    
    if [[ "$secret" == true ]]; then
        read -s -p "$prompt: " input
        echo
    else
        read -p "$prompt${default:+ [$default]}: " input
    fi
    
    echo "${input:-$default}"
}

setup_environment_file() {
    log_info "Setting up environment file for $ENVIRONMENT"
    
    local env_file="$PROJECT_ROOT/.env.$ENVIRONMENT"
    local template_file="$PROJECT_ROOT/.env.$ENVIRONMENT"
    
    if [[ ! -f "$template_file" ]]; then
        log_error "Template file not found: $template_file"
        exit 1
    fi
    
    # Copy template
    cp "$template_file" "$env_file.new"
    
    # Get user inputs
    if [[ -z "$DOMAIN" ]]; then
        DOMAIN=$(prompt_input "Enter your domain name (e.g., cipherscope.com)" "")
    fi
    
    if [[ -z "$EMAIL" ]]; then
        EMAIL=$(prompt_input "Enter your email for SSL certificates" "")
    fi
    
    # Generate secrets
    log_info "Generating secure secrets..."
    NEXTAUTH_SECRET=$(generate_secret)
    JWT_SECRET=$(generate_jwt_secret)
    SESSION_SECRET=$(generate_secret)
    
    # Database password
    DB_PASSWORD=$(prompt_input "Enter database password" "" true)
    if [[ -z "$DB_PASSWORD" ]]; then
        DB_PASSWORD=$(generate_secret)
        log_info "Generated database password: $DB_PASSWORD"
    fi
    
    # API keys
    log_info "Please provide your API keys (press Enter to skip):"
    COINGECKO_API_KEY=$(prompt_input "CoinGecko API Key" "")
    BINANCE_API_KEY=$(prompt_input "Binance API Key" "")
    INFURA_PROJECT_ID=$(prompt_input "Infura Project ID" "")
    
    # Update environment file
    log_info "Updating environment variables..."
    
    # Domain configuration
    if [[ "$ENVIRONMENT" == "production" ]]; then
        sed -i.bak "s|NEXT_PUBLIC_APP_URL=.*|NEXT_PUBLIC_APP_URL=https://$DOMAIN|g" "$env_file.new"
        sed -i.bak "s|NEXT_PUBLIC_API_URL=.*|NEXT_PUBLIC_API_URL=https://api.$DOMAIN|g" "$env_file.new"
        sed -i.bak "s|NEXT_PUBLIC_WS_URL=.*|NEXT_PUBLIC_WS_URL=wss://api.$DOMAIN/ws|g" "$env_file.new"
        sed -i.bak "s|NEXTAUTH_URL=.*|NEXTAUTH_URL=https://$DOMAIN|g" "$env_file.new"
    else
        sed -i.bak "s|NEXT_PUBLIC_APP_URL=.*|NEXT_PUBLIC_APP_URL=https://$DOMAIN|g" "$env_file.new"
        sed -i.bak "s|NEXT_PUBLIC_API_URL=.*|NEXT_PUBLIC_API_URL=https://api.$DOMAIN|g" "$env_file.new"
        sed -i.bak "s|NEXT_PUBLIC_WS_URL=.*|NEXT_PUBLIC_WS_URL=wss://api.$DOMAIN/ws|g" "$env_file.new"
        sed -i.bak "s|NEXTAUTH_URL=.*|NEXTAUTH_URL=https://$DOMAIN|g" "$env_file.new"
    fi
    
    # Secrets
    sed -i.bak "s|NEXTAUTH_SECRET=.*|NEXTAUTH_SECRET=$NEXTAUTH_SECRET|g" "$env_file.new"
    sed -i.bak "s|JWT_SECRET=.*|JWT_SECRET=$JWT_SECRET|g" "$env_file.new"
    sed -i.bak "s|SESSION_SECRET=.*|SESSION_SECRET=$SESSION_SECRET|g" "$env_file.new"
    
    # API keys
    if [[ -n "$COINGECKO_API_KEY" ]]; then
        sed -i.bak "s|NEXT_PUBLIC_COINGECKO_API_KEY=.*|NEXT_PUBLIC_COINGECKO_API_KEY=$COINGECKO_API_KEY|g" "$env_file.new"
    fi
    
    if [[ -n "$BINANCE_API_KEY" ]]; then
        sed -i.bak "s|NEXT_PUBLIC_BINANCE_API_KEY=.*|NEXT_PUBLIC_BINANCE_API_KEY=$BINANCE_API_KEY|g" "$env_file.new"
    fi
    
    if [[ -n "$INFURA_PROJECT_ID" ]]; then
        sed -i.bak "s|YOUR_INFURA_PROJECT_ID|$INFURA_PROJECT_ID|g" "$env_file.new"
    fi
    
    # Move new file to final location
    mv "$env_file.new" "$env_file"
    rm -f "$env_file.new.bak"
    
    log_success "Environment file created: $env_file"
    
    # Export for Terraform
    export TF_VAR_db_password="$DB_PASSWORD"
    export TF_VAR_domain_name="$DOMAIN"
    
    log_info "Exported Terraform variables:"
    log_info "  TF_VAR_db_password=***"
    log_info "  TF_VAR_domain_name=$DOMAIN"
}

setup_ssl_certificate() {
    log_info "Setting up SSL certificate for $DOMAIN"
    
    if command -v aws &> /dev/null; then
        log_info "Creating ACM certificate..."
        
        # Request certificate
        CERT_ARN=$(aws acm request-certificate \
            --domain-name "$DOMAIN" \
            --subject-alternative-names "www.$DOMAIN" "api.$DOMAIN" "*.${DOMAIN}" \
            --validation-method DNS \
            --query 'CertificateArn' \
            --output text)
        
        if [[ -n "$CERT_ARN" ]]; then
            log_success "Certificate requested: $CERT_ARN"
            export TF_VAR_certificate_arn="$CERT_ARN"
            
            log_warning "Please validate the certificate in AWS Console:"
            log_warning "1. Go to AWS Certificate Manager"
            log_warning "2. Find your certificate"
            log_warning "3. Add the DNS validation records to your domain"
            log_warning "4. Wait for validation to complete"
        else
            log_error "Failed to request certificate"
        fi
    else
        log_warning "AWS CLI not found. Please manually create SSL certificate:"
        log_warning "1. Go to AWS Certificate Manager"
        log_warning "2. Request a certificate for $DOMAIN"
        log_warning "3. Include SANs: www.$DOMAIN, api.$DOMAIN, *.$DOMAIN"
        log_warning "4. Use DNS validation"
        log_warning "5. Set TF_VAR_certificate_arn environment variable"
    fi
}

setup_kubernetes_secrets() {
    log_info "Setting up Kubernetes secrets"
    
    local env_file="$PROJECT_ROOT/.env.$ENVIRONMENT"
    local namespace="cipherscope"
    
    if [[ "$ENVIRONMENT" != "production" ]]; then
        namespace="cipherscope-$ENVIRONMENT"
    fi
    
    # Check if kubectl is available
    if ! command -v kubectl &> /dev/null; then
        log_warning "kubectl not found. Please install kubectl and configure cluster access"
        return
    fi
    
    # Create namespace if it doesn't exist
    kubectl create namespace "$namespace" --dry-run=client -o yaml | kubectl apply -f -
    
    # Create secret from environment file
    kubectl create secret generic cipherscope-frontend-secrets \
        --from-env-file="$env_file" \
        --namespace="$namespace" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    log_success "Kubernetes secrets created in namespace: $namespace"
}

setup_monitoring() {
    log_info "Setting up monitoring configuration"
    
    local monitoring_dir="$PROJECT_ROOT/monitoring"
    
    # Update Prometheus configuration
    if [[ -f "$monitoring_dir/prometheus.yml" ]]; then
        # Update with actual domain
        sed -i.bak "s|cipherscope.com|$DOMAIN|g" "$monitoring_dir/prometheus.yml"
        sed -i.bak "s|api.cipherscope.com|api.$DOMAIN|g" "$monitoring_dir/prometheus.yml"
        rm -f "$monitoring_dir/prometheus.yml.bak"
        
        log_success "Updated Prometheus configuration"
    fi
    
    # Create Grafana datasource configuration
    cat > "$monitoring_dir/grafana/datasources/prometheus.yml" << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
EOF
    
    log_success "Created Grafana datasource configuration"
}

validate_configuration() {
    log_info "Validating configuration..."
    
    local env_file="$PROJECT_ROOT/.env.$ENVIRONMENT"
    
    # Check required variables
    local required_vars=(
        "NEXT_PUBLIC_APP_URL"
        "NEXT_PUBLIC_API_URL"
        "NEXTAUTH_SECRET"
        "JWT_SECRET"
    )
    
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^${var}=" "$env_file" || grep -q "^${var}=.*CHANGE_ME" "$env_file"; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_error "Missing or invalid configuration for:"
        for var in "${missing_vars[@]}"; do
            log_error "  - $var"
        done
        return 1
    fi
    
    log_success "Configuration validation passed"
    return 0
}

show_next_steps() {
    log_success "Environment setup completed!"
    echo
    log_info "Next steps:"
    echo "1. Review and customize your environment file: .env.$ENVIRONMENT"
    echo "2. Validate SSL certificate in AWS Console"
    echo "3. Deploy infrastructure with Terraform:"
    echo "   cd terraform"
    echo "   terraform init"
    echo "   terraform plan -var-file=\"environments/$ENVIRONMENT.tfvars\""
    echo "   terraform apply -var-file=\"environments/$ENVIRONMENT.tfvars\""
    echo "4. Deploy application:"
    echo "   ./scripts/deploy.sh -e $ENVIRONMENT"
    echo "5. Set up monitoring:"
    echo "   kubectl apply -f monitoring/"
    echo
    log_warning "Important: Keep your .env.$ENVIRONMENT file secure and never commit it to version control!"
}

main() {
    log_info "Starting CipherScope environment setup"
    log_info "Environment: $ENVIRONMENT"
    log_info "Domain: ${DOMAIN:-'Not specified'}"
    log_info "Interactive: $INTERACTIVE"
    
    setup_environment_file
    
    if [[ -n "$DOMAIN" ]]; then
        setup_ssl_certificate
        setup_monitoring
    fi
    
    if validate_configuration; then
        setup_kubernetes_secrets
        show_next_steps
    else
        log_error "Configuration validation failed. Please fix the issues and run again."
        exit 1
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -d|--domain)
            DOMAIN="$2"
            shift 2
            ;;
        -m|--email)
            EMAIL="$2"
            shift 2
            ;;
        -n|--non-interactive)
            INTERACTIVE=false
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate environment
if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    log_error "Invalid environment: $ENVIRONMENT. Must be 'staging' or 'production'"
    exit 1
fi

# Run main function
main
