#!/bin/bash

# CipherScope SSL Certificate Setup Script
# This script helps set up SSL certificates for your domain

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOMAIN=""
EMAIL=""
METHOD="dns"
PROVIDER="aws"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Set up SSL certificates for CipherScope

OPTIONS:
    -d, --domain DOMAIN     Your domain name (e.g., cipherscope.com)
    -e, --email EMAIL       Your email for certificate notifications
    -m, --method METHOD     Validation method (dns|http) [default: dns]
    -p, --provider PROVIDER Certificate provider (aws|letsencrypt|cloudflare) [default: aws]
    -h, --help              Show this help message

EXAMPLES:
    $0 -d cipherscope.com -e <EMAIL> -p aws
    $0 -d staging.cipherscope.com -e <EMAIL> -p letsencrypt

EOF
}

setup_aws_acm() {
    log_info "Setting up AWS ACM certificate for $DOMAIN"
    
    # Check if AWS CLI is configured
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS CLI not configured. Please run 'aws configure' first"
        exit 1
    fi
    
    # Request certificate
    log_info "Requesting certificate..."
    CERT_ARN=$(aws acm request-certificate \
        --domain-name "$DOMAIN" \
        --subject-alternative-names "www.$DOMAIN" "api.$DOMAIN" "*.${DOMAIN}" \
        --validation-method DNS \
        --query 'CertificateArn' \
        --output text)
    
    if [[ -n "$CERT_ARN" ]]; then
        log_success "Certificate requested successfully!"
        log_info "Certificate ARN: $CERT_ARN"
        
        # Get validation records
        log_info "Getting DNS validation records..."
        sleep 5  # Wait for AWS to process the request
        
        aws acm describe-certificate \
            --certificate-arn "$CERT_ARN" \
            --query 'Certificate.DomainValidationOptions[*].[DomainName,ResourceRecord.Name,ResourceRecord.Value]' \
            --output table
        
        log_warning "Please add the DNS validation records to your domain:"
        log_warning "1. Log into your DNS provider"
        log_warning "2. Add the CNAME records shown above"
        log_warning "3. Wait for DNS propagation (5-30 minutes)"
        log_warning "4. Certificate will be automatically validated"
        
        # Save certificate ARN for Terraform
        echo "export TF_VAR_certificate_arn=\"$CERT_ARN\"" > .env.ssl
        log_success "Certificate ARN saved to .env.ssl"
        
        # Wait for validation (optional)
        read -p "Wait for certificate validation? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "Waiting for certificate validation..."
            aws acm wait certificate-validated --certificate-arn "$CERT_ARN"
            log_success "Certificate validated successfully!"
        fi
        
    else
        log_error "Failed to request certificate"
        exit 1
    fi
}

setup_letsencrypt() {
    log_info "Setting up Let's Encrypt certificate for $DOMAIN"
    
    # Check if certbot is installed
    if ! command -v certbot &> /dev/null; then
        log_info "Installing certbot..."
        if command -v apt-get &> /dev/null; then
            sudo apt-get update
            sudo apt-get install -y certbot
        elif command -v yum &> /dev/null; then
            sudo yum install -y certbot
        elif command -v brew &> /dev/null; then
            brew install certbot
        else
            log_error "Please install certbot manually"
            exit 1
        fi
    fi
    
    if [[ "$METHOD" == "dns" ]]; then
        log_info "Using DNS validation..."
        certbot certonly \
            --manual \
            --preferred-challenges dns \
            --email "$EMAIL" \
            --agree-tos \
            --no-eff-email \
            -d "$DOMAIN" \
            -d "www.$DOMAIN" \
            -d "api.$DOMAIN" \
            -d "*.$DOMAIN"
    else
        log_info "Using HTTP validation..."
        certbot certonly \
            --standalone \
            --email "$EMAIL" \
            --agree-tos \
            --no-eff-email \
            -d "$DOMAIN" \
            -d "www.$DOMAIN" \
            -d "api.$DOMAIN"
    fi
    
    # Copy certificates to project directory
    CERT_DIR="/etc/letsencrypt/live/$DOMAIN"
    PROJECT_SSL_DIR="./ssl"
    
    if [[ -d "$CERT_DIR" ]]; then
        mkdir -p "$PROJECT_SSL_DIR"
        sudo cp "$CERT_DIR/fullchain.pem" "$PROJECT_SSL_DIR/cert.pem"
        sudo cp "$CERT_DIR/privkey.pem" "$PROJECT_SSL_DIR/key.pem"
        sudo chown $(whoami):$(whoami) "$PROJECT_SSL_DIR"/*
        
        log_success "Certificates copied to $PROJECT_SSL_DIR"
        
        # Create renewal script
        cat > "./scripts/renew-ssl.sh" << EOF
#!/bin/bash
# SSL Certificate Renewal Script

certbot renew --quiet
if [[ \$? -eq 0 ]]; then
    # Copy renewed certificates
    sudo cp /etc/letsencrypt/live/$DOMAIN/fullchain.pem ./ssl/cert.pem
    sudo cp /etc/letsencrypt/live/$DOMAIN/privkey.pem ./ssl/key.pem
    sudo chown \$(whoami):\$(whoami) ./ssl/*
    
    # Restart services
    docker-compose restart nginx
    kubectl rollout restart deployment/cipherscope-frontend -n cipherscope
fi
EOF
        chmod +x "./scripts/renew-ssl.sh"
        
        log_success "Renewal script created: ./scripts/renew-ssl.sh"
        log_info "Add to crontab for automatic renewal:"
        log_info "0 2 * * * /path/to/cipherscope/scripts/renew-ssl.sh"
    else
        log_error "Certificate directory not found: $CERT_DIR"
        exit 1
    fi
}

setup_cloudflare() {
    log_info "Setting up Cloudflare certificate for $DOMAIN"
    
    # Check if Cloudflare CLI is installed
    if ! command -v cloudflared &> /dev/null; then
        log_warning "Cloudflare CLI not found. Please install cloudflared"
        log_info "Visit: https://developers.cloudflare.com/cloudflare-one/connections/connect-apps/install-and-setup/installation/"
        exit 1
    fi
    
    log_info "Please configure Cloudflare SSL/TLS settings:"
    log_info "1. Go to Cloudflare Dashboard > SSL/TLS"
    log_info "2. Set SSL/TLS encryption mode to 'Full (strict)'"
    log_info "3. Enable 'Always Use HTTPS'"
    log_info "4. Enable 'HTTP Strict Transport Security (HSTS)'"
    log_info "5. Create Origin Certificate:"
    log_info "   - Go to SSL/TLS > Origin Server"
    log_info "   - Click 'Create Certificate'"
    log_info "   - Add hostnames: $DOMAIN, *.$DOMAIN"
    log_info "   - Download certificate and private key"
    
    # Create SSL directory
    mkdir -p ./ssl
    
    log_warning "Please save the Origin Certificate as ./ssl/cert.pem"
    log_warning "Please save the Private Key as ./ssl/key.pem"
    
    read -p "Press Enter when you have saved the certificates..."
    
    if [[ -f "./ssl/cert.pem" && -f "./ssl/key.pem" ]]; then
        log_success "Certificates found!"
        
        # Set proper permissions
        chmod 600 ./ssl/key.pem
        chmod 644 ./ssl/cert.pem
        
        log_success "Certificate setup completed"
    else
        log_error "Certificates not found. Please save them as instructed."
        exit 1
    fi
}

create_kubernetes_tls_secret() {
    log_info "Creating Kubernetes TLS secret"
    
    if [[ ! -f "./ssl/cert.pem" || ! -f "./ssl/key.pem" ]]; then
        log_error "Certificate files not found in ./ssl/"
        return 1
    fi
    
    # Check if kubectl is available
    if ! command -v kubectl &> /dev/null; then
        log_warning "kubectl not found. Please create TLS secret manually:"
        log_warning "kubectl create secret tls cipherscope-tls --cert=./ssl/cert.pem --key=./ssl/key.pem -n cipherscope"
        return 0
    fi
    
    # Create TLS secret
    kubectl create secret tls cipherscope-tls \
        --cert=./ssl/cert.pem \
        --key=./ssl/key.pem \
        --namespace=cipherscope \
        --dry-run=client -o yaml | kubectl apply -f -
    
    log_success "Kubernetes TLS secret created"
}

update_nginx_config() {
    log_info "Updating Nginx configuration for SSL"
    
    local nginx_config="./nginx/nginx.conf"
    
    if [[ -f "$nginx_config" ]]; then
        # Update SSL certificate paths
        sed -i.bak "s|ssl_certificate .*|ssl_certificate /etc/nginx/ssl/cert.pem;|g" "$nginx_config"
        sed -i.bak "s|ssl_certificate_key .*|ssl_certificate_key /etc/nginx/ssl/key.pem;|g" "$nginx_config"
        
        # Update server name
        sed -i.bak "s|server_name .*;|server_name $DOMAIN www.$DOMAIN;|g" "$nginx_config"
        
        rm -f "$nginx_config.bak"
        
        log_success "Nginx configuration updated"
    else
        log_warning "Nginx configuration not found: $nginx_config"
    fi
}

verify_ssl_setup() {
    log_info "Verifying SSL setup..."
    
    # Check certificate files
    if [[ -f "./ssl/cert.pem" && -f "./ssl/key.pem" ]]; then
        log_success "Certificate files found"
        
        # Verify certificate
        if openssl x509 -in ./ssl/cert.pem -text -noout &> /dev/null; then
            log_success "Certificate is valid"
            
            # Show certificate details
            log_info "Certificate details:"
            openssl x509 -in ./ssl/cert.pem -text -noout | grep -E "(Subject:|DNS:|Not After)"
        else
            log_error "Certificate is invalid"
            return 1
        fi
        
        # Verify private key
        if openssl rsa -in ./ssl/key.pem -check &> /dev/null; then
            log_success "Private key is valid"
        else
            log_error "Private key is invalid"
            return 1
        fi
        
        # Check if certificate and key match
        CERT_HASH=$(openssl x509 -noout -modulus -in ./ssl/cert.pem | openssl md5)
        KEY_HASH=$(openssl rsa -noout -modulus -in ./ssl/key.pem | openssl md5)
        
        if [[ "$CERT_HASH" == "$KEY_HASH" ]]; then
            log_success "Certificate and private key match"
        else
            log_error "Certificate and private key do not match"
            return 1
        fi
        
    else
        log_error "Certificate files not found"
        return 1
    fi
    
    log_success "SSL setup verification completed"
}

main() {
    log_info "Starting SSL certificate setup"
    log_info "Domain: $DOMAIN"
    log_info "Email: $EMAIL"
    log_info "Method: $METHOD"
    log_info "Provider: $PROVIDER"
    
    case "$PROVIDER" in
        "aws")
            setup_aws_acm
            ;;
        "letsencrypt")
            setup_letsencrypt
            create_kubernetes_tls_secret
            update_nginx_config
            ;;
        "cloudflare")
            setup_cloudflare
            create_kubernetes_tls_secret
            update_nginx_config
            ;;
        *)
            log_error "Unknown provider: $PROVIDER"
            exit 1
            ;;
    esac
    
    if [[ "$PROVIDER" != "aws" ]]; then
        verify_ssl_setup
    fi
    
    log_success "SSL certificate setup completed!"
    
    if [[ "$PROVIDER" == "aws" ]]; then
        log_info "Next steps:"
        log_info "1. Validate certificate in AWS Console"
        log_info "2. Update Terraform configuration with certificate ARN"
        log_info "3. Deploy infrastructure with Terraform"
    else
        log_info "Next steps:"
        log_info "1. Deploy application with SSL enabled"
        log_info "2. Test HTTPS connectivity"
        log_info "3. Set up certificate renewal (if using Let's Encrypt)"
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--domain)
            DOMAIN="$2"
            shift 2
            ;;
        -e|--email)
            EMAIL="$2"
            shift 2
            ;;
        -m|--method)
            METHOD="$2"
            shift 2
            ;;
        -p|--provider)
            PROVIDER="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$DOMAIN" ]]; then
    log_error "Domain is required. Use -d or --domain"
    show_usage
    exit 1
fi

if [[ -z "$EMAIL" && "$PROVIDER" == "letsencrypt" ]]; then
    log_error "Email is required for Let's Encrypt. Use -e or --email"
    show_usage
    exit 1
fi

# Run main function
main
