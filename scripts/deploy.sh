#!/bin/bash

# CipherScope Frontend Deployment Script
# This script handles deployment to different environments

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DOCKER_REGISTRY="ghcr.io"
IMAGE_NAME="cipherscope/frontend"
NAMESPACE="cipherscope"

# Default values
ENVIRONMENT="staging"
BUILD_ONLY=false
SKIP_TESTS=false
SKIP_BUILD=false
FORCE_DEPLOY=false
DRY_RUN=false

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy CipherScope Frontend to specified environment

OPTIONS:
    -e, --environment ENV    Target environment (dev|staging|production) [default: staging]
    -b, --build-only        Only build the Docker image, don't deploy
    -s, --skip-tests        Skip running tests
    -S, --skip-build        Skip building Docker image
    -f, --force             Force deployment without confirmation
    -d, --dry-run           Show what would be deployed without actually deploying
    -h, --help              Show this help message

EXAMPLES:
    $0 -e staging                    # Deploy to staging
    $0 -e production -f              # Force deploy to production
    $0 -b                           # Build only
    $0 -e staging --dry-run         # Dry run for staging

EOF
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if required tools are installed
    local tools=("docker" "kubectl" "helm")
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "$tool is not installed or not in PATH"
            exit 1
        fi
    done
    
    # Check if Docker is running
    if ! docker info &> /dev/null; then
        log_error "Docker is not running"
        exit 1
    fi
    
    # Check kubectl context
    local current_context=$(kubectl config current-context 2>/dev/null || echo "none")
    log_info "Current kubectl context: $current_context"
    
    log_success "Prerequisites check passed"
}

load_environment() {
    log_info "Loading environment configuration for: $ENVIRONMENT"
    
    local env_file="$PROJECT_ROOT/.env.$ENVIRONMENT"
    if [[ -f "$env_file" ]]; then
        source "$env_file"
        log_success "Loaded environment file: $env_file"
    else
        log_warning "Environment file not found: $env_file"
    fi
    
    # Set environment-specific variables
    case "$ENVIRONMENT" in
        "dev"|"development")
            NAMESPACE="cipherscope-dev"
            ;;
        "staging")
            NAMESPACE="cipherscope-staging"
            ;;
        "production"|"prod")
            NAMESPACE="cipherscope"
            if [[ "$FORCE_DEPLOY" != true ]]; then
                log_warning "Deploying to production requires --force flag"
                exit 1
            fi
            ;;
        *)
            log_error "Unknown environment: $ENVIRONMENT"
            exit 1
            ;;
    esac
}

run_tests() {
    if [[ "$SKIP_TESTS" == true ]]; then
        log_warning "Skipping tests"
        return 0
    fi
    
    log_info "Running tests..."
    cd "$PROJECT_ROOT"
    
    # Install dependencies if needed
    if [[ ! -d "node_modules" ]]; then
        log_info "Installing dependencies..."
        npm ci
    fi
    
    # Run linting
    log_info "Running ESLint..."
    npm run lint
    
    # Run type checking
    log_info "Running TypeScript type check..."
    npm run type-check
    
    # Run unit tests
    log_info "Running unit tests..."
    npm run test:ci
    
    log_success "All tests passed"
}

build_docker_image() {
    if [[ "$SKIP_BUILD" == true ]]; then
        log_warning "Skipping Docker build"
        return 0
    fi
    
    log_info "Building Docker image..."
    cd "$PROJECT_ROOT"
    
    # Generate image tag
    local git_sha=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    local timestamp=$(date +%Y%m%d-%H%M%S)
    local image_tag="${ENVIRONMENT}-${git_sha}-${timestamp}"
    
    # Build image
    log_info "Building image: ${DOCKER_REGISTRY}/${IMAGE_NAME}:${image_tag}"
    docker build \
        --tag "${DOCKER_REGISTRY}/${IMAGE_NAME}:${image_tag}" \
        --tag "${DOCKER_REGISTRY}/${IMAGE_NAME}:${ENVIRONMENT}-latest" \
        --build-arg NODE_ENV=production \
        --build-arg ENVIRONMENT="$ENVIRONMENT" \
        .
    
    # Push to registry
    log_info "Pushing image to registry..."
    docker push "${DOCKER_REGISTRY}/${IMAGE_NAME}:${image_tag}"
    docker push "${DOCKER_REGISTRY}/${IMAGE_NAME}:${ENVIRONMENT}-latest"
    
    # Export for use in deployment
    export DOCKER_IMAGE_TAG="$image_tag"
    
    log_success "Docker image built and pushed: ${image_tag}"
}

deploy_to_kubernetes() {
    if [[ "$BUILD_ONLY" == true ]]; then
        log_info "Build-only mode, skipping deployment"
        return 0
    fi
    
    log_info "Deploying to Kubernetes..."
    
    # Check if namespace exists
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_info "Creating namespace: $NAMESPACE"
        kubectl create namespace "$NAMESPACE"
    fi
    
    # Apply Kubernetes manifests
    local k8s_dir="$PROJECT_ROOT/k8s"
    if [[ -d "$k8s_dir" ]]; then
        log_info "Applying Kubernetes manifests..."
        
        if [[ "$DRY_RUN" == true ]]; then
            kubectl apply -f "$k8s_dir" --namespace="$NAMESPACE" --dry-run=client
        else
            kubectl apply -f "$k8s_dir" --namespace="$NAMESPACE"
        fi
    fi
    
    # Update deployment with new image
    if [[ -n "$DOCKER_IMAGE_TAG" ]]; then
        local image_full="${DOCKER_REGISTRY}/${IMAGE_NAME}:${DOCKER_IMAGE_TAG}"
        log_info "Updating deployment with image: $image_full"
        
        if [[ "$DRY_RUN" == true ]]; then
            log_info "DRY RUN: Would update deployment with image: $image_full"
        else
            kubectl set image deployment/cipherscope-frontend \
                frontend="$image_full" \
                --namespace="$NAMESPACE"
        fi
    fi
    
    # Wait for rollout to complete
    if [[ "$DRY_RUN" != true ]]; then
        log_info "Waiting for deployment rollout..."
        kubectl rollout status deployment/cipherscope-frontend \
            --namespace="$NAMESPACE" \
            --timeout=300s
    fi
    
    log_success "Deployment completed"
}

run_health_checks() {
    if [[ "$DRY_RUN" == true ]] || [[ "$BUILD_ONLY" == true ]]; then
        return 0
    fi
    
    log_info "Running health checks..."
    
    # Wait for pods to be ready
    kubectl wait --for=condition=ready pod \
        -l app=cipherscope-frontend \
        --namespace="$NAMESPACE" \
        --timeout=300s
    
    # Get service URL
    local service_url
    case "$ENVIRONMENT" in
        "production")
            service_url="https://cipherscope.com"
            ;;
        "staging")
            service_url="https://staging.cipherscope.com"
            ;;
        *)
            # For dev, use port-forward
            log_info "Setting up port-forward for health check..."
            kubectl port-forward service/cipherscope-frontend-service 8080:80 \
                --namespace="$NAMESPACE" &
            local port_forward_pid=$!
            sleep 5
            service_url="http://localhost:8080"
            ;;
    esac
    
    # Health check
    log_info "Checking health endpoint: ${service_url}/api/health"
    local max_attempts=10
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s "${service_url}/api/health" > /dev/null; then
            log_success "Health check passed"
            break
        else
            log_warning "Health check failed (attempt $attempt/$max_attempts)"
            sleep 10
            ((attempt++))
        fi
    done
    
    # Cleanup port-forward if used
    if [[ -n "$port_forward_pid" ]]; then
        kill $port_forward_pid 2>/dev/null || true
    fi
    
    if [[ $attempt -gt $max_attempts ]]; then
        log_error "Health checks failed after $max_attempts attempts"
        exit 1
    fi
}

cleanup() {
    log_info "Cleaning up..."
    
    # Remove old Docker images
    docker image prune -f
    
    log_success "Cleanup completed"
}

main() {
    log_info "Starting CipherScope Frontend deployment"
    log_info "Environment: $ENVIRONMENT"
    log_info "Build only: $BUILD_ONLY"
    log_info "Skip tests: $SKIP_TESTS"
    log_info "Skip build: $SKIP_BUILD"
    log_info "Dry run: $DRY_RUN"
    
    check_prerequisites
    load_environment
    run_tests
    build_docker_image
    deploy_to_kubernetes
    run_health_checks
    cleanup
    
    log_success "Deployment completed successfully!"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -b|--build-only)
            BUILD_ONLY=true
            shift
            ;;
        -s|--skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        -S|--skip-build)
            SKIP_BUILD=true
            shift
            ;;
        -f|--force)
            FORCE_DEPLOY=true
            shift
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Run main function
main
