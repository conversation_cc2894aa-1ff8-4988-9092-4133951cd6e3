#!/bin/bash

# CipherScope Monitoring Setup Script
# This script deploys Prometheus, Grafana, and monitoring stack

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="monitoring"
ENVIRONMENT="production"
DOMAIN=""
GRAFANA_PASSWORD="admin"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy monitoring stack for CipherScope

OPTIONS:
    -e, --environment ENV    Target environment (staging|production) [default: production]
    -d, --domain DOMAIN     Your domain name for Grafana access
    -p, --password PASS     <PERSON>ana admin password [default: admin]
    -n, --namespace NS      Kubernetes namespace [default: monitoring]
    -h, --help              Show this help message

EXAMPLES:
    $0 -e production -d cipherscope.com -p secure_password
    $0 -e staging -d staging.cipherscope.com

EOF
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if kubectl is available
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed or not in PATH"
        exit 1
    fi
    
    # Check if helm is available
    if ! command -v helm &> /dev/null; then
        log_error "helm is not installed or not in PATH"
        log_info "Install Helm: https://helm.sh/docs/intro/install/"
        exit 1
    fi
    
    # Check kubectl context
    local current_context=$(kubectl config current-context 2>/dev/null || echo "none")
    log_info "Current kubectl context: $current_context"
    
    # Check if cluster is accessible
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot access Kubernetes cluster"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

create_namespace() {
    log_info "Creating namespace: $NAMESPACE"
    
    kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -
    
    log_success "Namespace created: $NAMESPACE"
}

add_helm_repositories() {
    log_info "Adding Helm repositories..."
    
    # Add Prometheus community repo
    helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
    
    # Add Grafana repo
    helm repo add grafana https://grafana.github.io/helm-charts
    
    # Add Elastic repo for logging
    helm repo add elastic https://helm.elastic.co
    
    # Update repositories
    helm repo update
    
    log_success "Helm repositories added and updated"
}

create_prometheus_values() {
    log_info "Creating Prometheus values file..."
    
    cat > "./monitoring/prometheus-values.yaml" << EOF
# Prometheus Stack Values for CipherScope

prometheus:
  prometheusSpec:
    retention: 30d
    retentionSize: 50GB
    storageSpec:
      volumeClaimTemplate:
        spec:
          storageClassName: gp2
          accessModes: ["ReadWriteOnce"]
          resources:
            requests:
              storage: 100Gi
    
    additionalScrapeConfigs:
      - job_name: 'cipherscope-frontend'
        static_configs:
          - targets: ['cipherscope-frontend-service.cipherscope:80']
        metrics_path: /api/metrics
        scrape_interval: 30s
      
      - job_name: 'cipherscope-backend'
        static_configs:
          - targets: ['cipherscope-backend-service.cipherscope:8000']
        metrics_path: /metrics
        scrape_interval: 30s

grafana:
  enabled: true
  adminPassword: "$GRAFANA_PASSWORD"
  
  persistence:
    enabled: true
    storageClassName: gp2
    size: 10Gi
  
  ingress:
    enabled: true
    annotations:
      kubernetes.io/ingress.class: nginx
      cert-manager.io/cluster-issuer: letsencrypt-prod
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
    hosts:
      - grafana.$DOMAIN
    tls:
      - secretName: grafana-tls
        hosts:
          - grafana.$DOMAIN
  
  grafana.ini:
    server:
      root_url: https://grafana.$DOMAIN
    security:
      admin_password: "$GRAFANA_PASSWORD"
    auth:
      disable_login_form: false
    auth.anonymous:
      enabled: false

alertmanager:
  enabled: true
  alertmanagerSpec:
    storage:
      volumeClaimTemplate:
        spec:
          storageClassName: gp2
          accessModes: ["ReadWriteOnce"]
          resources:
            requests:
              storage: 10Gi

nodeExporter:
  enabled: true

kubeStateMetrics:
  enabled: true

defaultRules:
  create: true
  rules:
    alertmanager: true
    etcd: true
    general: true
    k8s: true
    kubeApiserver: true
    kubePrometheusNodeAlerting: true
    kubePrometheusNodeRecording: true
    kubernetesAbsent: true
    kubernetesApps: true
    kubernetesResources: true
    kubernetesStorage: true
    kubernetesSystem: true
    node: true
    prometheus: true
    prometheusOperator: true
EOF
    
    log_success "Prometheus values file created"
}

deploy_prometheus_stack() {
    log_info "Deploying Prometheus stack..."
    
    helm upgrade --install prometheus-stack prometheus-community/kube-prometheus-stack \
        --namespace "$NAMESPACE" \
        --values "./monitoring/prometheus-values.yaml" \
        --wait \
        --timeout 10m
    
    log_success "Prometheus stack deployed"
}

create_grafana_dashboards() {
    log_info "Creating Grafana dashboards..."
    
    # Create dashboard ConfigMap
    kubectl create configmap cipherscope-dashboards \
        --from-file=./monitoring/grafana/dashboards/ \
        --namespace="$NAMESPACE" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # Create dashboard provider
    cat > "./monitoring/grafana-dashboard-provider.yaml" << EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboard-provider
  namespace: $NAMESPACE
  labels:
    grafana_dashboard: "1"
data:
  dashboards.yaml: |
    apiVersion: 1
    providers:
      - name: 'cipherscope'
        orgId: 1
        folder: 'CipherScope'
        type: file
        disableDeletion: false
        updateIntervalSeconds: 10
        allowUiUpdates: true
        options:
          path: /var/lib/grafana/dashboards/cipherscope
EOF
    
    kubectl apply -f "./monitoring/grafana-dashboard-provider.yaml"
    
    log_success "Grafana dashboards configured"
}

create_alerting_rules() {
    log_info "Creating alerting rules..."
    
    cat > "./monitoring/alert-rules.yaml" << EOF
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: cipherscope-alerts
  namespace: $NAMESPACE
  labels:
    prometheus: kube-prometheus
    role: alert-rules
spec:
  groups:
    - name: cipherscope.frontend
      rules:
        - alert: FrontendHighErrorRate
          expr: rate(cipherscope_frontend_requests_total{status=~"5.."}[5m]) > 0.1
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "High error rate in frontend"
            description: "Frontend error rate is {{ \$value }} errors per second"
        
        - alert: FrontendHighResponseTime
          expr: histogram_quantile(0.95, rate(cipherscope_frontend_request_duration_seconds_bucket[5m])) > 2
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "High response time in frontend"
            description: "95th percentile response time is {{ \$value }} seconds"
        
        - alert: FrontendDown
          expr: up{job="cipherscope-frontend"} == 0
          for: 1m
          labels:
            severity: critical
          annotations:
            summary: "Frontend is down"
            description: "Frontend service is not responding"
    
    - name: cipherscope.system
      rules:
        - alert: HighCPUUsage
          expr: cipherscope_system_load_average{period="1m"} > 0.8
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "High CPU usage"
            description: "System load average is {{ \$value }}"
        
        - alert: HighMemoryUsage
          expr: (cipherscope_frontend_memory_usage_mb{type="rss"} / cipherscope_system_memory_mb{type="total"}) > 0.8
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "High memory usage"
            description: "Memory usage is {{ \$value | humanizePercentage }}"
        
        - alert: LowDiskSpace
          expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) < 0.1
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: "Low disk space"
            description: "Disk space is {{ \$value | humanizePercentage }} full"
EOF
    
    kubectl apply -f "./monitoring/alert-rules.yaml"
    
    log_success "Alerting rules created"
}

setup_logging() {
    log_info "Setting up logging with ELK stack..."
    
    # Deploy Elasticsearch
    helm upgrade --install elasticsearch elastic/elasticsearch \
        --namespace "$NAMESPACE" \
        --set replicas=1 \
        --set minimumMasterNodes=1 \
        --set resources.requests.cpu=100m \
        --set resources.requests.memory=512Mi \
        --set resources.limits.cpu=1000m \
        --set resources.limits.memory=2Gi \
        --set volumeClaimTemplate.resources.requests.storage=10Gi \
        --wait \
        --timeout 10m
    
    # Deploy Kibana
    helm upgrade --install kibana elastic/kibana \
        --namespace "$NAMESPACE" \
        --set elasticsearchHosts="http://elasticsearch-master:9200" \
        --set ingress.enabled=true \
        --set ingress.annotations."kubernetes\.io/ingress\.class"=nginx \
        --set ingress.annotations."cert-manager\.io/cluster-issuer"=letsencrypt-prod \
        --set ingress.hosts[0].host="kibana.$DOMAIN" \
        --set ingress.hosts[0].paths[0].path="/" \
        --set ingress.tls[0].secretName=kibana-tls \
        --set ingress.tls[0].hosts[0]="kibana.$DOMAIN" \
        --wait \
        --timeout 10m
    
    # Deploy Filebeat
    helm upgrade --install filebeat elastic/filebeat \
        --namespace "$NAMESPACE" \
        --set daemonset.enabled=true \
        --set deployment.enabled=false \
        --wait \
        --timeout 5m
    
    log_success "ELK stack deployed"
}

verify_deployment() {
    log_info "Verifying monitoring deployment..."
    
    # Check if all pods are running
    log_info "Checking pod status..."
    kubectl get pods -n "$NAMESPACE"
    
    # Check services
    log_info "Checking services..."
    kubectl get services -n "$NAMESPACE"
    
    # Check ingresses
    log_info "Checking ingresses..."
    kubectl get ingresses -n "$NAMESPACE"
    
    # Wait for pods to be ready
    log_info "Waiting for pods to be ready..."
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=prometheus -n "$NAMESPACE" --timeout=300s
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=grafana -n "$NAMESPACE" --timeout=300s
    
    log_success "Monitoring deployment verified"
}

show_access_info() {
    log_success "Monitoring stack deployed successfully!"
    echo
    log_info "Access Information:"
    
    if [[ -n "$DOMAIN" ]]; then
        echo "Grafana: https://grafana.$DOMAIN"
        echo "Kibana: https://kibana.$DOMAIN"
    else
        echo "Grafana: Use port-forward - kubectl port-forward svc/prometheus-stack-grafana 3000:80 -n $NAMESPACE"
        echo "Kibana: Use port-forward - kubectl port-forward svc/kibana-kibana 5601:5601 -n $NAMESPACE"
    fi
    
    echo "Prometheus: Use port-forward - kubectl port-forward svc/prometheus-stack-kube-prom-prometheus 9090:9090 -n $NAMESPACE"
    echo "AlertManager: Use port-forward - kubectl port-forward svc/prometheus-stack-kube-prom-alertmanager 9093:9093 -n $NAMESPACE"
    echo
    log_info "Default Credentials:"
    echo "Grafana - Username: admin, Password: $GRAFANA_PASSWORD"
    echo
    log_info "Useful Commands:"
    echo "View logs: kubectl logs -f deployment/prometheus-stack-grafana -n $NAMESPACE"
    echo "Get pods: kubectl get pods -n $NAMESPACE"
    echo "Port forward Grafana: kubectl port-forward svc/prometheus-stack-grafana 3000:80 -n $NAMESPACE"
}

main() {
    log_info "Starting monitoring stack deployment"
    log_info "Environment: $ENVIRONMENT"
    log_info "Namespace: $NAMESPACE"
    log_info "Domain: ${DOMAIN:-'Not specified'}"
    
    check_prerequisites
    create_namespace
    add_helm_repositories
    
    if [[ -n "$DOMAIN" ]]; then
        create_prometheus_values
    fi
    
    deploy_prometheus_stack
    create_grafana_dashboards
    create_alerting_rules
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        setup_logging
    fi
    
    verify_deployment
    show_access_info
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -d|--domain)
            DOMAIN="$2"
            shift 2
            ;;
        -p|--password)
            GRAFANA_PASSWORD="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Run main function
main
