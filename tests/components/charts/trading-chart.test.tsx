import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { TradingChart } from '@/components/charts/trading-chart'

// Mock the lightweight-charts library
jest.mock('lightweight-charts')

// Create a test wrapper with QueryClient
const createTestWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('TradingChart', () => {
  let mockChart: any
  let mockCandlestickSeries: any
  let mockVolumeSeries: any

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks()

    // Set up mock chart and series
    mockCandlestickSeries = {
      setData: jest.fn(),
      update: jest.fn(),
      applyOptions: jest.fn(),
    }

    mockVolumeSeries = {
      setData: jest.fn(),
      update: jest.fn(),
      applyOptions: jest.fn(),
    }

    mockChart = {
      addCandlestickSeries: jest.fn(() => mockCandlestickSeries),
      addVolumeSeries: jest.fn(() => mockVolumeSeries),
      timeScale: jest.fn(() => ({
        fitContent: jest.fn(),
        setVisibleRange: jest.fn(),
        getVisibleRange: jest.fn(),
      })),
      priceScale: jest.fn(() => ({
        applyOptions: jest.fn(),
      })),
      applyOptions: jest.fn(),
      resize: jest.fn(),
      remove: jest.fn(),
      subscribeCrosshairMove: jest.fn(),
      unsubscribeCrosshairMove: jest.fn(),
    }

    // Mock createChart to return our mock chart
    const { createChart } = require('lightweight-charts')
    createChart.mockReturnValue(mockChart)
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  it('renders without crashing', () => {
    const Wrapper = createTestWrapper()
    
    render(
      <Wrapper>
        <TradingChart symbol="BTCUSDT" />
      </Wrapper>
    )

    expect(screen.getByTestId('trading-chart')).toBeInTheDocument()
  })

  it('displays loading state initially', () => {
    const Wrapper = createTestWrapper()
    
    render(
      <Wrapper>
        <TradingChart symbol="BTCUSDT" />
      </Wrapper>
    )

    expect(screen.getByText('Loading chart data...')).toBeInTheDocument()
  })

  it('creates chart with correct configuration', async () => {
    const Wrapper = createTestWrapper()
    const { createChart } = require('lightweight-charts')

    render(
      <Wrapper>
        <TradingChart symbol="BTCUSDT" />
      </Wrapper>
    )

    await waitFor(() => {
      expect(createChart).toHaveBeenCalledWith(
        expect.any(HTMLElement),
        expect.objectContaining({
          width: expect.any(Number),
          height: expect.any(Number),
          layout: expect.objectContaining({
            background: expect.any(Object),
            textColor: expect.any(String),
          }),
          grid: expect.any(Object),
          crosshair: expect.any(Object),
          rightPriceScale: expect.any(Object),
          timeScale: expect.any(Object),
        })
      )
    })
  })

  it('adds candlestick and volume series', async () => {
    const Wrapper = createTestWrapper()

    render(
      <Wrapper>
        <TradingChart symbol="BTCUSDT" />
      </Wrapper>
    )

    await waitFor(() => {
      expect(mockChart.addCandlestickSeries).toHaveBeenCalledWith(
        expect.objectContaining({
          upColor: expect.any(String),
          downColor: expect.any(String),
          borderVisible: false,
          wickUpColor: expect.any(String),
          wickDownColor: expect.any(String),
        })
      )

      expect(mockChart.addVolumeSeries).toHaveBeenCalledWith(
        expect.objectContaining({
          color: expect.any(String),
          priceFormat: expect.objectContaining({
            type: 'volume',
          }),
          priceScaleId: '',
        })
      )
    })
  })

  it('handles timeframe changes', async () => {
    const Wrapper = createTestWrapper()

    render(
      <Wrapper>
        <TradingChart symbol="BTCUSDT" />
      </Wrapper>
    )

    // Wait for initial render
    await waitFor(() => {
      expect(screen.getByTestId('trading-chart')).toBeInTheDocument()
    })

    // Find and click timeframe button
    const timeframeButton = screen.getByText('1H')
    fireEvent.click(timeframeButton)

    // Verify that the chart data is updated
    await waitFor(() => {
      expect(mockCandlestickSeries.setData).toHaveBeenCalled()
    })
  })

  it('handles symbol changes', async () => {
    const Wrapper = createTestWrapper()
    const { rerender } = render(
      <Wrapper>
        <TradingChart symbol="BTCUSDT" />
      </Wrapper>
    )

    // Wait for initial render
    await waitFor(() => {
      expect(mockCandlestickSeries.setData).toHaveBeenCalled()
    })

    // Clear previous calls
    jest.clearAllMocks()

    // Change symbol
    rerender(
      <Wrapper>
        <TradingChart symbol="ETHUSDT" />
      </Wrapper>
    )

    // Verify that new data is loaded
    await waitFor(() => {
      expect(mockCandlestickSeries.setData).toHaveBeenCalled()
    })
  })

  it('displays technical indicators', async () => {
    const Wrapper = createTestWrapper()

    render(
      <Wrapper>
        <TradingChart symbol="BTCUSDT" showIndicators={true} />
      </Wrapper>
    )

    await waitFor(() => {
      expect(screen.getByText('Indicators')).toBeInTheDocument()
    })

    // Check for common indicators
    expect(screen.getByText('MA')).toBeInTheDocument()
    expect(screen.getByText('RSI')).toBeInTheDocument()
    expect(screen.getByText('MACD')).toBeInTheDocument()
  })

  it('handles real-time updates', async () => {
    const Wrapper = createTestWrapper()

    render(
      <Wrapper>
        <TradingChart symbol="BTCUSDT" realTime={true} />
      </Wrapper>
    )

    // Wait for initial render
    await waitFor(() => {
      expect(mockCandlestickSeries.setData).toHaveBeenCalled()
    })

    // Simulate real-time update
    const mockUpdate = {
      time: Math.floor(Date.now() / 1000),
      open: 45000,
      high: 45500,
      low: 44500,
      close: 45200,
      volume: 1000,
    }

    // Trigger update (this would normally come from WebSocket)
    fireEvent(window, new CustomEvent('chart-update', { detail: mockUpdate }))

    await waitFor(() => {
      expect(mockCandlestickSeries.update).toHaveBeenCalledWith(
        expect.objectContaining({
          time: mockUpdate.time,
          open: mockUpdate.open,
          high: mockUpdate.high,
          low: mockUpdate.low,
          close: mockUpdate.close,
        })
      )
    })
  })

  it('handles chart resize', async () => {
    const Wrapper = createTestWrapper()

    render(
      <Wrapper>
        <TradingChart symbol="BTCUSDT" />
      </Wrapper>
    )

    // Wait for initial render
    await waitFor(() => {
      expect(mockChart.resize).toHaveBeenCalled()
    })

    // Simulate window resize
    fireEvent(window, new Event('resize'))

    await waitFor(() => {
      expect(mockChart.resize).toHaveBeenCalledTimes(2)
    })
  })

  it('cleans up chart on unmount', async () => {
    const Wrapper = createTestWrapper()
    const { unmount } = render(
      <Wrapper>
        <TradingChart symbol="BTCUSDT" />
      </Wrapper>
    )

    // Wait for chart creation
    await waitFor(() => {
      expect(mockChart.addCandlestickSeries).toHaveBeenCalled()
    })

    // Unmount component
    unmount()

    // Verify cleanup
    expect(mockChart.remove).toHaveBeenCalled()
  })

  it('handles error states gracefully', async () => {
    const Wrapper = createTestWrapper()

    // Mock API error
    global.testUtils.mockApiError('Network error')

    render(
      <Wrapper>
        <TradingChart symbol="INVALID_SYMBOL" />
      </Wrapper>
    )

    await waitFor(() => {
      expect(screen.getByText(/error loading chart data/i)).toBeInTheDocument()
    })
  })

  it('displays chart controls', async () => {
    const Wrapper = createTestWrapper()

    render(
      <Wrapper>
        <TradingChart symbol="BTCUSDT" showControls={true} />
      </Wrapper>
    )

    await waitFor(() => {
      // Check for timeframe controls
      expect(screen.getByText('1M')).toBeInTheDocument()
      expect(screen.getByText('5M')).toBeInTheDocument()
      expect(screen.getByText('1H')).toBeInTheDocument()
      expect(screen.getByText('1D')).toBeInTheDocument()

      // Check for chart type controls
      expect(screen.getByText('Candlestick')).toBeInTheDocument()
      expect(screen.getByText('Line')).toBeInTheDocument()
    })
  })

  it('supports custom styling', async () => {
    const Wrapper = createTestWrapper()
    const customTheme = {
      background: '#000000',
      textColor: '#ffffff',
      upColor: '#00ff00',
      downColor: '#ff0000',
    }

    render(
      <Wrapper>
        <TradingChart symbol="BTCUSDT" theme={customTheme} />
      </Wrapper>
    )

    await waitFor(() => {
      expect(mockChart.applyOptions).toHaveBeenCalledWith(
        expect.objectContaining({
          layout: expect.objectContaining({
            background: expect.objectContaining({
              color: customTheme.background,
            }),
            textColor: customTheme.textColor,
          }),
        })
      )
    })
  })
})
