import { renderHook, act, waitFor } from '@testing-library/react'
import { useRealTimeData } from '@/hooks/useRealTimeData'

// Mock WebSocket
const mockWebSocket = {
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  send: jest.fn(),
  close: jest.fn(),
  readyState: 1,
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3,
}

// Mock WebSocket constructor
global.WebSocket = jest.fn(() => mockWebSocket) as any

describe('useRealTimeData', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Reset WebSocket mock
    mockWebSocket.addEventListener.mockClear()
    mockWebSocket.removeEventListener.mockClear()
    mockWebSocket.send.mockClear()
    mockWebSocket.close.mockClear()
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  it('initializes with default state', () => {
    const { result } = renderHook(() => useRealTimeData())

    expect(result.current.isConnected).toBe(false)
    expect(result.current.connectionStatus).toBe('disconnected')
    expect(result.current.lastUpdate).toBeNull()
    expect(result.current.error).toBeNull()
    expect(result.current.tokenPrices).toEqual({})
    expect(result.current.systemMetrics).toEqual({})
    expect(typeof result.current.connect).toBe('function')
    expect(typeof result.current.disconnect).toBe('function')
    expect(typeof result.current.subscribe).toBe('function')
    expect(typeof result.current.unsubscribe).toBe('function')
  })

  it('establishes WebSocket connection', async () => {
    const { result } = renderHook(() => useRealTimeData())

    act(() => {
      result.current.connect()
    })

    expect(global.WebSocket).toHaveBeenCalledWith(
      expect.stringContaining('ws://localhost:8000/ws')
    )
    expect(mockWebSocket.addEventListener).toHaveBeenCalledWith(
      'open',
      expect.any(Function)
    )
    expect(mockWebSocket.addEventListener).toHaveBeenCalledWith(
      'message',
      expect.any(Function)
    )
    expect(mockWebSocket.addEventListener).toHaveBeenCalledWith(
      'error',
      expect.any(Function)
    )
    expect(mockWebSocket.addEventListener).toHaveBeenCalledWith(
      'close',
      expect.any(Function)
    )
  })

  it('handles connection open event', async () => {
    const { result } = renderHook(() => useRealTimeData())

    act(() => {
      result.current.connect()
    })

    // Simulate WebSocket open event
    const openHandler = mockWebSocket.addEventListener.mock.calls.find(
      call => call[0] === 'open'
    )?.[1]

    act(() => {
      openHandler?.()
    })

    await waitFor(() => {
      expect(result.current.isConnected).toBe(true)
      expect(result.current.connectionStatus).toBe('connected')
      expect(result.current.error).toBeNull()
    })
  })

  it('handles connection error', async () => {
    const { result } = renderHook(() => useRealTimeData())

    act(() => {
      result.current.connect()
    })

    // Simulate WebSocket error event
    const errorHandler = mockWebSocket.addEventListener.mock.calls.find(
      call => call[0] === 'error'
    )?.[1]

    const mockError = new Error('Connection failed')

    act(() => {
      errorHandler?.(mockError)
    })

    await waitFor(() => {
      expect(result.current.isConnected).toBe(false)
      expect(result.current.connectionStatus).toBe('error')
      expect(result.current.error).toBe('Connection failed')
    })
  })

  it('handles connection close', async () => {
    const { result } = renderHook(() => useRealTimeData())

    act(() => {
      result.current.connect()
    })

    // First open the connection
    const openHandler = mockWebSocket.addEventListener.mock.calls.find(
      call => call[0] === 'open'
    )?.[1]

    act(() => {
      openHandler?.()
    })

    // Then close it
    const closeHandler = mockWebSocket.addEventListener.mock.calls.find(
      call => call[0] === 'close'
    )?.[1]

    act(() => {
      closeHandler?.({ code: 1000, reason: 'Normal closure' })
    })

    await waitFor(() => {
      expect(result.current.isConnected).toBe(false)
      expect(result.current.connectionStatus).toBe('disconnected')
    })
  })

  it('processes token price updates', async () => {
    const { result } = renderHook(() => useRealTimeData())

    act(() => {
      result.current.connect()
    })

    // Simulate connection open
    const openHandler = mockWebSocket.addEventListener.mock.calls.find(
      call => call[0] === 'open'
    )?.[1]

    act(() => {
      openHandler?.()
    })

    // Simulate message event with token price update
    const messageHandler = mockWebSocket.addEventListener.mock.calls.find(
      call => call[0] === 'message'
    )?.[1]

    const mockPriceUpdate = {
      data: JSON.stringify({
        type: 'token_price_update',
        data: {
          symbol: 'BTCUSDT',
          price: 45000,
          change24h: 2.5,
          volume: 1000000,
          timestamp: Date.now(),
        },
      }),
    }

    act(() => {
      messageHandler?.(mockPriceUpdate)
    })

    await waitFor(() => {
      expect(result.current.tokenPrices['BTCUSDT']).toEqual({
        symbol: 'BTCUSDT',
        price: 45000,
        change24h: 2.5,
        volume: 1000000,
        timestamp: expect.any(Number),
      })
      expect(result.current.lastUpdate).not.toBeNull()
    })
  })

  it('processes system metrics updates', async () => {
    const { result } = renderHook(() => useRealTimeData())

    act(() => {
      result.current.connect()
    })

    // Simulate connection open
    const openHandler = mockWebSocket.addEventListener.mock.calls.find(
      call => call[0] === 'open'
    )?.[1]

    act(() => {
      openHandler?.()
    })

    // Simulate message event with system metrics
    const messageHandler = mockWebSocket.addEventListener.mock.calls.find(
      call => call[0] === 'message'
    )?.[1]

    const mockMetricsUpdate = {
      data: JSON.stringify({
        type: 'system_metrics',
        data: {
          cpu: 45.2,
          memory: 62.8,
          disk: 34.1,
          network: 28.9,
          timestamp: Date.now(),
        },
      }),
    }

    act(() => {
      messageHandler?.(mockMetricsUpdate)
    })

    await waitFor(() => {
      expect(result.current.systemMetrics).toEqual({
        cpu: 45.2,
        memory: 62.8,
        disk: 34.1,
        network: 28.9,
        timestamp: expect.any(Number),
      })
    })
  })

  it('handles subscription to specific channels', async () => {
    const { result } = renderHook(() => useRealTimeData())

    act(() => {
      result.current.connect()
    })

    // Simulate connection open
    const openHandler = mockWebSocket.addEventListener.mock.calls.find(
      call => call[0] === 'open'
    )?.[1]

    act(() => {
      openHandler?.()
    })

    // Subscribe to a channel
    act(() => {
      result.current.subscribe('token_prices', ['BTCUSDT', 'ETHUSDT'])
    })

    expect(mockWebSocket.send).toHaveBeenCalledWith(
      JSON.stringify({
        type: 'subscribe',
        channel: 'token_prices',
        symbols: ['BTCUSDT', 'ETHUSDT'],
      })
    )
  })

  it('handles unsubscription from channels', async () => {
    const { result } = renderHook(() => useRealTimeData())

    act(() => {
      result.current.connect()
    })

    // Simulate connection open
    const openHandler = mockWebSocket.addEventListener.mock.calls.find(
      call => call[0] === 'open'
    )?.[1]

    act(() => {
      openHandler?.()
    })

    // Unsubscribe from a channel
    act(() => {
      result.current.unsubscribe('token_prices', ['BTCUSDT'])
    })

    expect(mockWebSocket.send).toHaveBeenCalledWith(
      JSON.stringify({
        type: 'unsubscribe',
        channel: 'token_prices',
        symbols: ['BTCUSDT'],
      })
    )
  })

  it('disconnects WebSocket connection', async () => {
    const { result } = renderHook(() => useRealTimeData())

    act(() => {
      result.current.connect()
    })

    act(() => {
      result.current.disconnect()
    })

    expect(mockWebSocket.close).toHaveBeenCalled()
  })

  it('handles automatic reconnection', async () => {
    const { result } = renderHook(() => useRealTimeData())

    act(() => {
      result.current.connect()
    })

    // Simulate connection open
    const openHandler = mockWebSocket.addEventListener.mock.calls.find(
      call => call[0] === 'open'
    )?.[1]

    act(() => {
      openHandler?.()
    })

    // Simulate unexpected close (should trigger reconnection)
    const closeHandler = mockWebSocket.addEventListener.mock.calls.find(
      call => call[0] === 'close'
    )?.[1]

    act(() => {
      closeHandler?.({ code: 1006, reason: 'Abnormal closure' })
    })

    await waitFor(() => {
      expect(result.current.connectionStatus).toBe('reconnecting')
    })

    // Wait for reconnection attempt
    await waitFor(
      () => {
        expect(global.WebSocket).toHaveBeenCalledTimes(2)
      },
      { timeout: 6000 }
    )
  })

  it('provides connection statistics', async () => {
    const { result } = renderHook(() => useRealTimeData())

    act(() => {
      result.current.connect()
    })

    // Simulate connection open
    const openHandler = mockWebSocket.addEventListener.mock.calls.find(
      call => call[0] === 'open'
    )?.[1]

    act(() => {
      openHandler?.()
    })

    // Simulate some message events
    const messageHandler = mockWebSocket.addEventListener.mock.calls.find(
      call => call[0] === 'message'
    )?.[1]

    for (let i = 0; i < 5; i++) {
      act(() => {
        messageHandler?.({
          data: JSON.stringify({
            type: 'token_price_update',
            data: { symbol: 'BTCUSDT', price: 45000 + i },
          }),
        })
      })
    }

    const stats = result.current.getConnectionStats()

    expect(stats).toEqual({
      isConnected: true,
      messagesReceived: 5,
      lastMessageTime: expect.any(Number),
      connectionTime: expect.any(Number),
      reconnectAttempts: 0,
    })
  })

  it('handles malformed messages gracefully', async () => {
    const { result } = renderHook(() => useRealTimeData())

    act(() => {
      result.current.connect()
    })

    // Simulate connection open
    const openHandler = mockWebSocket.addEventListener.mock.calls.find(
      call => call[0] === 'open'
    )?.[1]

    act(() => {
      openHandler?.()
    })

    // Simulate malformed message
    const messageHandler = mockWebSocket.addEventListener.mock.calls.find(
      call => call[0] === 'message'
    )?.[1]

    act(() => {
      messageHandler?.({ data: 'invalid json' })
    })

    // Should not crash and connection should remain stable
    await waitFor(() => {
      expect(result.current.isConnected).toBe(true)
      expect(result.current.error).toBeNull()
    })
  })

  it('cleans up on unmount', () => {
    const { result, unmount } = renderHook(() => useRealTimeData())

    act(() => {
      result.current.connect()
    })

    unmount()

    expect(mockWebSocket.close).toHaveBeenCalled()
  })
})
