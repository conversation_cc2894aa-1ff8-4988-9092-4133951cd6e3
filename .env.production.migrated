# CipherScope Production Environment - Migrated Configuration
# All API keys and configurations migrated from existing .env file

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Environment
NODE_ENV=production
VITE_ENVIRONMENT=production
VITE_APP_NAME=CipherScope
VITE_APP_VERSION=1.0.0

# Application URLs (Production - UPDATE WITH YOUR DOMAIN)
VITE_APP_URL=https://your-domain.com
VITE_API_URL=https://api.your-domain.com
VITE_WS_URL=wss://api.your-domain.com/ws

# =============================================================================
# BLOCKCHAIN & WEB3 CONFIGURATION (MIGRATED FROM EXISTING .ENV)
# =============================================================================

# Infura API Key (from your existing config)
INFURA_API_KEY=********************************
VITE_INFURA_API_KEY=********************************

# Web3 Provider (Alchemy - from your existing config)
WEB3_PROVIDER_URL=https://eth-mainnet.g.alchemy.com/v2/BwYx2Nu9Fo8TBdqPmMw18bIz_MWdsQP-
VITE_WEB3_PROVIDER_URL=https://eth-mainnet.g.alchemy.com/v2/BwYx2Nu9Fo8TBdqPmMw18bIz_MWdsQP-

# Blockchain RPC URLs (Production endpoints)
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/********************************
POLYGON_RPC_URL=https://polygon-mainnet.infura.io/v3/********************************
BSC_RPC_URL=https://bsc-dataseed.binance.org/

VITE_ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/********************************
VITE_POLYGON_RPC_URL=https://polygon-mainnet.infura.io/v3/********************************
VITE_BSC_RPC_URL=https://bsc-dataseed.binance.org/

# =============================================================================
# API KEYS (MIGRATED FROM EXISTING CONFIGURATION)
# =============================================================================

# CoinGecko API (from your existing config)
COINGECKO_API_KEY=CG-wJx8SHeFD1KjG3F73LB14967
VITE_COINGECKO_API_KEY=CG-wJx8SHeFD1KjG3F73LB14967

# CoinAPI (Crypto data)
COIN_API_KEY=1dc67551-276c-4098-a1f2-2b58ee57ce69
VITE_COIN_API_KEY=1dc67551-276c-4098-a1f2-2b58ee57ce69

# Etherscan API (Ethereum blockchain data)
ETHERSCAN_API_KEY=**********************************
VITE_ETHERSCAN_API_KEY=**********************************

# Dune Analytics (Blockchain analytics)
DUNE_API_KEY=8L1zlFUtMWg4FCMsZuFkKIkXbRpPYefv
VITE_DUNE_API_KEY=8L1zlFUtMWg4FCMsZuFkKIkXbRpPYefv

# Birdeye API (Solana-focused)
API_BIRDEYE_API_KEY=34eb196d0dad4234ace628c3e8767360
VITE_BIRDEYE_API_KEY=34eb196d0dad4234ace628c3e8767360

# =============================================================================
# WEBSOCKET CONFIGURATION (MIGRATED FROM EXISTING CONFIG)
# =============================================================================

# Main WebSocket URLs
INFURA_MAINNET_WS=wss://mainnet.infura.io/ws/v3/********************************
INFURA_SEPOLIA_WS=wss://sepolia.infura.io/ws/v3/********************************

VITE_INFURA_MAINNET_WS=wss://mainnet.infura.io/ws/v3/********************************
VITE_INFURA_SEPOLIA_WS=wss://sepolia.infura.io/ws/v3/********************************

# Multi-chain WebSocket URLs
INFURA_POLYGON_MAINNET_WS=wss://polygon-mainnet.infura.io/ws/v3/********************************
INFURA_BASE_MAINNET_WS=wss://base-mainnet.infura.io/ws/v3/********************************
INFURA_ARBITRUM_MAINNET_WS=wss://arbitrum-mainnet.infura.io/ws/v3/********************************
INFURA_OPTIMISM_MAINNET_WS=wss://optimism-mainnet.infura.io/ws/v3/********************************

VITE_INFURA_POLYGON_MAINNET_WS=wss://polygon-mainnet.infura.io/ws/v3/********************************
VITE_INFURA_BASE_MAINNET_WS=wss://base-mainnet.infura.io/ws/v3/********************************
VITE_INFURA_ARBITRUM_MAINNET_WS=wss://arbitrum-mainnet.infura.io/ws/v3/********************************
VITE_INFURA_OPTIMISM_MAINNET_WS=wss://optimism-mainnet.infura.io/ws/v3/********************************

# =============================================================================
# SOCIAL MEDIA & SENTIMENT ANALYSIS (MIGRATED)
# =============================================================================

# Reddit API (For social sentiment)
REDDIT_CLIENT_ID=Ar2tSflbt93IycLB0fEyLg
REDDIT_CLIENT_SECRET=ec67445f-0ac1-4f3a-8de3-c54ec3233341

# =============================================================================
# AI & ML SERVICES (MIGRATED)
# =============================================================================

# OpenRouter API (AI services)
OPENROUTER_API_KEY=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

# =============================================================================
# WEB SCRAPING & DATA COLLECTION (MIGRATED)
# =============================================================================

# ScrapingBee API (Web scraping)
SCRAPINGBEE_API_KEY=********************************************************************************

# HyperBrowser API
HYPERBROWSER_API_KEY=hb_adc1508444618104ba9ed438f19b

# =============================================================================
# SECURITY & AUTHENTICATION
# =============================================================================

# JWT Configuration (Production - CHANGE THESE)
JWT_SECRET_KEY=CHANGE_ME_TO_SECURE_RANDOM_STRING_64_CHARS_MIN
ENCRYPTION_KEY=CHANGE_ME_TO_SECURE_RANDOM_STRING_64_CHARS_MIN

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Database (PostgreSQL for production)
DATABASE_URL=*********************************************************/cipherscope
VITE_DATABASE_TYPE=postgresql

# Redis Configuration
REDIS_URL=redis://your-elasticache-endpoint:6379/0

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/disable features for production
VITE_ENABLE_REAL_TIME=true
VITE_ENABLE_ADVANCED_CHARTS=true
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_TRADING=true
VITE_ENABLE_SECURITY_MONITORING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true

# =============================================================================
# PRODUCTION CONFIGURATION
# =============================================================================

# Debug and Logging (Disabled for production)
DEBUG=false
LOG_LEVEL=INFO
VITE_DEBUG=false
VITE_LOG_LEVEL=INFO

# Production Features
VITE_ENABLE_DEVTOOLS=false
VITE_ENABLE_HOT_RELOAD=false
VITE_ENABLE_MOCK_DATA=false
VITE_ENABLE_DEBUG_PANEL=false

# Performance Settings (Production optimized)
MAX_CONCURRENT_ANALYSES=50
CACHE_TTL_SECONDS=1800
API_RATE_LIMIT_PER_MINUTE=1000

# =============================================================================
# MONITORING & ANALYTICS
# =============================================================================

# Analytics
VITE_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
VITE_SENTRY_DSN=https://<EMAIL>/project-id

# =============================================================================
# AWS CONFIGURATION
# =============================================================================

AWS_REGION=us-west-2
AWS_S3_BUCKET=cipherscope-assets-production

# =============================================================================
# SECURITY
# =============================================================================

# CORS Configuration
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
