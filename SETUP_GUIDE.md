# CipherScope Frontend Setup Guide

This comprehensive guide will walk you through setting up the CipherScope frontend for production deployment.

## 📋 Prerequisites

Before starting, ensure you have:

- **Domain name** (e.g., `cipherscope.com`)
- **AWS Account** with appropriate permissions
- **Kubernetes cluster** (<PERSON><PERSON> recommended)
- **Docker** and **Docker Compose**
- **kubectl** configured for your cluster
- **Helm 3.8+**
- **Terraform 1.0+**
- **Node.js 18+** (for local development)

## 🚀 Quick Start

### 1. Clone and Setup

```bash
# Clone the repository
git clone https://github.com/cipherscope/frontend.git
cd frontend

# Make scripts executable
chmod +x scripts/*.sh
```

### 2. Environment Configuration

Run the interactive setup script:

```bash
./scripts/setup-environment.sh -e production -d your-domain.com -m <EMAIL>
```

Or manually configure:

```bash
# Copy environment template
cp .env.production .env.production.local

# Edit with your configuration
nano .env.production.local
```

### 3. SSL Certificate Setup

Choose your SSL provider:

```bash
# Option A: AWS Certificate Manager (Recommended for AWS)
./scripts/setup-ssl.sh -d your-domain.com -e <EMAIL> -p aws

# Option B: Let's Encrypt
./scripts/setup-ssl.sh -d your-domain.com -e <EMAIL> -p letsencrypt

# Option C: Cloudflare
./scripts/setup-ssl.sh -d your-domain.com -e <EMAIL> -p cloudflare
```

### 4. Infrastructure Deployment

```bash
# Initialize Terraform
cd terraform
terraform init

# Plan deployment
terraform plan -var-file="environments/production.tfvars"

# Deploy infrastructure
terraform apply -var-file="environments/production.tfvars"

# Get outputs
terraform output
```

### 5. Application Deployment

```bash
# Deploy to Kubernetes
./scripts/deploy.sh -e production -f

# Verify deployment
kubectl get pods -n cipherscope
```

### 6. Monitoring Setup

```bash
# Deploy monitoring stack
./scripts/setup-monitoring.sh -e production -d your-domain.com -p secure_grafana_password
```

## 🔧 Detailed Configuration

### Environment Variables

#### Required Variables

Update these in your `.env.production.local`:

```bash
# Domain Configuration
NEXT_PUBLIC_APP_URL=https://your-domain.com
NEXT_PUBLIC_API_URL=https://api.your-domain.com
NEXT_PUBLIC_WS_URL=wss://api.your-domain.com/ws

# Security (Generate secure random strings)
NEXTAUTH_SECRET=your-32-char-random-string
JWT_SECRET=your-64-char-random-string
SESSION_SECRET=your-32-char-random-string

# Database (From Terraform output)
DATABASE_URL=************************************************/database

# Redis (From Terraform output)
REDIS_URL=redis://elasticache-endpoint:6379/0
```

#### API Keys

Add your API keys for external services:

```bash
# Market Data
NEXT_PUBLIC_COINGECKO_API_KEY=your-coingecko-key
NEXT_PUBLIC_BINANCE_API_KEY=your-binance-key
NEXT_PUBLIC_POLYGON_API_KEY=your-polygon-key

# Blockchain RPC
NEXT_PUBLIC_ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your-project-id
NEXT_PUBLIC_POLYGON_RPC_URL=https://polygon-mainnet.infura.io/v3/your-project-id

# Analytics & Monitoring
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id
```

### Domain Configuration

#### DNS Setup

Configure your DNS records:

```
# A Records
your-domain.com         → Load Balancer IP
www.your-domain.com     → Load Balancer IP
api.your-domain.com     → Load Balancer IP

# CNAME Records (for subdomains)
grafana.your-domain.com → Load Balancer CNAME
kibana.your-domain.com  → Load Balancer CNAME

# For AWS ACM validation (if using AWS)
_validation.your-domain.com → AWS validation CNAME
```

#### SSL Certificate Validation

If using AWS ACM:

1. Go to AWS Certificate Manager console
2. Find your certificate request
3. Copy the DNS validation records
4. Add them to your DNS provider
5. Wait for validation (5-30 minutes)

### Backend Integration

#### API Endpoints

Configure your backend API endpoints in the environment file:

```bash
# Backend API Base URL
NEXT_PUBLIC_API_URL=https://api.your-domain.com

# WebSocket URL
NEXT_PUBLIC_WS_URL=wss://api.your-domain.com/ws

# Health Check Endpoint
BACKEND_HEALTH_URL=https://api.your-domain.com/health
```

#### Authentication

Set up authentication integration:

```bash
# NextAuth Configuration
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your-secure-secret

# JWT Configuration
JWT_SECRET=your-jwt-secret
JWT_ALGORITHM=HS256
JWT_EXPIRATION=24h
```

#### CORS Configuration

Ensure your backend allows requests from your frontend domain:

```bash
# Backend CORS Origins
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com
```

### Monitoring Setup

#### Prometheus Configuration

The monitoring stack includes:

- **Prometheus** - Metrics collection
- **Grafana** - Visualization dashboards
- **AlertManager** - Alert routing
- **ELK Stack** - Log aggregation (production only)

#### Access URLs

After deployment, access monitoring tools at:

```
Grafana:     https://grafana.your-domain.com
Kibana:      https://kibana.your-domain.com (production only)
Prometheus:  kubectl port-forward svc/prometheus 9090:9090 -n monitoring
```

#### Default Dashboards

Pre-configured dashboards include:

- **System Overview** - CPU, memory, disk, network
- **Application Metrics** - Request rates, response times, errors
- **Security Dashboard** - Security events and threats
- **Business Metrics** - User activity, feature usage

## 🔐 Security Configuration

### Network Security

#### Firewall Rules

Configure security groups/firewall rules:

```bash
# Inbound Rules
Port 80   (HTTP)  → Redirect to HTTPS
Port 443  (HTTPS) → Allow from 0.0.0.0/0
Port 22   (SSH)   → Allow from your IP only

# Outbound Rules
Port 443  (HTTPS) → Allow to 0.0.0.0/0 (for API calls)
Port 53   (DNS)   → Allow to 0.0.0.0/0
```

#### Network Policies

Kubernetes network policies are automatically applied to:

- Restrict pod-to-pod communication
- Allow only necessary ingress traffic
- Block unauthorized egress traffic

### Application Security

#### Security Headers

Configured in Nginx:

```nginx
# Security Headers
add_header X-Frame-Options "SAMEORIGIN";
add_header X-Content-Type-Options "nosniff";
add_header X-XSS-Protection "1; mode=block";
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
add_header Content-Security-Policy "default-src 'self'; ...";
```

#### Rate Limiting

API rate limiting is configured:

```bash
# Rate Limits
General API: 100 requests/minute
Authentication: 5 requests/minute
WebSocket: 1000 messages/minute
```

## 📊 Performance Optimization

### CDN Configuration

#### CloudFront Setup

If using AWS CloudFront:

1. Create distribution pointing to your load balancer
2. Configure caching behaviors
3. Set up custom error pages
4. Enable compression

#### Caching Strategy

```bash
# Cache Headers
Static Assets: 1 year
API Responses: 5 minutes
HTML Pages: 1 hour
```

### Database Optimization

#### Connection Pooling

Configure connection pooling:

```bash
# Database Pool Settings
POOL_SIZE=20
MAX_OVERFLOW=30
POOL_TIMEOUT=30
POOL_RECYCLE=3600
```

#### Query Optimization

- Enable query logging for slow queries
- Set up read replicas for read-heavy workloads
- Configure appropriate indexes

### Caching

#### Redis Configuration

```bash
# Redis Settings
REDIS_MAX_CONNECTIONS=100
REDIS_TIMEOUT=5000
CACHE_TTL=3600
SESSION_TTL=86400
```

## 🚨 Troubleshooting

### Common Issues

#### 1. SSL Certificate Issues

```bash
# Check certificate status
kubectl describe certificate cipherscope-tls -n cipherscope

# Check cert-manager logs
kubectl logs -n cert-manager deployment/cert-manager

# Manually verify certificate
openssl s509 -in ./ssl/cert.pem -text -noout
```

#### 2. Pod Startup Issues

```bash
# Check pod status
kubectl describe pod <pod-name> -n cipherscope

# Check logs
kubectl logs <pod-name> -n cipherscope

# Check events
kubectl get events -n cipherscope --sort-by='.lastTimestamp'
```

#### 3. Database Connection Issues

```bash
# Test database connectivity
kubectl exec -it <pod-name> -n cipherscope -- psql $DATABASE_URL

# Check database logs
kubectl logs deployment/postgres -n cipherscope

# Verify credentials
kubectl get secret cipherscope-frontend-secrets -n cipherscope -o yaml
```

#### 4. Monitoring Issues

```bash
# Check Prometheus targets
kubectl port-forward svc/prometheus 9090:9090 -n monitoring
# Visit http://localhost:9090/targets

# Check Grafana logs
kubectl logs deployment/grafana -n monitoring

# Restart monitoring stack
helm upgrade prometheus-stack prometheus-community/kube-prometheus-stack -n monitoring
```

### Performance Issues

#### High Memory Usage

```bash
# Check resource usage
kubectl top pods -n cipherscope

# Increase memory limits
kubectl patch deployment cipherscope-frontend -n cipherscope -p '{"spec":{"template":{"spec":{"containers":[{"name":"frontend","resources":{"limits":{"memory":"1Gi"}}}]}}}}'

# Enable horizontal pod autoscaling
kubectl autoscale deployment cipherscope-frontend --cpu-percent=70 --min=3 --max=10 -n cipherscope
```

#### Slow Response Times

```bash
# Check application metrics
curl https://your-domain.com/api/metrics | grep response_time

# Scale up replicas
kubectl scale deployment cipherscope-frontend --replicas=5 -n cipherscope

# Check database performance
kubectl exec -it postgres-pod -n cipherscope -- psql -c "SELECT * FROM pg_stat_activity;"
```

## 🔄 Maintenance

### Regular Tasks

#### Daily

- Monitor application health and performance
- Check error rates and response times
- Review security alerts

#### Weekly

- Update dependencies (security patches)
- Review and rotate logs
- Check backup integrity

#### Monthly

- Review and update SSL certificates
- Performance optimization review
- Security audit and updates

### Backup and Recovery

#### Database Backups

```bash
# Manual backup
kubectl exec postgres-pod -n cipherscope -- pg_dump $DATABASE_URL > backup.sql

# Automated backups (configured in Terraform)
# - Daily automated backups
# - 30-day retention
# - Point-in-time recovery enabled
```

#### Application Backups

```bash
# Backup Kubernetes manifests
kubectl get all -n cipherscope -o yaml > cipherscope-backup.yaml

# Backup secrets (encrypted)
kubectl get secrets -n cipherscope -o yaml > secrets-backup.yaml
```

## 📞 Support

### Getting Help

- **Documentation**: Check this guide and DEPLOYMENT.md
- **Logs**: Use `kubectl logs` to check application logs
- **Monitoring**: Check Grafana dashboards for system health
- **Issues**: Create GitHub issues for bugs or feature requests

### Emergency Contacts

- **DevOps Team**: #devops Slack channel
- **On-call Engineer**: Check PagerDuty rotation
- **Security Issues**: <EMAIL>

---

**🎉 Congratulations!** Your CipherScope frontend is now ready for production use with enterprise-grade security, monitoring, and performance optimization.
