#!/bin/bash

# CipherScope Development Environment Setup
set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }

log_info "🚀 Setting up CipherScope Development Environment"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    log_error "Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    log_error "Node.js version 18+ is required. Current version: $(node --version)"
    exit 1
fi

log_success "Node.js version: $(node --version)"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    log_error "npm is not installed."
    exit 1
fi

log_success "npm version: $(npm --version)"

# Install additional dependencies for crypto analytics
log_info "📦 Installing additional dependencies for crypto analytics..."

# Crypto and Web3 libraries
npm install --save \
    ethers \
    web3 \
    @web3-react/core \
    @web3-react/injected-connector \
    @web3-react/walletconnect-connector \
    lightweight-charts \
    ws \
    axios \
    socket.io-client

# Development and debugging tools
npm install --save-dev \
    @types/ws \
    @types/node \
    concurrently \
    nodemon \
    cross-env

log_success "Dependencies installed"

# Create development configuration files
log_info "📝 Creating development configuration files..."

# Create vite.config.ts with proper environment variable handling
cat > vite.config.ts << 'EOF'
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    port: 5173,
    host: true,
    open: true,
    cors: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
      },
      '/ws': {
        target: 'ws://localhost:8000',
        ws: true,
        changeOrigin: true,
      }
    }
  },
  define: {
    global: 'globalThis',
  },
  optimizeDeps: {
    include: ['ethers', 'web3', 'lightweight-charts']
  },
  build: {
    rollupOptions: {
      external: [],
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          crypto: ['ethers', 'web3'],
          charts: ['lightweight-charts', 'recharts']
        }
      }
    }
  }
})
EOF

log_success "Created vite.config.ts"

# Create environment type definitions
mkdir -p src/types
cat > src/types/env.d.ts << 'EOF'
/// <reference types="vite/client" />

interface ImportMetaEnv {
  // Application
  readonly VITE_APP_NAME: string
  readonly VITE_APP_VERSION: string
  readonly VITE_ENVIRONMENT: string
  readonly VITE_APP_URL: string
  readonly VITE_API_URL: string
  readonly VITE_WS_URL: string
  
  // Blockchain & Web3
  readonly VITE_INFURA_API_KEY: string
  readonly VITE_WEB3_PROVIDER_URL: string
  readonly VITE_ETHEREUM_RPC_URL: string
  readonly VITE_POLYGON_RPC_URL: string
  readonly VITE_BSC_RPC_URL: string
  
  // API Keys
  readonly VITE_COINGECKO_API_KEY: string
  readonly VITE_COIN_API_KEY: string
  readonly VITE_ETHERSCAN_API_KEY: string
  readonly VITE_DUNE_API_KEY: string
  readonly VITE_BIRDEYE_API_KEY: string
  
  // WebSocket URLs
  readonly VITE_INFURA_MAINNET_WS: string
  readonly VITE_INFURA_SEPOLIA_WS: string
  readonly VITE_INFURA_POLYGON_MAINNET_WS: string
  readonly VITE_INFURA_BASE_MAINNET_WS: string
  readonly VITE_INFURA_ARBITRUM_MAINNET_WS: string
  readonly VITE_INFURA_OPTIMISM_MAINNET_WS: string
  
  // Feature Flags
  readonly VITE_ENABLE_REAL_TIME: string
  readonly VITE_ENABLE_ADVANCED_CHARTS: string
  readonly VITE_ENABLE_NOTIFICATIONS: string
  readonly VITE_ENABLE_ANALYTICS: string
  readonly VITE_ENABLE_TRADING: string
  readonly VITE_ENABLE_SECURITY_MONITORING: string
  readonly VITE_ENABLE_PERFORMANCE_MONITORING: string
  
  // Debug
  readonly VITE_DEBUG: string
  readonly VITE_LOG_LEVEL: string
  readonly VITE_ENABLE_DEVTOOLS: string
  readonly VITE_ENABLE_DEBUG_PANEL: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
EOF

log_success "Created environment type definitions"

# Create development utilities
mkdir -p src/lib
cat > src/lib/env.ts << 'EOF'
// Environment configuration utility
export const env = {
  // Application
  APP_NAME: import.meta.env.VITE_APP_NAME || 'CipherScope',
  APP_VERSION: import.meta.env.VITE_APP_VERSION || '1.0.0',
  ENVIRONMENT: import.meta.env.VITE_ENVIRONMENT || 'development',
  APP_URL: import.meta.env.VITE_APP_URL || 'http://localhost:5173',
  API_URL: import.meta.env.VITE_API_URL || 'http://localhost:8000',
  WS_URL: import.meta.env.VITE_WS_URL || 'ws://localhost:8000/ws',
  
  // Blockchain & Web3
  INFURA_API_KEY: import.meta.env.VITE_INFURA_API_KEY,
  WEB3_PROVIDER_URL: import.meta.env.VITE_WEB3_PROVIDER_URL,
  ETHEREUM_RPC_URL: import.meta.env.VITE_ETHEREUM_RPC_URL,
  POLYGON_RPC_URL: import.meta.env.VITE_POLYGON_RPC_URL,
  BSC_RPC_URL: import.meta.env.VITE_BSC_RPC_URL,
  
  // API Keys
  COINGECKO_API_KEY: import.meta.env.VITE_COINGECKO_API_KEY,
  COIN_API_KEY: import.meta.env.VITE_COIN_API_KEY,
  ETHERSCAN_API_KEY: import.meta.env.VITE_ETHERSCAN_API_KEY,
  DUNE_API_KEY: import.meta.env.VITE_DUNE_API_KEY,
  BIRDEYE_API_KEY: import.meta.env.VITE_BIRDEYE_API_KEY,
  
  // WebSocket URLs
  INFURA_MAINNET_WS: import.meta.env.VITE_INFURA_MAINNET_WS,
  INFURA_SEPOLIA_WS: import.meta.env.VITE_INFURA_SEPOLIA_WS,
  INFURA_POLYGON_MAINNET_WS: import.meta.env.VITE_INFURA_POLYGON_MAINNET_WS,
  INFURA_BASE_MAINNET_WS: import.meta.env.VITE_INFURA_BASE_MAINNET_WS,
  INFURA_ARBITRUM_MAINNET_WS: import.meta.env.VITE_INFURA_ARBITRUM_MAINNET_WS,
  INFURA_OPTIMISM_MAINNET_WS: import.meta.env.VITE_INFURA_OPTIMISM_MAINNET_WS,
  
  // Feature Flags
  ENABLE_REAL_TIME: import.meta.env.VITE_ENABLE_REAL_TIME === 'true',
  ENABLE_ADVANCED_CHARTS: import.meta.env.VITE_ENABLE_ADVANCED_CHARTS === 'true',
  ENABLE_NOTIFICATIONS: import.meta.env.VITE_ENABLE_NOTIFICATIONS === 'true',
  ENABLE_ANALYTICS: import.meta.env.VITE_ENABLE_ANALYTICS === 'true',
  ENABLE_TRADING: import.meta.env.VITE_ENABLE_TRADING === 'true',
  ENABLE_SECURITY_MONITORING: import.meta.env.VITE_ENABLE_SECURITY_MONITORING === 'true',
  ENABLE_PERFORMANCE_MONITORING: import.meta.env.VITE_ENABLE_PERFORMANCE_MONITORING === 'true',
  
  // Debug
  DEBUG: import.meta.env.VITE_DEBUG === 'true',
  LOG_LEVEL: import.meta.env.VITE_LOG_LEVEL || 'INFO',
  ENABLE_DEVTOOLS: import.meta.env.VITE_ENABLE_DEVTOOLS === 'true',
  ENABLE_DEBUG_PANEL: import.meta.env.VITE_ENABLE_DEBUG_PANEL === 'true',
  
  // Computed properties
  get isDevelopment() {
    return this.ENVIRONMENT === 'development'
  },
  
  get isProduction() {
    return this.ENVIRONMENT === 'production'
  },
  
  get isDebugMode() {
    return this.DEBUG || this.isDevelopment
  }
}

// Validation function
export function validateEnvironment() {
  const required = [
    'VITE_COINGECKO_API_KEY',
    'VITE_INFURA_API_KEY',
    'VITE_WEB3_PROVIDER_URL'
  ]
  
  const missing = required.filter(key => !import.meta.env[key])
  
  if (missing.length > 0) {
    console.error('Missing required environment variables:', missing)
    return false
  }
  
  return true
}

// Debug logging
export function debugLog(message: string, data?: any) {
  if (env.isDebugMode) {
    console.log(`[${env.APP_NAME}] ${message}`, data || '')
  }
}
EOF

log_success "Created environment utilities"

# Update package.json scripts
log_info "📝 Updating package.json scripts..."

# Create a temporary script to update package.json
cat > update_package.js << 'EOF'
const fs = require('fs');
const path = require('path');

const packagePath = path.join(__dirname, 'package.json');
const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));

// Update scripts
packageJson.scripts = {
  ...packageJson.scripts,
  "dev": "cross-env NODE_ENV=development vite",
  "dev:debug": "cross-env NODE_ENV=development VITE_DEBUG=true vite",
  "build": "cross-env NODE_ENV=production tsc -b && vite build",
  "build:dev": "cross-env NODE_ENV=development tsc -b && vite build",
  "preview": "vite preview",
  "lint": "eslint .",
  "lint:fix": "eslint . --fix",
  "type-check": "tsc --noEmit",
  "test": "echo \"No tests specified yet\"",
  "start:full": "concurrently \"npm run dev\" \"npm run mock-backend\"",
  "mock-backend": "node scripts/mock-backend.js"
};

fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
console.log('✅ Updated package.json scripts');
EOF

node update_package.js
rm update_package.js

log_success "Updated package.json scripts"

# Create mock backend for development
mkdir -p scripts
cat > scripts/mock-backend.js << 'EOF'
const http = require('http');
const WebSocket = require('ws');

// Simple HTTP server for API endpoints
const server = http.createServer((req, res) => {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  const url = new URL(req.url, `http://${req.headers.host}`);
  
  // Mock API endpoints
  if (url.pathname === '/api/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ status: 'healthy', timestamp: new Date().toISOString() }));
  } else if (url.pathname === '/api/tokens') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      data: [
        { id: 'bitcoin', symbol: 'BTC', name: 'Bitcoin', current_price: 45000, market_cap: 850000000000 },
        { id: 'ethereum', symbol: 'ETH', name: 'Ethereum', current_price: 3000, market_cap: 360000000000 }
      ]
    }));
  } else {
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ error: 'Not found' }));
  }
});

// WebSocket server for real-time data
const wss = new WebSocket.Server({ server, path: '/ws' });

wss.on('connection', (ws) => {
  console.log('WebSocket client connected');
  
  // Send mock price updates every 5 seconds
  const interval = setInterval(() => {
    const mockData = {
      type: 'price_update',
      data: {
        symbol: 'BTCUSDT',
        price: 45000 + (Math.random() - 0.5) * 1000,
        timestamp: Date.now()
      }
    };
    
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(mockData));
    }
  }, 5000);
  
  ws.on('close', () => {
    console.log('WebSocket client disconnected');
    clearInterval(interval);
  });
  
  ws.on('message', (message) => {
    console.log('Received:', message.toString());
  });
});

const PORT = 8000;
server.listen(PORT, () => {
  console.log(`🚀 Mock backend server running on http://localhost:${PORT}`);
  console.log(`📡 WebSocket server running on ws://localhost:${PORT}/ws`);
});
EOF

log_success "Created mock backend server"

# Create development debugging utilities
mkdir -p src/components/debug
cat > src/components/debug/DebugPanel.tsx << 'EOF'
import React, { useState } from 'react';
import { env } from '@/lib/env';

export function DebugPanel() {
  const [isOpen, setIsOpen] = useState(false);
  
  if (!env.ENABLE_DEBUG_PANEL) {
    return null;
  }
  
  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-blue-700"
      >
        🐛 Debug
      </button>
      
      {isOpen && (
        <div className="absolute bottom-12 right-0 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-xl p-4 w-80 max-h-96 overflow-y-auto">
          <h3 className="font-bold mb-2">Debug Information</h3>
          
          <div className="space-y-2 text-sm">
            <div>
              <strong>Environment:</strong> {env.ENVIRONMENT}
            </div>
            <div>
              <strong>Debug Mode:</strong> {env.isDebugMode ? 'ON' : 'OFF'}
            </div>
            <div>
              <strong>API URL:</strong> {env.API_URL}
            </div>
            <div>
              <strong>WebSocket URL:</strong> {env.WS_URL}
            </div>
            <div>
              <strong>CoinGecko API:</strong> {env.COINGECKO_API_KEY ? '✅ Set' : '❌ Missing'}
            </div>
            <div>
              <strong>Infura API:</strong> {env.INFURA_API_KEY ? '✅ Set' : '❌ Missing'}
            </div>
            
            <hr className="my-2" />
            
            <div>
              <strong>Feature Flags:</strong>
              <ul className="ml-4 mt-1">
                <li>Real-time: {env.ENABLE_REAL_TIME ? '✅' : '❌'}</li>
                <li>Charts: {env.ENABLE_ADVANCED_CHARTS ? '✅' : '❌'}</li>
                <li>Trading: {env.ENABLE_TRADING ? '✅' : '❌'}</li>
                <li>Security: {env.ENABLE_SECURITY_MONITORING ? '✅' : '❌'}</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
EOF

log_success "Created debug panel component"

# Copy development environment file
if [ ! -f .env.local ]; then
    cp .env.development .env.local
    log_success "Created .env.local from development template"
else
    log_warning ".env.local already exists, skipping copy"
fi

# Create data directory for local database
mkdir -p data

log_success "🎉 Development environment setup completed!"
echo
log_info "📋 Next steps:"
echo "1. Review and customize .env.local with your API keys"
echo "2. Start development server: npm run dev"
echo "3. Start with mock backend: npm run start:full"
echo "4. Enable debug mode: npm run dev:debug"
echo
log_info "🔗 Development URLs:"
echo "Frontend: http://localhost:5173"
echo "Mock Backend: http://localhost:8000"
echo "WebSocket: ws://localhost:8000/ws"
echo
log_info "🐛 Debug features:"
echo "- Debug panel in bottom-right corner (when enabled)"
echo "- Console logging for API calls and WebSocket events"
echo "- Environment variable validation"
echo "- Hot reload enabled"
EOF
