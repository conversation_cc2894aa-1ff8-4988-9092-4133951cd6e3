#!/bin/bash

# CipherScope Development Environment Setup
set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }

log_info "🚀 Setting up CipherScope Development Environment"

# Check if this is a React/Vite project
if [ ! -f "package.json" ]; then
    log_error "package.json not found. Are you in the right directory?"
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    log_error "Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

log_success "Node.js version: $(node --version)"

# Install additional dependencies for crypto analytics
log_info "📦 Installing additional dependencies for crypto analytics..."

# Check if npm install is needed
if [ ! -d "node_modules" ]; then
    log_info "Installing existing dependencies..."
    npm install
fi

# Install crypto and Web3 libraries
npm install --save \
    ethers \
    web3 \
    lightweight-charts \
    ws \
    axios \
    socket.io-client

# Install development tools
npm install --save-dev \
    @types/ws \
    concurrently \
    cross-env

log_success "Dependencies installed"

# Create development environment file if it doesn't exist
if [ ! -f ".env.development" ]; then
    log_info "📝 Creating .env.development file..."
    
    cat > .env.development << 'ENV_END'
# CipherScope Development Environment
NODE_ENV=development
VITE_ENVIRONMENT=development
VITE_APP_NAME=CipherScope
VITE_APP_VERSION=1.0.0

# Application URLs (Development)
VITE_APP_URL=http://localhost:5173
VITE_API_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000/ws

# Copy your API keys from .env file
VITE_COINGECKO_API_KEY=CG-wJx8SHeFD1KjG3F73LB14967
VITE_INFURA_API_KEY=********************************
VITE_WEB3_PROVIDER_URL=https://eth-mainnet.g.alchemy.com/v2/BwYx2Nu9Fo8TBdqPmMw18bIz_MWdsQP-
VITE_ETHERSCAN_API_KEY=**********************************
VITE_DUNE_API_KEY=8L1zlFUtMWg4FCMsZuFkKIkXbRpPYefv

# WebSocket URLs
VITE_INFURA_MAINNET_WS=wss://mainnet.infura.io/ws/v3/********************************

# Feature Flags
VITE_ENABLE_REAL_TIME=true
VITE_ENABLE_ADVANCED_CHARTS=true
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_TRADING=true
VITE_ENABLE_SECURITY_MONITORING=true

# Debug
VITE_DEBUG=true
VITE_LOG_LEVEL=DEBUG
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_DEBUG_PANEL=true
ENV_END

    log_success "Created .env.development"
fi

# Copy to .env.local if it doesn't exist
if [ ! -f ".env.local" ]; then
    cp .env.development .env.local
    log_success "Created .env.local from development template"
fi

# Create mock backend server
mkdir -p scripts
cat > scripts/mock-backend.js << 'BACKEND_END'
const http = require('http');

// Simple HTTP server for API endpoints
const server = http.createServer((req, res) => {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  const url = new URL(req.url, `http://${req.headers.host}`);
  
  // Mock API endpoints
  if (url.pathname === '/api/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ 
      status: 'healthy', 
      timestamp: new Date().toISOString(),
      environment: 'development'
    }));
  } else if (url.pathname === '/api/tokens') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      data: [
        { 
          id: 'bitcoin', 
          symbol: 'BTC', 
          name: 'Bitcoin', 
          current_price: 45000 + (Math.random() - 0.5) * 2000, 
          market_cap: 850000000000,
          price_change_percentage_24h: (Math.random() - 0.5) * 10
        },
        { 
          id: 'ethereum', 
          symbol: 'ETH', 
          name: 'Ethereum', 
          current_price: 3000 + (Math.random() - 0.5) * 400, 
          market_cap: 360000000000,
          price_change_percentage_24h: (Math.random() - 0.5) * 8
        }
      ]
    }));
  } else if (url.pathname === '/api/chart-data') {
    // Mock chart data
    const data = [];
    const now = Date.now();
    for (let i = 100; i >= 0; i--) {
      const time = now - (i * 60000); // 1 minute intervals
      const price = 45000 + Math.sin(i / 10) * 2000 + (Math.random() - 0.5) * 500;
      data.push({
        time: Math.floor(time / 1000),
        open: price,
        high: price + Math.random() * 200,
        low: price - Math.random() * 200,
        close: price + (Math.random() - 0.5) * 100,
        volume: Math.random() * 1000000
      });
    }
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ data }));
  } else {
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ error: 'Not found' }));
  }
});

const PORT = 8000;
server.listen(PORT, () => {
  console.log(`🚀 Mock backend server running on http://localhost:${PORT}`);
  console.log(`📊 Available endpoints:`);
  console.log(`   GET /api/health - Health check`);
  console.log(`   GET /api/tokens - Token prices`);
  console.log(`   GET /api/chart-data - Chart data`);
});
BACKEND_END

log_success "Created mock backend server"

# Update package.json scripts
log_info "📝 Updating package.json scripts..."

# Create a simple script updater
node -e "
const fs = require('fs');
const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
pkg.scripts = {
  ...pkg.scripts,
  'dev': 'vite',
  'dev:debug': 'cross-env VITE_DEBUG=true vite',
  'start:full': 'concurrently \"npm run dev\" \"npm run mock-backend\"',
  'mock-backend': 'node scripts/mock-backend.js'
};
fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2));
console.log('✅ Updated package.json scripts');
"

log_success "Updated package.json scripts"

# Create basic environment utilities
mkdir -p src/lib
cat > src/lib/env.ts << 'ENV_UTIL_END'
// Environment configuration utility
export const env = {
  // Application
  APP_NAME: import.meta.env.VITE_APP_NAME || 'CipherScope',
  APP_VERSION: import.meta.env.VITE_APP_VERSION || '1.0.0',
  ENVIRONMENT: import.meta.env.VITE_ENVIRONMENT || 'development',
  APP_URL: import.meta.env.VITE_APP_URL || 'http://localhost:5173',
  API_URL: import.meta.env.VITE_API_URL || 'http://localhost:8000',
  WS_URL: import.meta.env.VITE_WS_URL || 'ws://localhost:8000/ws',
  
  // API Keys
  COINGECKO_API_KEY: import.meta.env.VITE_COINGECKO_API_KEY,
  INFURA_API_KEY: import.meta.env.VITE_INFURA_API_KEY,
  WEB3_PROVIDER_URL: import.meta.env.VITE_WEB3_PROVIDER_URL,
  ETHERSCAN_API_KEY: import.meta.env.VITE_ETHERSCAN_API_KEY,
  DUNE_API_KEY: import.meta.env.VITE_DUNE_API_KEY,
  
  // Feature Flags
  ENABLE_REAL_TIME: import.meta.env.VITE_ENABLE_REAL_TIME === 'true',
  ENABLE_ADVANCED_CHARTS: import.meta.env.VITE_ENABLE_ADVANCED_CHARTS === 'true',
  ENABLE_NOTIFICATIONS: import.meta.env.VITE_ENABLE_NOTIFICATIONS === 'true',
  ENABLE_ANALYTICS: import.meta.env.VITE_ENABLE_ANALYTICS === 'true',
  ENABLE_TRADING: import.meta.env.VITE_ENABLE_TRADING === 'true',
  ENABLE_SECURITY_MONITORING: import.meta.env.VITE_ENABLE_SECURITY_MONITORING === 'true',
  
  // Debug
  DEBUG: import.meta.env.VITE_DEBUG === 'true',
  LOG_LEVEL: import.meta.env.VITE_LOG_LEVEL || 'INFO',
  ENABLE_DEVTOOLS: import.meta.env.VITE_ENABLE_DEVTOOLS === 'true',
  ENABLE_DEBUG_PANEL: import.meta.env.VITE_ENABLE_DEBUG_PANEL === 'true',
  
  // Computed properties
  get isDevelopment() {
    return this.ENVIRONMENT === 'development'
  },
  
  get isProduction() {
    return this.ENVIRONMENT === 'production'
  },
  
  get isDebugMode() {
    return this.DEBUG || this.isDevelopment
  }
}

// Debug logging
export function debugLog(message: string, data?: any) {
  if (env.isDebugMode) {
    console.log(`[${env.APP_NAME}] ${message}`, data || '')
  }
}

// Validation function
export function validateEnvironment() {
  const required = ['VITE_COINGECKO_API_KEY', 'VITE_INFURA_API_KEY']
  const missing = required.filter(key => !import.meta.env[key])
  
  if (missing.length > 0) {
    console.error('Missing required environment variables:', missing)
    return false
  }
  
  return true
}
ENV_UTIL_END

log_success "Created environment utilities"

log_success "🎉 Development environment setup completed!"
echo
log_info "📋 Next steps:"
echo "1. Review .env.local and customize if needed"
echo "2. Start development server: npm run dev"
echo "3. Start with mock backend: npm run start:full"
echo "4. Enable debug mode: npm run dev:debug"
echo
log_info "🔗 Development URLs:"
echo "Frontend: http://localhost:5173"
echo "Mock Backend: http://localhost:8000"
echo
log_info "🐛 Debug features:"
echo "- Console logging enabled in development"
echo "- Environment variable validation"
echo "- Hot reload enabled"
echo "- Mock API endpoints for testing"
