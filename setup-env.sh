#!/bin/bash

# Simple CipherScope Environment Setup
set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Parse arguments
DOMAIN="$1"
EMAIL="$2"

if [[ -z "$DOMAIN" ]]; then
    echo "Usage: $0 <domain> <email>"
    echo "Example: $0 cipherscope.com <EMAIL>"
    exit 1
fi

log_info "Setting up environment for domain: $DOMAIN"

# Generate secure secrets
generate_secret() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-32
}

NEXTAUTH_SECRET=$(generate_secret)
JWT_SECRET=$(generate_secret)
SESSION_SECRET=$(generate_secret)
DB_PASSWORD=$(generate_secret)

# Create .env.production file
cat > .env.production << EOF
# CipherScope Production Environment

# Application URLs
NEXT_PUBLIC_APP_URL=https://$DOMAIN
NEXT_PUBLIC_API_URL=https://api.$DOMAIN
NEXT_PUBLIC_WS_URL=wss://api.$DOMAIN/ws

# Environment
NODE_ENV=production
NEXT_PUBLIC_ENVIRONMENT=production

# Security - Generated secure secrets
NEXTAUTH_URL=https://$DOMAIN
NEXTAUTH_SECRET=$NEXTAUTH_SECRET
JWT_SECRET=$JWT_SECRET
SESSION_SECRET=$SESSION_SECRET

# Features
NEXT_PUBLIC_ENABLE_REAL_TIME=true
NEXT_PUBLIC_ENABLE_ADVANCED_CHARTS=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_DEBUG=false

# Database - UPDATE AFTER TERRAFORM DEPLOYMENT
DATABASE_URL=postgresql://cipherscope:$<EMAIL>:5432/cipherscope

# Redis - UPDATE AFTER TERRAFORM DEPLOYMENT  
REDIS_URL=redis://your-elasticache-endpoint.cache.amazonaws.com:6379/0

# API Keys - ADD YOUR ACTUAL KEYS
NEXT_PUBLIC_COINGECKO_API_KEY=your-coingecko-api-key
NEXT_PUBLIC_BINANCE_API_KEY=your-binance-api-key
NEXT_PUBLIC_POLYGON_API_KEY=your-polygon-api-key

# Blockchain RPC - ADD YOUR INFURA PROJECT ID
NEXT_PUBLIC_ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/YOUR_INFURA_PROJECT_ID
NEXT_PUBLIC_POLYGON_RPC_URL=https://polygon-mainnet.infura.io/v3/YOUR_INFURA_PROJECT_ID

# Analytics - ADD YOUR TRACKING IDs
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id

# AWS Configuration
AWS_REGION=us-west-2
AWS_S3_BUCKET=cipherscope-assets-production

# Monitoring
METRICS_ENABLED=true
HEALTH_CHECK_ENABLED=true
LOG_LEVEL=info

# Security
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
CORS_ORIGINS=https://$DOMAIN,https://www.$DOMAIN

# SSL/TLS
SSL_CERT_PATH=/etc/ssl/certs/cert.pem
SSL_KEY_PATH=/etc/ssl/private/key.pem
HSTS_MAX_AGE=31536000
HSTS_INCLUDE_SUBDOMAINS=true
EOF

log_success "Created .env.production file"

# Create Terraform variables file
mkdir -p terraform/environments
cat > terraform/environments/production.tfvars << EOF
# CipherScope Production Terraform Variables

aws_region = "us-west-2"
environment = "production"
project_name = "cipherscope"

# VPC Configuration
vpc_cidr = "10.0.0.0/16"
private_subnet_cidrs = ["********/24", "********/24", "********/24"]
public_subnet_cidrs = ["**********/24", "**********/24", "**********/24"]

# EKS Configuration
kubernetes_version = "1.28"
node_instance_types = ["t3.large"]
node_group_min_size = 3
node_group_max_size = 10
node_group_desired_size = 3

# Database Configuration
db_instance_class = "db.t3.medium"
db_allocated_storage = 100
db_max_allocated_storage = 500
db_name = "cipherscope"
db_username = "cipherscope"
# Set TF_VAR_db_password environment variable

# Redis Configuration
redis_node_type = "cache.t3.medium"
redis_num_cache_nodes = 2

# Domain Configuration
domain_name = "$DOMAIN"
# Set TF_VAR_certificate_arn after creating SSL certificate

# Security
enable_waf = true
enable_monitoring = true
log_retention_days = 30

# Scaling
enable_cluster_autoscaler = true
enable_horizontal_pod_autoscaler = true
EOF

log_success "Created Terraform variables file"

# Create simple SSL setup script
cat > setup-ssl.sh << 'EOF'
#!/bin/bash

DOMAIN="$1"
if [[ -z "$DOMAIN" ]]; then
    echo "Usage: $0 <domain>"
    exit 1
fi

echo "Setting up SSL certificate for $DOMAIN"

# Check if AWS CLI is available
if command -v aws &> /dev/null; then
    echo "Requesting AWS ACM certificate..."
    CERT_ARN=$(aws acm request-certificate \
        --domain-name "$DOMAIN" \
        --subject-alternative-names "www.$DOMAIN" "api.$DOMAIN" "*.$DOMAIN" \
        --validation-method DNS \
        --query 'CertificateArn' \
        --output text)
    
    if [[ -n "$CERT_ARN" ]]; then
        echo "Certificate requested: $CERT_ARN"
        echo "export TF_VAR_certificate_arn=\"$CERT_ARN\"" > .env.ssl
        echo "Add DNS validation records in AWS Console"
        echo "Then run: source .env.ssl"
    fi
else
    echo "AWS CLI not found. Install it first:"
    echo "https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html"
fi
EOF

chmod +x setup-ssl.sh
log_success "Created SSL setup script"

# Create deployment script
cat > deploy.sh << 'EOF'
#!/bin/bash

set -e

ENVIRONMENT="${1:-production}"

echo "Deploying CipherScope to $ENVIRONMENT"

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo "kubectl not found. Install it first."
    exit 1
fi

# Create namespace
kubectl create namespace cipherscope --dry-run=client -o yaml | kubectl apply -f -

# Create secrets from environment file
if [[ -f ".env.$ENVIRONMENT" ]]; then
    kubectl create secret generic cipherscope-frontend-secrets \
        --from-env-file=".env.$ENVIRONMENT" \
        --namespace=cipherscope \
        --dry-run=client -o yaml | kubectl apply -f -
    echo "Secrets created"
fi

# Apply Kubernetes manifests if they exist
if [[ -d "k8s" ]]; then
    kubectl apply -f k8s/ --namespace=cipherscope
    echo "Kubernetes manifests applied"
fi

echo "Deployment completed"
echo "Check status: kubectl get pods -n cipherscope"
EOF

chmod +x deploy.sh
log_success "Created deployment script"

# Export Terraform variables
export TF_VAR_db_password="$DB_PASSWORD"
export TF_VAR_domain_name="$DOMAIN"

log_success "Environment setup completed!"
echo
echo "Generated files:"
echo "  .env.production - Environment variables"
echo "  terraform/environments/production.tfvars - Terraform variables"
echo "  setup-ssl.sh - SSL certificate setup"
echo "  deploy.sh - Deployment script"
echo
echo "Generated secrets:"
echo "  Database password: $DB_PASSWORD"
echo "  NextAuth secret: $NEXTAUTH_SECRET"
echo
echo "Next steps:"
echo "1. Review and customize .env.production"
echo "2. Add your actual API keys"
echo "3. Set up SSL: ./setup-ssl.sh $DOMAIN"
echo "4. Deploy infrastructure with Terraform"
echo "5. Update database/Redis URLs in .env.production"
echo "6. Deploy application: ./deploy.sh"
EOF
