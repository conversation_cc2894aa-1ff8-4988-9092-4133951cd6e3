#!/bin/bash

# Simple CipherScope Environment Setup
set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Parse arguments
DOMAIN="$1"
EMAIL="$2"

if [[ -z "$DOMAIN" ]]; then
    echo "Usage: $0 <domain> <email>"
    echo "Example: $0 cipherscope.com <EMAIL>"
    exit 1
fi

log_info "Setting up environment for domain: $DOMAIN"

# Generate secure secrets
generate_secret() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-32
}

NEXTAUTH_SECRET=$(generate_secret)
JWT_SECRET=$(generate_secret)
SESSION_SECRET=$(generate_secret)
DB_PASSWORD=$(generate_secret)

# Create .env.production file
cat > .env.production << EOF
# CipherScope Production Environment

# Application URLs
NEXT_PUBLIC_APP_URL=https://$DOMAIN
NEXT_PUBLIC_API_URL=https://api.$DOMAIN
NEXT_PUBLIC_WS_URL=wss://api.$DOMAIN/ws

# Environment
NODE_ENV=production
NEXT_PUBLIC_ENVIRONMENT=production

# Security - Generated secure secrets
NEXTAUTH_URL=https://$DOMAIN
NEXTAUTH_SECRET=$NEXTAUTH_SECRET
JWT_SECRET=$JWT_SECRET
SESSION_SECRET=$SESSION_SECRET

# Features
NEXT_PUBLIC_ENABLE_REAL_TIME=true
NEXT_PUBLIC_ENABLE_ADVANCED_CHARTS=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_DEBUG=false

# Database - UPDATE AFTER TERRAFORM DEPLOYMENT
DATABASE_URL=postgresql://cipherscope:$<EMAIL>:5432/cipherscope

# Redis - UPDATE AFTER TERRAFORM DEPLOYMENT  
REDIS_URL=redis://your-elasticache-endpoint.cache.amazonaws.com:6379/0

# API Keys - ADD YOUR ACTUAL KEYS
NEXT_PUBLIC_COINGECKO_API_KEY=your-coingecko-api-key
NEXT_PUBLIC_BINANCE_API_KEY=your-binance-api-key
NEXT_PUBLIC_POLYGON_API_KEY=your-polygon-api-key

# Blockchain RPC - ADD YOUR INFURA PROJECT ID
NEXT_PUBLIC_ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/YOUR_INFURA_PROJECT_ID
NEXT_PUBLIC_POLYGON_RPC_URL=https://polygon-mainnet.infura.io/v3/YOUR_INFURA_PROJECT_ID

# Analytics - ADD YOUR TRACKING IDs
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id

# AWS Configuration
AWS_REGION=us-west-2
AWS_S3_BUCKET=cipherscope-assets-production

# Monitoring
METRICS_ENABLED=true
HEALTH_CHECK_ENABLED=true
LOG_LEVEL=info

# Security
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
CORS_ORIGINS=https://$DOMAIN,https://www.$DOMAIN
EOF

log_success "Created .env.production file"

# Export Terraform variables
export TF_VAR_db_password="$DB_PASSWORD"
export TF_VAR_domain_name="$DOMAIN"

log_success "Environment setup completed!"
echo
echo "Generated files:"
echo "  .env.production - Environment variables"
echo
echo "Generated secrets:"
echo "  Database password: $DB_PASSWORD"
echo "  NextAuth secret: $NEXTAUTH_SECRET"
echo
echo "Next steps:"
echo "1. Review and customize .env.production"
echo "2. Add your actual API keys"
echo "3. Set up SSL certificate"
echo "4. Deploy infrastructure with Terraform"
echo "5. Update database/Redis URLs in .env.production"
