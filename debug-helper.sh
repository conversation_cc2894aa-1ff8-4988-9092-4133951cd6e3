#!/bin/bash

# CipherScope Debug Helper Script
set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }

show_usage() {
    echo "CipherScope Debug Helper"
    echo
    echo "Usage: $0 [command]"
    echo
    echo "Commands:"
    echo "  env          - Check environment variables"
    echo "  deps         - Check dependencies"
    echo "  api          - Test API connections"
    echo "  backend      - Test backend connectivity"
    echo "  frontend     - Test frontend build"
    echo "  logs         - Show recent logs"
    echo "  health       - Full health check"
    echo "  fix          - Attempt to fix common issues"
    echo "  clean        - Clean and reinstall"
    echo
    echo "Examples:"
    echo "  $0 health    - Run full health check"
    echo "  $0 api       - Test API connections"
    echo "  $0 fix       - Fix common issues"
}

check_environment() {
    log_info "🔍 Checking environment variables..."
    
    if [ ! -f ".env.local" ]; then
        log_error "❌ .env.local file missing"
        echo "Run ./dev-setup.sh to create it"
        return 1
    fi
    
    log_success "✅ .env.local file exists"
    
    # Check required variables
    local required_vars=(
        "VITE_COINGECKO_API_KEY"
        "VITE_INFURA_API_KEY"
        "VITE_APP_URL"
        "VITE_API_URL"
    )
    
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^$var=" .env.local; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -eq 0 ]; then
        log_success "✅ All required environment variables present"
    else
        log_error "❌ Missing environment variables:"
        for var in "${missing_vars[@]}"; do
            echo "   - $var"
        done
        return 1
    fi
    
    # Show current configuration
    echo
    log_info "📋 Current Configuration:"
    echo "   App URL: $(grep VITE_APP_URL .env.local | cut -d'=' -f2)"
    echo "   API URL: $(grep VITE_API_URL .env.local | cut -d'=' -f2)"
    echo "   Environment: $(grep VITE_ENVIRONMENT .env.local | cut -d'=' -f2 || echo 'development')"
    echo "   Debug Mode: $(grep VITE_DEBUG .env.local | cut -d'=' -f2 || echo 'false')"
}

check_dependencies() {
    log_info "📦 Checking dependencies..."
    
    if [ ! -f "package.json" ]; then
        log_error "❌ package.json not found"
        return 1
    fi
    
    if [ ! -d "node_modules" ]; then
        log_warning "⚠️ node_modules not found, installing..."
        npm install
    fi
    
    # Check key dependencies
    local key_deps=(
        "react"
        "typescript"
        "vite"
        "ethers"
        "lightweight-charts"
    )
    
    for dep in "${key_deps[@]}"; do
        if [ -d "node_modules/$dep" ]; then
            log_success "✅ $dep installed"
        else
            log_warning "⚠️ $dep missing, installing..."
            npm install "$dep"
        fi
    done
}

test_api_connections() {
    log_info "🌐 Testing API connections..."
    
    # Test CoinGecko API
    log_info "Testing CoinGecko API..."
    if curl -s --max-time 10 "https://api.coingecko.com/api/v3/ping" > /dev/null; then
        log_success "✅ CoinGecko API accessible"
    else
        log_error "❌ CoinGecko API not accessible"
    fi
    
    # Test Infura API
    local infura_key=$(grep "VITE_INFURA_API_KEY" .env.local 2>/dev/null | cut -d'=' -f2)
    if [ ! -z "$infura_key" ]; then
        log_info "Testing Infura API..."
        local response=$(curl -s --max-time 10 \
            -X POST \
            -H "Content-Type: application/json" \
            -d '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' \
            "https://mainnet.infura.io/v3/$infura_key")
        
        if echo "$response" | grep -q "result"; then
            log_success "✅ Infura API accessible"
        else
            log_error "❌ Infura API not accessible"
            echo "Response: $response"
        fi
    else
        log_warning "⚠️ Infura API key not configured"
    fi
    
    # Test general internet connectivity
    log_info "Testing internet connectivity..."
    if ping -c 1 google.com > /dev/null 2>&1; then
        log_success "✅ Internet connectivity OK"
    else
        log_error "❌ No internet connectivity"
    fi
}

test_backend() {
    log_info "🖥️ Testing backend connectivity..."
    
    local api_url=$(grep "VITE_API_URL" .env.local 2>/dev/null | cut -d'=' -f2 || echo "http://localhost:8000")
    
    log_info "Testing backend at: $api_url"
    
    # Test health endpoint
    if curl -s --max-time 5 "$api_url/api/health" > /dev/null; then
        log_success "✅ Backend health check passed"
    else
        log_warning "⚠️ Backend not responding, starting mock backend..."
        
        if [ -f "scripts/mock-backend.js" ]; then
            node scripts/mock-backend.js &
            local backend_pid=$!
            sleep 3
            
            if curl -s --max-time 5 "http://localhost:8000/api/health" > /dev/null; then
                log_success "✅ Mock backend started successfully"
                kill $backend_pid 2>/dev/null || true
            else
                log_error "❌ Mock backend failed to start"
                kill $backend_pid 2>/dev/null || true
            fi
        else
            log_error "❌ Mock backend script not found"
        fi
    fi
}

test_frontend() {
    log_info "🏗️ Testing frontend build..."
    
    # Test TypeScript compilation
    if command -v npx &> /dev/null; then
        log_info "Checking TypeScript..."
        if npx tsc --noEmit > /dev/null 2>&1; then
            log_success "✅ TypeScript compilation successful"
        else
            log_warning "⚠️ TypeScript compilation issues"
            echo "Run 'npx tsc --noEmit' for details"
        fi
    fi
    
    # Test build
    log_info "Testing build process..."
    if npm run build > /dev/null 2>&1; then
        log_success "✅ Frontend builds successfully"
    else
        log_error "❌ Frontend build failed"
        echo "Run 'npm run build' for details"
    fi
}

show_logs() {
    log_info "📋 Recent logs and status..."
    
    # Show npm logs if they exist
    if [ -f "npm-debug.log" ]; then
        echo "=== NPM Debug Log ==="
        tail -20 npm-debug.log
        echo
    fi
    
    # Show process status
    echo "=== Process Status ==="
    if pgrep -f "vite" > /dev/null; then
        log_info "Vite dev server is running"
    else
        log_info "Vite dev server is not running"
    fi
    
    if pgrep -f "mock-backend" > /dev/null; then
        log_info "Mock backend is running"
    else
        log_info "Mock backend is not running"
    fi
    
    # Show port usage
    echo
    echo "=== Port Usage ==="
    if command -v lsof &> /dev/null; then
        echo "Port 5173 (Frontend): $(lsof -ti:5173 | wc -l | tr -d ' ') processes"
        echo "Port 8000 (Backend): $(lsof -ti:8000 | wc -l | tr -d ' ') processes"
    else
        echo "lsof not available, cannot check port usage"
    fi
}

health_check() {
    log_info "🏥 Running full health check..."
    echo
    
    local issues=0
    
    # Check environment
    if ! check_environment; then
        ((issues++))
    fi
    echo
    
    # Check dependencies
    if ! check_dependencies; then
        ((issues++))
    fi
    echo
    
    # Check API connections
    test_api_connections
    echo
    
    # Check backend
    test_backend
    echo
    
    # Check frontend
    test_frontend
    echo
    
    # Summary
    if [ $issues -eq 0 ]; then
        log_success "🎉 All health checks passed!"
        echo
        log_info "🚀 Ready to start development:"
        echo "   npm run dev          - Start frontend only"
        echo "   npm run start:full   - Start frontend + mock backend"
        echo "   npm run dev:debug    - Start with debug mode"
    else
        log_warning "⚠️ Found $issues issue(s). Run './debug-helper.sh fix' to attempt fixes."
    fi
}

fix_common_issues() {
    log_info "🔧 Attempting to fix common issues..."
    
    # Fix 1: Reinstall dependencies
    log_info "Reinstalling dependencies..."
    rm -rf node_modules package-lock.json
    npm install
    
    # Fix 2: Create missing environment file
    if [ ! -f ".env.local" ]; then
        log_info "Creating .env.local from template..."
        if [ -f ".env.development" ]; then
            cp .env.development .env.local
        else
            ./dev-setup.sh
        fi
    fi
    
    # Fix 3: Create missing directories
    mkdir -p src/lib src/components scripts
    
    # Fix 4: Kill any hanging processes
    log_info "Cleaning up processes..."
    pkill -f "vite" 2>/dev/null || true
    pkill -f "mock-backend" 2>/dev/null || true
    
    # Fix 5: Clear npm cache
    log_info "Clearing npm cache..."
    npm cache clean --force
    
    log_success "✅ Common fixes applied"
    echo
    log_info "Run './debug-helper.sh health' to verify fixes"
}

clean_install() {
    log_info "🧹 Performing clean installation..."
    
    # Remove all generated files
    rm -rf node_modules
    rm -rf dist
    rm -rf .vite
    rm -f package-lock.json
    rm -f npm-debug.log
    
    # Clean npm cache
    npm cache clean --force
    
    # Fresh install
    npm install
    
    log_success "✅ Clean installation completed"
}

# Main script logic
case "${1:-help}" in
    "env")
        check_environment
        ;;
    "deps")
        check_dependencies
        ;;
    "api")
        test_api_connections
        ;;
    "backend")
        test_backend
        ;;
    "frontend")
        test_frontend
        ;;
    "logs")
        show_logs
        ;;
    "health")
        health_check
        ;;
    "fix")
        fix_common_issues
        ;;
    "clean")
        clean_install
        ;;
    "help"|*)
        show_usage
        ;;
esac
