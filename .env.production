# CipherScope Production Environment Variables
# Copy this file and customize for your production deployment

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Application URLs - UPDATE THESE WITH YOUR ACTUAL DOMAINS
NEXT_PUBLIC_APP_URL=https://cipherscope.com
NEXT_PUBLIC_API_URL=https://api.cipherscope.com
NEXT_PUBLIC_WS_URL=wss://api.cipherscope.com/ws

# Environment
NODE_ENV=production
NEXT_PUBLIC_ENVIRONMENT=production

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================

# NextAuth.js configuration - GENERATE SECURE SECRETS
NEXTAUTH_URL=https://cipherscope.com
NEXTAUTH_SECRET=CHANGE_ME_TO_SECURE_RANDOM_STRING_32_CHARS_MIN

# JWT Secret - GENERATE SECURE SECRET
JWT_SECRET=CHANGE_ME_TO_SECURE_RANDOM_STRING_32_CHARS_MIN

# API Keys - ADD YOUR ACTUAL API KEYS
NEXT_PUBLIC_API_KEY=your-production-api-key-here

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/disable features for production
NEXT_PUBLIC_ENABLE_REAL_TIME=true
NEXT_PUBLIC_ENABLE_ADVANCED_CHARTS=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_DEBUG=false

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================

# Analytics - ADD YOUR TRACKING IDs
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
NEXT_PUBLIC_MIXPANEL_TOKEN=your-mixpanel-token
ANALYTICS_KEY=your-analytics-key

# Error Tracking - ADD YOUR SENTRY DSN
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_AUTH_TOKEN=your-sentry-auth-token

# Monitoring - ADD YOUR DATADOG TOKENS
NEXT_PUBLIC_DATADOG_CLIENT_TOKEN=your-datadog-client-token
NEXT_PUBLIC_DATADOG_APPLICATION_ID=your-datadog-app-id

# =============================================================================
# TRADING & MARKET DATA
# =============================================================================

# Market data providers - ADD YOUR API KEYS
NEXT_PUBLIC_COINGECKO_API_KEY=your-coingecko-api-key
NEXT_PUBLIC_COINMARKETCAP_API_KEY=your-coinmarketcap-api-key
NEXT_PUBLIC_BINANCE_API_KEY=your-binance-api-key
NEXT_PUBLIC_POLYGON_API_KEY=your-polygon-api-key

# WebSocket endpoints - UPDATE WITH YOUR ENDPOINTS
NEXT_PUBLIC_BINANCE_WS_URL=wss://stream.binance.com:9443/ws
NEXT_PUBLIC_COINBASE_WS_URL=wss://ws-feed.pro.coinbase.com

# =============================================================================
# BLOCKCHAIN CONFIGURATION
# =============================================================================

# Ethereum - ADD YOUR INFURA PROJECT ID
NEXT_PUBLIC_ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/YOUR_INFURA_PROJECT_ID
NEXT_PUBLIC_ETHEREUM_CHAIN_ID=1

# Polygon - ADD YOUR INFURA PROJECT ID
NEXT_PUBLIC_POLYGON_RPC_URL=https://polygon-mainnet.infura.io/v3/YOUR_INFURA_PROJECT_ID
NEXT_PUBLIC_POLYGON_CHAIN_ID=137

# BSC
NEXT_PUBLIC_BSC_RPC_URL=https://bsc-dataseed.binance.org/
NEXT_PUBLIC_BSC_CHAIN_ID=56

# =============================================================================
# STORAGE & CDN
# =============================================================================

# AWS S3 - ADD YOUR AWS CREDENTIALS
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_REGION=us-west-2
AWS_S3_BUCKET=cipherscope-assets-production

# CloudFront - UPDATE WITH YOUR DISTRIBUTION
NEXT_PUBLIC_CDN_URL=https://d1234567890.cloudfront.net

# =============================================================================
# DATABASE & CACHE
# =============================================================================

# Database - UPDATE WITH YOUR RDS ENDPOINT
DATABASE_URL=postgresql://cipherscope:<EMAIL>:5432/cipherscope

# Redis - UPDATE WITH YOUR ELASTICACHE ENDPOINT
REDIS_URL=redis://your-elasticache-endpoint.cache.amazonaws.com:6379/0

# =============================================================================
# NOTIFICATIONS
# =============================================================================

# Email - ADD YOUR SMTP CONFIGURATION
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Push notifications - GENERATE VAPID KEYS
NEXT_PUBLIC_VAPID_PUBLIC_KEY=your-vapid-public-key
VAPID_PRIVATE_KEY=your-vapid-private-key

# Slack - ADD YOUR WEBHOOK URLs
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
SLACK_BOT_TOKEN=xoxb-your-slack-bot-token

# Discord - ADD YOUR WEBHOOK URL
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/YOUR/DISCORD/WEBHOOK

# =============================================================================
# PRODUCTION CONFIGURATION
# =============================================================================

# SSL/TLS - UPDATE PATHS IF USING CUSTOM CERTIFICATES
SSL_CERT_PATH=/etc/ssl/certs/cert.pem
SSL_KEY_PATH=/etc/ssl/private/key.pem

# Rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# CORS - UPDATE WITH YOUR DOMAINS
CORS_ORIGINS=https://cipherscope.com,https://www.cipherscope.com,https://app.cipherscope.com

# =============================================================================
# MONITORING & LOGGING
# =============================================================================

# Log level
LOG_LEVEL=info

# Metrics
METRICS_ENABLED=true
METRICS_PORT=9090

# Health checks
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30000

# =============================================================================
# PERFORMANCE
# =============================================================================

# Caching
CACHE_TTL=3600
STATIC_CACHE_TTL=86400

# Compression
COMPRESSION_ENABLED=true
COMPRESSION_LEVEL=6

# =============================================================================
# SECURITY
# =============================================================================

# Content Security Policy
CSP_ENABLED=true
CSP_REPORT_URI=https://your-csp-report-endpoint.com/report

# HSTS
HSTS_MAX_AGE=31536000
HSTS_INCLUDE_SUBDOMAINS=true

# Session - GENERATE SECURE SECRET
SESSION_SECRET=CHANGE_ME_TO_SECURE_RANDOM_STRING_32_CHARS_MIN
SESSION_MAX_AGE=86400000

# =============================================================================
# THIRD-PARTY INTEGRATIONS
# =============================================================================

# Social media - ADD YOUR API KEYS
TWITTER_API_KEY=your-twitter-api-key
TWITTER_API_SECRET=your-twitter-api-secret
DISCORD_CLIENT_ID=your-discord-client-id
TELEGRAM_BOT_TOKEN=your-telegram-bot-token

# Payment processing - ADD YOUR STRIPE KEYS
STRIPE_PUBLIC_KEY=pk_live_your_stripe_public_key
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key

# AI/ML Services - ADD YOUR API KEYS
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key

# =============================================================================
# DEPLOYMENT
# =============================================================================

# Container configuration
CONTAINER_PORT=3000
CONTAINER_HOST=0.0.0.0

# Kubernetes
K8S_NAMESPACE=cipherscope
K8S_SERVICE_ACCOUNT=cipherscope-frontend

# Docker
DOCKER_REGISTRY=ghcr.io
DOCKER_IMAGE_TAG=latest

# =============================================================================
# BACKUP & RECOVERY
# =============================================================================

# Backup configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=90
