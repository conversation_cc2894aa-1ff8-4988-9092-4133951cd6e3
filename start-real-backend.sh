#!/bin/bash

# CipherScope Real Backend Connection Script
set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }

log_info "🚀 Starting CipherScope with Real Backend"

# Check if Python virtual environment exists
if [ ! -d ".venv" ]; then
    log_error "Python virtual environment not found. Creating one..."
    python3 -m venv .venv
fi

# Activate virtual environment
log_info "Activating Python virtual environment..."
source .venv/bin/activate

# Check if requirements are installed
log_info "Checking Python dependencies..."
if [ -f "pyproject.toml" ]; then
    log_info "Installing Python dependencies from pyproject.toml..."
    pip install -e .
elif [ -f "requirements.txt" ]; then
    log_info "Installing Python dependencies from requirements.txt..."
    pip install -r requirements.txt
else
    log_info "Installing basic FastAPI dependencies..."
    pip install fastapi uvicorn python-dotenv
fi

# Update frontend environment to use real backend
log_info "📝 Updating frontend environment for real backend..."

# Create/update .env.local for frontend
cat > .env.local << 'ENV_END'
# CipherScope Development Environment - Real Backend
NODE_ENV=development
VITE_ENVIRONMENT=development
VITE_APP_NAME=CipherScope
VITE_APP_VERSION=1.0.0

# Application URLs (Real Backend)
VITE_APP_URL=http://localhost:5173
VITE_API_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000/ws

# Copy your API keys from .env file
VITE_COINGECKO_API_KEY=CG-wJx8SHeFD1KjG3F73LB14967
VITE_INFURA_API_KEY=********************************
VITE_WEB3_PROVIDER_URL=https://eth-mainnet.g.alchemy.com/v2/BwYx2Nu9Fo8TBdqPmMw18bIz_MWdsQP-
VITE_ETHERSCAN_API_KEY=**********************************
VITE_DUNE_API_KEY=8L1zlFUtMWg4FCMsZuFkKIkXbRpPYefv
VITE_BIRDEYE_API_KEY=34eb196d0dad4234ace628c3e8767360

# WebSocket URLs
VITE_INFURA_MAINNET_WS=wss://mainnet.infura.io/ws/v3/********************************

# Feature Flags
VITE_ENABLE_REAL_TIME=true
VITE_ENABLE_ADVANCED_CHARTS=true
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_TRADING=true
VITE_ENABLE_SECURITY_MONITORING=true

# Debug
VITE_DEBUG=true
VITE_LOG_LEVEL=DEBUG
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_DEBUG_PANEL=true

# Backend Type
VITE_BACKEND_TYPE=real
ENV_END

log_success "Frontend environment updated for real backend"

# Function to start backend
start_backend() {
    log_info "🖥️ Starting Python FastAPI backend..."
    
    # Check if backend can start
    if python run.py api --help > /dev/null 2>&1; then
        log_info "Starting backend on http://localhost:8000"
        python run.py api --host 0.0.0.0 --port 8000 &
        BACKEND_PID=$!
        echo $BACKEND_PID > .backend.pid
        
        # Wait for backend to start
        log_info "Waiting for backend to start..."
        sleep 5
        
        # Test backend health
        if curl -s http://localhost:8000/health > /dev/null; then
            log_success "✅ Backend started successfully"
            return 0
        else
            log_error "❌ Backend failed to start properly"
            return 1
        fi
    else
        log_error "❌ Cannot start backend. Check if run.py exists and is executable."
        return 1
    fi
}

# Function to start frontend
start_frontend() {
    log_info "🌐 Starting React frontend..."
    
    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        log_info "Installing frontend dependencies..."
        npm install
    fi
    
    # Start frontend
    log_info "Starting frontend on http://localhost:5173"
    npm run dev &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > .frontend.pid
    
    # Wait for frontend to start
    sleep 3
    log_success "✅ Frontend started successfully"
}

# Function to test integration
test_integration() {
    log_info "🧪 Testing frontend-backend integration..."
    
    # Wait a bit more for services to fully start
    sleep 5
    
    # Test backend health endpoint
    log_info "Testing backend health endpoint..."
    if curl -s http://localhost:8000/health | grep -q "healthy"; then
        log_success "✅ Backend health check passed"
    else
        log_warning "⚠️ Backend health check failed or returned unexpected response"
    fi
    
    # Test backend metrics endpoint
    log_info "Testing backend metrics endpoint..."
    if curl -s http://localhost:8000/metrics > /dev/null; then
        log_success "✅ Backend metrics endpoint accessible"
    else
        log_warning "⚠️ Backend metrics endpoint not accessible"
    fi
    
    # Test CORS (frontend can access backend)
    log_info "Testing CORS configuration..."
    if curl -s -H "Origin: http://localhost:5173" http://localhost:8000/health > /dev/null; then
        log_success "✅ CORS configuration working"
    else
        log_warning "⚠️ CORS configuration may have issues"
    fi
    
    # Test if frontend is accessible
    log_info "Testing frontend accessibility..."
    if curl -s http://localhost:5173 > /dev/null; then
        log_success "✅ Frontend is accessible"
    else
        log_warning "⚠️ Frontend not accessible yet (may still be starting)"
    fi
}

# Function to show status
show_status() {
    echo
    log_success "🎉 CipherScope is running with real backend!"
    echo
    log_info "📊 Service URLs:"
    echo "   Frontend:        http://localhost:5173"
    echo "   Backend API:     http://localhost:8000"
    echo "   API Docs:        http://localhost:8000/docs"
    echo "   Health Check:    http://localhost:8000/health"
    echo "   Metrics:         http://localhost:8000/metrics"
    echo
    log_info "🔧 Available Backend Endpoints:"
    echo "   /health                          - Health check"
    echo "   /metrics                         - System metrics"
    echo "   /api-inventory/report           - API inventory"
    echo "   /security/bopla/report          - Security report"
    echo "   /security/threat-intelligence   - Threat intelligence"
    echo "   /ml/weight-optimization         - ML optimization"
    echo "   /monitoring/accuracy            - Accuracy monitoring"
    echo "   /compliance/automation          - Compliance automation"
    echo
    log_info "🐛 Debug Commands:"
    echo "   curl http://localhost:8000/health"
    echo "   curl http://localhost:8000/metrics"
    echo "   curl http://localhost:8000/api-inventory/report"
    echo
    log_info "🛑 To stop services:"
    echo "   ./stop-services.sh"
    echo "   Or press Ctrl+C and run: pkill -f 'python run.py' && pkill -f 'npm run dev'"
}

# Function to cleanup on exit
cleanup() {
    log_info "🛑 Stopping services..."
    
    if [ -f ".backend.pid" ]; then
        BACKEND_PID=$(cat .backend.pid)
        kill $BACKEND_PID 2>/dev/null || true
        rm .backend.pid
    fi
    
    if [ -f ".frontend.pid" ]; then
        FRONTEND_PID=$(cat .frontend.pid)
        kill $FRONTEND_PID 2>/dev/null || true
        rm .frontend.pid
    fi
    
    # Kill any remaining processes
    pkill -f "python run.py" 2>/dev/null || true
    pkill -f "npm run dev" 2>/dev/null || true
    
    log_info "Services stopped"
}

# Set up signal handlers
trap cleanup EXIT INT TERM

# Main execution
main() {
    # Start backend
    if start_backend; then
        log_success "Backend started successfully"
    else
        log_error "Failed to start backend"
        exit 1
    fi
    
    # Start frontend
    start_frontend
    
    # Test integration
    test_integration
    
    # Show status
    show_status
    
    # Keep script running
    log_info "🔄 Services are running. Press Ctrl+C to stop."
    
    # Monitor services
    while true; do
        sleep 30
        
        # Check if backend is still running
        if ! curl -s http://localhost:8000/health > /dev/null; then
            log_warning "⚠️ Backend appears to be down"
        fi
        
        # Check if frontend is still running
        if ! curl -s http://localhost:5173 > /dev/null; then
            log_warning "⚠️ Frontend appears to be down"
        fi
    done
}

# Run main function
main
