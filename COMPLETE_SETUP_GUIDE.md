# 🚀 CipherScope Complete Development & Production Setup Guide

This guide provides a complete setup for CipherScope development and production environments with active debugging and troubleshooting capabilities.

## 📋 Overview

You now have a complete CipherScope environment with:
- ✅ Migrated environment variables from your existing `.env` file
- ✅ Local development environment with hot reload
- ✅ Mock backend for testing
- ✅ Real-time WebSocket integration
- ✅ Comprehensive debugging tools
- ✅ Production readiness validation
- ✅ All your existing API keys properly configured

## 🛠️ Available Scripts

### 1. **Environment Setup**
```bash
./dev-setup.sh                    # Set up development environment
./setup-env.sh domain.com email   # Set up production environment
```

### 2. **Development & Testing**
```bash
./test-integration.sh             # Run integration tests
./debug-helper.sh health          # Full health check
./debug-helper.sh api             # Test API connections
./debug-helper.sh fix             # Fix common issues
```

### 3. **Production Readiness**
```bash
./production-check.sh             # Check production readiness
```

### 4. **Development Commands**
```bash
npm run dev                       # Start frontend only
npm run start:full               # Start frontend + mock backend
npm run dev:debug                # Start with debug mode
npm run build                    # Build for production
```

## 🚀 Quick Start Guide

### Step 1: Set Up Development Environment
```bash
# Run the development setup
./dev-setup.sh

# This will:
# - Install crypto/Web3 dependencies (ethers, web3, lightweight-charts)
# - Create .env.development with your migrated API keys
# - Set up mock backend server
# - Configure TypeScript and Vite
# - Create debugging utilities
```

### Step 2: Test Everything
```bash
# Run comprehensive integration tests
./test-integration.sh

# Run health check
./debug-helper.sh health

# This will verify:
# - Environment variables are properly configured
# - All dependencies are installed
# - API connections work (CoinGecko, Infura, etc.)
# - Mock backend functions correctly
# - Frontend builds successfully
```

### Step 3: Start Development
```bash
# Start with mock backend (recommended)
npm run start:full

# Or start frontend only
npm run dev

# Or start with debug mode
npm run dev:debug
```

### Step 4: Access Your Application
- **Frontend**: http://localhost:5173
- **Mock Backend**: http://localhost:8000
- **Health Check**: http://localhost:8000/api/health
- **Token Data**: http://localhost:8000/api/tokens

## 🔧 Your Migrated Configuration

### API Keys Successfully Migrated:
- ✅ **CoinGecko API**: `CG-wJx8SHeFD1KjG3F73LB14967`
- ✅ **Infura API**: `********************************`
- ✅ **Alchemy Web3**: `BwYx2Nu9Fo8TBdqPmMw18bIz_MWdsQP-`
- ✅ **Etherscan API**: `**********************************`
- ✅ **Dune Analytics**: `8L1zlFUtMWg4FCMsZuFkKIkXbRpPYefv`
- ✅ **CoinAPI**: `1dc67551-276c-4098-a1f2-2b58ee57ce69`
- ✅ **Birdeye API**: `34eb196d0dad4234ace628c3e8767360`
- ✅ **Reddit API**: Client ID and Secret configured
- ✅ **OpenRouter AI**: `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`
- ✅ **ScrapingBee**: `********************************************************************************`
- ✅ **HyperBrowser**: `hb_adc1508444618104ba9ed438f19b`

### WebSocket Endpoints Configured:
- ✅ **Ethereum Mainnet**: `wss://mainnet.infura.io/ws/v3/********************************`
- ✅ **Polygon**: `wss://polygon-mainnet.infura.io/ws/v3/********************************`
- ✅ **Base**: `wss://base-mainnet.infura.io/ws/v3/********************************`
- ✅ **Arbitrum**: `wss://arbitrum-mainnet.infura.io/ws/v3/********************************`
- ✅ **Optimism**: `wss://optimism-mainnet.infura.io/ws/v3/********************************`

## �� Debugging & Troubleshooting

### Debug Helper Commands:
```bash
./debug-helper.sh env             # Check environment variables
./debug-helper.sh deps            # Check dependencies
./debug-helper.sh api             # Test API connections
./debug-helper.sh backend         # Test backend connectivity
./debug-helper.sh frontend        # Test frontend build
./debug-helper.sh logs            # Show recent logs
./debug-helper.sh health          # Full health check
./debug-helper.sh fix             # Attempt to fix common issues
./debug-helper.sh clean           # Clean and reinstall
```

### Common Issues & Solutions:

#### 1. **Environment Variables Not Loading**
```bash
# Check if .env.local exists and has correct format
./debug-helper.sh env

# Fix: Recreate environment file
./dev-setup.sh
```

#### 2. **API Connection Issues**
```bash
# Test API connectivity
./debug-helper.sh api

# Check specific API:
curl "https://api.coingecko.com/api/v3/ping"
```

#### 3. **Dependencies Issues**
```bash
# Clean install
./debug-helper.sh clean

# Or manual fix:
rm -rf node_modules package-lock.json
npm install
```

#### 4. **Build Failures**
```bash
# Check TypeScript errors
npx tsc --noEmit

# Check build process
npm run build
```

#### 5. **Port Already in Use**
```bash
# Kill processes on ports 5173 and 8000
pkill -f "vite"
pkill -f "mock-backend"

# Or use different ports in .env.local
```

## 🏗️ Development Features

### Real-time Data Integration:
- **WebSocket connections** to multiple blockchain networks
- **Live price feeds** from CoinGecko and other APIs
- **Real-time chart updates** using Lightweight Charts
- **Mock backend** for testing without external dependencies

### Security Monitoring:
- **API key validation** and secure storage
- **Environment variable validation**
- **Debug mode controls** for development vs production

### Performance Monitoring:
- **Build optimization** with code splitting
- **Bundle analysis** and size monitoring
- **Hot reload** for fast development

## 🚀 Production Deployment

### Step 1: Prepare Production Environment
```bash
# Check production readiness
./production-check.sh

# This will verify:
# - Security configuration
# - Build process
# - Dependencies security
# - Performance optimizations
# - Configuration completeness
```

### Step 2: Configure Production Variables
```bash
# Update .env.production.migrated with your domain
# Replace placeholder values:
# - VITE_APP_URL=https://your-domain.com
# - VITE_API_URL=https://api.your-domain.com
# - JWT_SECRET_KEY=your-secure-secret
# - ENCRYPTION_KEY=your-secure-key
```

### Step 3: Build and Deploy
```bash
# Build for production
npm run build

# Preview production build locally
npm run preview

# Deploy dist/ folder to your hosting platform
```

## 📊 Monitoring & Analytics

### Built-in Monitoring:
- **Health check endpoints** at `/api/health`
- **Performance metrics** tracking
- **Error logging** and debugging
- **API usage monitoring**

### External Integrations:
- **Sentry** for error tracking (configure VITE_SENTRY_DSN)
- **Google Analytics** for usage analytics
- **Custom telemetry** with OpenTelemetry

## 🔗 Integration Points

### Frontend ↔ Backend Communication:
- **REST API** endpoints for data fetching
- **WebSocket** connections for real-time updates
- **Authentication** flow with JWT tokens
- **Error handling** and retry logic

### Blockchain Integration:
- **Multi-chain support** (Ethereum, Polygon, BSC, etc.)
- **Web3 provider** configuration with Alchemy/Infura
- **Smart contract** interaction capabilities
- **Transaction monitoring** and analysis

### Data Sources:
- **CoinGecko** for market data
- **Dune Analytics** for blockchain analytics
- **Etherscan** for transaction data
- **Social sentiment** from Reddit API
- **AI analysis** with OpenRouter

## 🎯 Next Steps

### Immediate Actions:
1. **Run the setup**: `./dev-setup.sh`
2. **Test integration**: `./test-integration.sh`
3. **Start development**: `npm run start:full`
4. **Open browser**: http://localhost:5173

### Development Workflow:
1. **Make changes** to your React components
2. **Test with mock data** using the mock backend
3. **Debug issues** using `./debug-helper.sh`
4. **Build and test** production builds regularly

### Production Preparation:
1. **Run production check**: `./production-check.sh`
2. **Configure domain** and SSL certificates
3. **Set up infrastructure** (AWS, Docker, etc.)
4. **Deploy and monitor**

## 🆘 Getting Help

### If You Encounter Issues:
1. **Check the debug helper**: `./debug-helper.sh health`
2. **Review logs**: `./debug-helper.sh logs`
3. **Try common fixes**: `./debug-helper.sh fix`
4. **Clean install**: `./debug-helper.sh clean`

### Debug Information:
- **Environment**: Check `.env.local` for configuration
- **Dependencies**: Verify `node_modules` and `package.json`
- **API Keys**: Test with `./debug-helper.sh api`
- **Build Process**: Check with `npm run build`

---

**🎉 Your CipherScope environment is now ready for development with all your existing API keys and configurations properly migrated!**

Start with: `./dev-setup.sh` then `npm run start:full`
