# CipherScope Frontend Deployment Guide

This guide covers deployment strategies for the CipherScope frontend application across different environments.

## 📋 Prerequisites

### Required Tools
- **Docker** 20.10+ and Docker Compose 2.0+
- **Kubernetes** 1.24+ (for production)
- **kubectl** configured for your cluster
- **Helm** 3.8+ (optional, for advanced deployments)
- **Node.js** 18+ (for local development)

### Required Accounts/Access
- Container registry access (GitHub Container Registry)
- Kubernetes cluster access
- Domain and SSL certificate management
- Monitoring and logging services

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   CloudFront    │    │   Load Balancer │    │   Kubernetes    │
│   (CDN)         │◄──►│   (Nginx)       │◄──►│   Cluster       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Static Assets │    │   SSL/TLS       │    │   Frontend Pods │
│   (S3)          │    │   Termination   │    │   (Next.js)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Deployment Methods

### 1. Docker Compose (Development/Testing)

**Use Case**: Local development, testing, small deployments

```bash
# Clone repository
git clone https://github.com/cipherscope/frontend.git
cd frontend

# Set up environment
cp .env.example .env.local
# Edit .env.local with your configuration

# Start services
docker-compose up -d

# View logs
docker-compose logs -f frontend

# Scale services
docker-compose up -d --scale frontend=3

# Stop services
docker-compose down
```

**Configuration Files**:
- `docker-compose.yml` - Main service definitions
- `docker-compose.override.yml` - Development overrides
- `docker-compose.prod.yml` - Production overrides

### 2. Kubernetes (Production)

**Use Case**: Production deployments, auto-scaling, high availability

#### Quick Deployment

```bash
# Deploy using the deployment script
./scripts/deploy.sh -e production -f

# Or manually apply manifests
kubectl apply -f k8s/
```

#### Step-by-Step Deployment

1. **Create Namespace**
   ```bash
   kubectl create namespace cipherscope
   ```

2. **Apply ConfigMaps and Secrets**
   ```bash
   kubectl apply -f k8s/configmap.yaml
   kubectl apply -f k8s/secrets.yaml
   ```

3. **Deploy Application**
   ```bash
   kubectl apply -f k8s/deployment.yaml
   kubectl apply -f k8s/service.yaml
   kubectl apply -f k8s/ingress.yaml
   ```

4. **Verify Deployment**
   ```bash
   kubectl get pods -n cipherscope
   kubectl get services -n cipherscope
   kubectl get ingress -n cipherscope
   ```

### 3. Terraform (Infrastructure as Code)

**Use Case**: Automated infrastructure provisioning

```bash
# Initialize Terraform
cd terraform
terraform init

# Plan deployment
terraform plan -var-file="environments/production.tfvars"

# Apply infrastructure
terraform apply -var-file="environments/production.tfvars"

# Get outputs
terraform output
```

## 🔧 Environment Configuration

### Development Environment

```bash
# .env.local
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000/ws
NEXT_PUBLIC_ENABLE_DEBUG=true
```

### Staging Environment

```bash
# .env.staging
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://staging.cipherscope.com
NEXT_PUBLIC_API_URL=https://api-staging.cipherscope.com
NEXT_PUBLIC_WS_URL=wss://api-staging.cipherscope.com/ws
NEXT_PUBLIC_ENABLE_ANALYTICS=true
```

### Production Environment

```bash
# .env.production
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://cipherscope.com
NEXT_PUBLIC_API_URL=https://api.cipherscope.com
NEXT_PUBLIC_WS_URL=wss://api.cipherscope.com/ws
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_MONITORING=true
```

## 🔐 Security Configuration

### SSL/TLS Setup

1. **Obtain SSL Certificates**
   ```bash
   # Using cert-manager (recommended for Kubernetes)
   kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.13.0/cert-manager.yaml
   
   # Apply ClusterIssuer
   kubectl apply -f k8s/cert-manager/cluster-issuer.yaml
   ```

2. **Configure Ingress**
   ```yaml
   # k8s/ingress.yaml
   metadata:
     annotations:
       cert-manager.io/cluster-issuer: "letsencrypt-prod"
   spec:
     tls:
       - hosts:
           - cipherscope.com
         secretName: cipherscope-tls
   ```

### Security Headers

Configured in `nginx.conf`:
- HSTS (HTTP Strict Transport Security)
- CSP (Content Security Policy)
- X-Frame-Options
- X-Content-Type-Options
- X-XSS-Protection

### Network Security

```yaml
# k8s/network-policy.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: cipherscope-frontend-netpol
spec:
  podSelector:
    matchLabels:
      app: cipherscope-frontend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
```

## 📊 Monitoring and Observability

### Health Checks

The application provides several health check endpoints:

```bash
# Basic health check
curl https://cipherscope.com/api/health

# Detailed health check
curl https://cipherscope.com/api/health?detailed=true

# Prometheus metrics
curl https://cipherscope.com/api/metrics
```

### Monitoring Stack

1. **Prometheus** - Metrics collection
2. **Grafana** - Visualization and dashboards
3. **AlertManager** - Alert routing and management

```bash
# Deploy monitoring stack
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm install prometheus prometheus-community/kube-prometheus-stack \
  --namespace monitoring \
  --create-namespace \
  --values monitoring/prometheus-values.yaml
```

### Log Aggregation

```bash
# Deploy ELK stack
helm repo add elastic https://helm.elastic.co
helm install elasticsearch elastic/elasticsearch --namespace logging --create-namespace
helm install kibana elastic/kibana --namespace logging
helm install filebeat elastic/filebeat --namespace logging
```

## 🔄 CI/CD Pipeline

### GitHub Actions Workflow

The CI/CD pipeline includes:

1. **Code Quality Checks**
   - ESLint and Prettier
   - TypeScript type checking
   - Unit and integration tests

2. **Security Scanning**
   - Dependency vulnerability scanning
   - Container image scanning
   - SAST (Static Application Security Testing)

3. **Build and Push**
   - Multi-platform Docker image build
   - Push to container registry
   - Image signing and verification

4. **Deployment**
   - Automated deployment to staging
   - Manual approval for production
   - Health checks and rollback

### Manual Deployment

```bash
# Build and push image
docker build -t ghcr.io/cipherscope/frontend:latest .
docker push ghcr.io/cipherscope/frontend:latest

# Deploy to Kubernetes
kubectl set image deployment/cipherscope-frontend \
  frontend=ghcr.io/cipherscope/frontend:latest \
  --namespace=cipherscope

# Wait for rollout
kubectl rollout status deployment/cipherscope-frontend --namespace=cipherscope
```

## 🔧 Scaling and Performance

### Horizontal Pod Autoscaler

```yaml
# k8s/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: cipherscope-frontend-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: cipherscope-frontend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
```

### Vertical Pod Autoscaler

```yaml
# k8s/vpa.yaml
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: cipherscope-frontend-vpa
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: cipherscope-frontend
  updatePolicy:
    updateMode: "Auto"
```

### CDN Configuration

```bash
# AWS CloudFront
aws cloudfront create-distribution \
  --distribution-config file://cloudfront-config.json

# Cloudflare (via API)
curl -X POST "https://api.cloudflare.com/client/v4/zones" \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -H "Content-Type: application/json" \
  --data '{"name":"cipherscope.com","type":"full"}'
```

## 🚨 Troubleshooting

### Common Issues

1. **Pod Startup Issues**
   ```bash
   # Check pod status
   kubectl describe pod <pod-name> -n cipherscope
   
   # Check logs
   kubectl logs <pod-name> -n cipherscope
   
   # Check events
   kubectl get events -n cipherscope --sort-by='.lastTimestamp'
   ```

2. **Service Discovery Issues**
   ```bash
   # Test service connectivity
   kubectl exec -it <pod-name> -n cipherscope -- curl http://backend-service:8000/health
   
   # Check service endpoints
   kubectl get endpoints -n cipherscope
   ```

3. **Ingress Issues**
   ```bash
   # Check ingress status
   kubectl describe ingress cipherscope-frontend-ingress -n cipherscope
   
   # Check ingress controller logs
   kubectl logs -n ingress-nginx deployment/ingress-nginx-controller
   ```

### Performance Issues

1. **High Memory Usage**
   ```bash
   # Check resource usage
   kubectl top pods -n cipherscope
   
   # Adjust resource limits
   kubectl patch deployment cipherscope-frontend -n cipherscope -p '{"spec":{"template":{"spec":{"containers":[{"name":"frontend","resources":{"limits":{"memory":"1Gi"}}}]}}}}'
   ```

2. **Slow Response Times**
   ```bash
   # Check application metrics
   curl https://cipherscope.com/api/metrics | grep response_time
   
   # Scale up replicas
   kubectl scale deployment cipherscope-frontend --replicas=5 -n cipherscope
   ```

## 🔄 Rollback Procedures

### Kubernetes Rollback

```bash
# Check rollout history
kubectl rollout history deployment/cipherscope-frontend -n cipherscope

# Rollback to previous version
kubectl rollout undo deployment/cipherscope-frontend -n cipherscope

# Rollback to specific revision
kubectl rollout undo deployment/cipherscope-frontend --to-revision=2 -n cipherscope
```

### Docker Rollback

```bash
# Tag previous version as latest
docker tag ghcr.io/cipherscope/frontend:v1.0.0 ghcr.io/cipherscope/frontend:latest
docker push ghcr.io/cipherscope/frontend:latest

# Restart containers
docker-compose up -d frontend
```

## 📚 Additional Resources

- [Kubernetes Documentation](https://kubernetes.io/docs/)
- [Docker Documentation](https://docs.docker.com/)
- [Next.js Deployment](https://nextjs.org/docs/deployment)
- [Prometheus Monitoring](https://prometheus.io/docs/)
- [Grafana Dashboards](https://grafana.com/docs/)

## 🆘 Support

For deployment issues:
- Check the [troubleshooting section](#-troubleshooting)
- Review application logs and metrics
- Contact the DevOps team via Slack #devops
- Create an issue in the GitHub repository
