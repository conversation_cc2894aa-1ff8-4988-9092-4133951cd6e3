# CipherScope Frontend

Modern React dashboard for the CipherScope crypto analytics platform, built with Next.js 15, React 19, and TradingView Lightweight Charts.

## 🚀 Features

### ✨ Modern Tech Stack
- **Next.js 15** with React 19 and Server Components
- **TypeScript** for type safety
- **Tailwind CSS** with custom design system
- **shadcn/ui** components for consistent UI
- **TradingView Lightweight Charts** for advanced charting
- **Framer Motion** for smooth animations

### 📊 Real-time Analytics
- **WebSocket connections** for live data streaming
- **Real-time token discovery** and analysis
- **Live price charts** with candlestick and volume data
- **System monitoring** with performance metrics
- **Security alerts** with real-time notifications

### 🎨 User Experience
- **Dark/Light theme** support with system preference detection
- **Responsive design** optimized for all screen sizes
- **Accessibility compliant** (WCAG 2.1 AA)
- **Progressive Web App** capabilities
- **Offline support** with service worker

### 🔒 Security & Performance
- **Content Security Policy** headers
- **Rate limiting** protection
- **Input validation** and sanitization
- **Error boundaries** for graceful error handling
- **Code splitting** and lazy loading
- **Image optimization** with Next.js Image

## 🛠️ Installation

### Prerequisites
- Node.js 18+ 
- npm 8+ or yarn 1.22+
- Backend API running on port 8000

### Quick Start

1. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

2. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   # Edit .env.local with your configuration
   ```

3. **Start development server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

4. **Open in browser**
   ```
   http://localhost:3000
   ```

### Environment Variables

```env
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000/ws

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=CipherScope

# Feature Flags
NEXT_PUBLIC_ENABLE_REAL_TIME=true
NEXT_PUBLIC_ENABLE_ADVANCED_CHARTS=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
```

## 📁 Project Structure

```
src/
├── app/                    # Next.js 15 App Router
│   ├── (dashboard)/        # Dashboard route group
│   ├── globals.css         # Global styles
│   ├── layout.tsx          # Root layout
│   └── page.tsx            # Home page
├── components/             # Reusable components
│   ├── ui/                 # shadcn/ui components
│   ├── charts/             # Chart components
│   ├── dashboard/          # Dashboard components
│   └── theme-provider.tsx  # Theme management
├── hooks/                  # Custom React hooks
│   ├── useWebSocket.ts     # WebSocket management
│   └── useRealTimeData.ts  # Real-time data hooks
├── lib/                    # Utility libraries
│   ├── api.ts              # API client
│   ├── utils.ts            # Utility functions
│   └── types.ts            # TypeScript types
├── services/               # Business logic
└── stores/                 # State management
```

## 🎯 Key Components

### Dashboard Layout
- **Responsive sidebar** with navigation
- **Real-time status indicators**
- **Theme toggle** and user preferences
- **Quick action buttons**

### Token Discovery
- **AI-powered token analysis**
- **Real-time discovery feed**
- **Risk scoring and recommendations**
- **Interactive token cards**

### Trading Charts
- **TradingView Lightweight Charts** integration
- **Real-time price updates** via WebSocket
- **Multiple timeframes** (1h, 1d, 7d, 30d)
- **Volume and technical indicators**
- **Fullscreen mode** support

### System Monitoring
- **Real-time system metrics**
- **Performance monitoring**
- **Resource utilization charts**
- **API response time tracking**

### Security Alerts
- **Real-time security notifications**
- **BOPLA vulnerability detection**
- **AI bot detection alerts**
- **Compliance monitoring**

## 🔌 WebSocket Integration

The frontend uses WebSocket connections for real-time updates:

```typescript
const { lastMessage, isConnected } = useWebSocket({
  url: 'ws://localhost:8000/ws',
  onMessage: (data) => {
    if (data.type === 'token_discovery') {
      // Handle new token discovery
    }
  },
});
```

### Message Types
- `token_discovery` - New token discovered
- `price_update` - Real-time price updates
- `system_metrics` - System performance data
- `security_alert` - Security notifications

## 🎨 Theming

The application supports dark/light themes with:
- **System preference detection**
- **Manual theme switching**
- **Persistent theme storage**
- **CSS custom properties** for colors
- **Tailwind CSS** dark mode classes

## 📱 Responsive Design

Breakpoints:
- `xs`: 475px
- `sm`: 640px
- `md`: 768px
- `lg`: 1024px
- `xl`: 1280px
- `2xl`: 1400px
- `3xl`: 1600px

## 🧪 Testing

```bash
# Run tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## 🚀 Deployment

### Production Build
```bash
npm run build
npm run start
```

### Docker Deployment
```bash
docker build -t cipherscope-frontend .
docker run -p 3000:3000 cipherscope-frontend
```

### Vercel Deployment
```bash
vercel --prod
```

## 🔧 Development

### Code Quality
- **ESLint** for code linting
- **Prettier** for code formatting
- **TypeScript** for type checking
- **Husky** for git hooks

### Performance Optimization
- **Code splitting** with dynamic imports
- **Image optimization** with Next.js Image
- **Bundle analysis** with @next/bundle-analyzer
- **Lighthouse** performance monitoring

## 📚 API Integration

The frontend integrates with the CipherScope backend API:

```typescript
import { apiClient } from '@/lib/api';

// Get token analysis
const analysis = await apiClient.analyzeToken(tokenAddress);

// Get system metrics
const metrics = await apiClient.getSystemMetrics();

// Get security alerts
const alerts = await apiClient.getSystemAlerts();
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Run linting and tests
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
