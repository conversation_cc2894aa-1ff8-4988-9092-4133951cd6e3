mcp[cli]>=1.10.1
pyautogen>=0.2.0
aiohttp[speedups]>=3.12.13
aiofiles>=24.1.0
websockets>=13.1
web3>=7.12.0
eth-account>=0.13.0
eth-utils>=5.1.0
pandas>=2.2.3
numpy>=2.0.0
polars>=1.12.0
pyarrow>=18.1.0
ta>=0.11.0
yfinance>=0.2.48
duckdb>=1.1.3
redis[hiredis]>=5.2.0
sqlalchemy[asyncio]>=2.0.36
structlog>=24.4.0
prometheus-client>=0.21.0
opentelemetry-api>=1.29.0
opentelemetry-sdk>=1.29.0
pydantic>=2.10.4
cryptography>=44.0.0
python-jose[cryptography]>=3.3.0
tenacity>=9.0.0
limits>=3.13.0
backoff>=2.2.1
fastapi>=0.115.6
uvicorn[standard]>=0.32.1
pydantic-settings>=2.6.1
python-dotenv>=1.0.1
aiocache[memcached,redis]>=0.12.3
rich>=13.9.4
typer>=0.15.1

[dev]
pytest>=8.3.4
pytest-asyncio>=0.24.0
pytest-cov>=6.0.0
pytest-mock>=3.14.0
black>=24.10.0
ruff>=0.8.5
mypy>=1.13.0
pre-commit>=4.0.1

[monitoring]
grafana-client>=3.8.1
elasticsearch>=8.17.0

[test]
pytest>=8.3.4
pytest-asyncio>=0.24.0
pytest-cov>=6.0.0
httpx>=0.28.1
respx>=0.21.1
