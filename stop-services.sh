#!/bin/bash

# Stop CipherScope Services Script
set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

log_info "🛑 Stopping CipherScope services..."

# Stop services using PID files
if [ -f ".backend.pid" ]; then
    BACKEND_PID=$(cat .backend.pid)
    if kill $BACKEND_PID 2>/dev/null; then
        log_success "✅ Backend stopped (PID: $BACKEND_PID)"
    else
        log_info "Backend PID not found or already stopped"
    fi
    rm .backend.pid
fi

if [ -f ".frontend.pid" ]; then
    FRONTEND_PID=$(cat .frontend.pid)
    if kill $FRONTEND_PID 2>/dev/null; then
        log_success "✅ Frontend stopped (PID: $FRONTEND_PID)"
    else
        log_info "Frontend PID not found or already stopped"
    fi
    rm .frontend.pid
fi

# Kill any remaining processes
log_info "Cleaning up any remaining processes..."

# Kill Python backend processes
if pkill -f "python run.py" 2>/dev/null; then
    log_success "✅ Stopped Python backend processes"
fi

# Kill Node.js frontend processes
if pkill -f "npm run dev" 2>/dev/null; then
    log_success "✅ Stopped Node.js frontend processes"
fi

# Kill Vite processes
if pkill -f "vite" 2>/dev/null; then
    log_success "✅ Stopped Vite processes"
fi

# Kill any processes on ports 8000 and 5173
if command -v lsof &> /dev/null; then
    # Kill processes on port 8000 (backend)
    if lsof -ti:8000 > /dev/null 2>&1; then
        lsof -ti:8000 | xargs kill -9 2>/dev/null || true
        log_success "✅ Freed port 8000"
    fi
    
    # Kill processes on port 5173 (frontend)
    if lsof -ti:5173 > /dev/null 2>&1; then
        lsof -ti:5173 | xargs kill -9 2>/dev/null || true
        log_success "✅ Freed port 5173"
    fi
fi

log_success "🎉 All CipherScope services stopped"

# Show port status
if command -v lsof &> /dev/null; then
    echo
    log_info "📊 Port status:"
    echo "Port 8000 (Backend): $(lsof -ti:8000 | wc -l | tr -d ' ') processes"
    echo "Port 5173 (Frontend): $(lsof -ti:5173 | wc -l | tr -d ' ') processes"
fi
