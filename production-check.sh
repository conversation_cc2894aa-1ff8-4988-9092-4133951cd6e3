#!/bin/bash

# CipherScope Production Readiness Check
set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }

log_info "🚀 CipherScope Production Readiness Check"

# Initialize counters
PASSED=0
FAILED=0
WARNINGS=0

check_result() {
    if [ $1 -eq 0 ]; then
        log_success "✅ $2"
        ((PASSED++))
    else
        log_error "❌ $2"
        ((FAILED++))
    fi
}

check_warning() {
    log_warning "⚠️ $1"
    ((WARNINGS++))
}

# 1. Environment Configuration Check
log_info "📋 Checking environment configuration..."

if [ -f ".env.production.migrated" ]; then
    check_result 0 "Production environment file exists"
    
    # Check for placeholder values
    if grep -q "CHANGE_ME" .env.production.migrated; then
        check_warning "Production environment contains placeholder values"
    else
        check_result 0 "No placeholder values in production environment"
    fi
    
    # Check for required production variables
    local prod_vars=(
        "VITE_APP_URL"
        "VITE_API_URL"
        "JWT_SECRET_KEY"
        "ENCRYPTION_KEY"
    )
    
    for var in "${prod_vars[@]}"; do
        if grep -q "^$var=" .env.production.migrated; then
            check_result 0 "$var configured"
        else
            check_result 1 "$var missing"
        fi
    done
else
    check_result 1 "Production environment file missing"
fi

# 2. Security Configuration Check
log_info "🔒 Checking security configuration..."

# Check for secure secrets
if [ -f ".env.production.migrated" ]; then
    local jwt_secret=$(grep "JWT_SECRET_KEY" .env.production.migrated | cut -d'=' -f2)
    if [ ${#jwt_secret} -ge 32 ]; then
        check_result 0 "JWT secret is sufficiently long"
    else
        check_result 1 "JWT secret is too short (minimum 32 characters)"
    fi
    
    # Check for HTTPS URLs
    if grep -q "https://" .env.production.migrated; then
        check_result 0 "HTTPS URLs configured"
    else
        check_result 1 "HTTPS URLs not configured"
    fi
    
    # Check debug mode is disabled
    if grep -q "VITE_DEBUG=false" .env.production.migrated; then
        check_result 0 "Debug mode disabled for production"
    else
        check_warning "Debug mode should be disabled for production"
    fi
fi

# 3. Build and Compilation Check
log_info "🏗️ Checking build process..."

# Test production build
if npm run build > /dev/null 2>&1; then
    check_result 0 "Production build successful"
    
    # Check build output
    if [ -d "dist" ]; then
        check_result 0 "Build output directory exists"
        
        # Check for essential files
        if [ -f "dist/index.html" ]; then
            check_result 0 "Main HTML file generated"
        else
            check_result 1 "Main HTML file missing"
        fi
        
        # Check for assets
        if [ -d "dist/assets" ]; then
            check_result 0 "Assets directory generated"
        else
            check_result 1 "Assets directory missing"
        fi
    else
        check_result 1 "Build output directory missing"
    fi
else
    check_result 1 "Production build failed"
fi

# 4. Dependencies Security Check
log_info "📦 Checking dependencies security..."

if command -v npm &> /dev/null; then
    # Check for vulnerabilities
    if npm audit --audit-level=high > /dev/null 2>&1; then
        check_result 0 "No high-severity vulnerabilities found"
    else
        check_warning "High-severity vulnerabilities found - run 'npm audit' for details"
    fi
    
    # Check for outdated packages
    local outdated=$(npm outdated --json 2>/dev/null | wc -l)
    if [ "$outdated" -eq 1 ]; then
        check_result 0 "All packages up to date"
    else
        check_warning "Some packages are outdated - run 'npm outdated' for details"
    fi
fi

# 5. API Keys and External Services Check
log_info "🔑 Checking API keys and external services..."

if [ -f ".env.production.migrated" ]; then
    # Check API keys are not development keys
    local coingecko_key=$(grep "VITE_COINGECKO_API_KEY" .env.production.migrated | cut -d'=' -f2)
    if [ ! -z "$coingecko_key" ] && [ "$coingecko_key" != "your-coingecko-api-key" ]; then
        check_result 0 "CoinGecko API key configured"
    else
        check_result 1 "CoinGecko API key not properly configured"
    fi
    
    local infura_key=$(grep "VITE_INFURA_API_KEY" .env.production.migrated | cut -d'=' -f2)
    if [ ! -z "$infura_key" ] && [ "$infura_key" != "your-infura-api-key" ]; then
        check_result 0 "Infura API key configured"
    else
        check_result 1 "Infura API key not properly configured"
    fi
fi

# 6. Performance and Optimization Check
log_info "⚡ Checking performance optimizations..."

# Check for bundle size
if [ -d "dist/assets" ]; then
    local js_files=$(find dist/assets -name "*.js" | wc -l)
    if [ "$js_files" -gt 0 ]; then
        check_result 0 "JavaScript bundles generated"
        
        # Check for code splitting
        if [ "$js_files" -gt 1 ]; then
            check_result 0 "Code splitting implemented"
        else
            check_warning "Consider implementing code splitting for better performance"
        fi
    else
        check_result 1 "No JavaScript bundles found"
    fi
    
    # Check for CSS optimization
    local css_files=$(find dist/assets -name "*.css" | wc -l)
    if [ "$css_files" -gt 0 ]; then
        check_result 0 "CSS files generated"
    else
        check_warning "No CSS files found - check if styles are properly bundled"
    fi
fi

# 7. Configuration Files Check
log_info "📝 Checking configuration files..."

# Check for essential config files
local config_files=(
    "package.json"
    "tsconfig.json"
    "vite.config.ts"
)

for file in "${config_files[@]}"; do
    if [ -f "$file" ]; then
        check_result 0 "$file exists"
    else
        check_result 1 "$file missing"
    fi
done

# 8. Deployment Readiness Check
log_info "🚀 Checking deployment readiness..."

# Check for deployment scripts
if grep -q "build" package.json; then
    check_result 0 "Build script configured"
else
    check_result 1 "Build script missing"
fi

# Check for proper TypeScript configuration
if [ -f "tsconfig.json" ]; then
    if grep -q "strict" tsconfig.json; then
        check_result 0 "TypeScript strict mode enabled"
    else
        check_warning "Consider enabling TypeScript strict mode"
    fi
fi

# 9. Documentation Check
log_info "📚 Checking documentation..."

local doc_files=(
    "README.md"
    ".env.example"
)

for file in "${doc_files[@]}"; do
    if [ -f "$file" ]; then
        check_result 0 "$file exists"
    else
        check_warning "$file missing - consider adding for better maintainability"
    fi
done

# 10. Final Summary
echo
echo "=================================="
log_info "📊 Production Readiness Summary"
echo "=================================="
echo
echo "✅ Passed: $PASSED"
echo "❌ Failed: $FAILED"
echo "⚠️ Warnings: $WARNINGS"
echo

if [ $FAILED -eq 0 ]; then
    if [ $WARNINGS -eq 0 ]; then
        log_success "🎉 READY FOR PRODUCTION!"
        echo "All checks passed. Your application is ready for production deployment."
    else
        log_warning "🔧 MOSTLY READY - Address warnings for optimal production setup"
        echo "Critical checks passed, but consider addressing the warnings above."
    fi
else
    log_error "🚫 NOT READY FOR PRODUCTION"
    echo "Please fix the failed checks before deploying to production."
fi

echo
log_info "📋 Next Steps:"

if [ $FAILED -eq 0 ]; then
    echo "1. Review and address any warnings above"
    echo "2. Set up your production domain and SSL certificates"
    echo "3. Configure your production infrastructure (AWS, etc.)"
    echo "4. Update .env.production.migrated with your actual domain"
    echo "5. Deploy using your preferred method (Docker, Kubernetes, etc.)"
    echo
    echo "🔗 Useful commands:"
    echo "   npm run build                    - Build for production"
    echo "   npm run preview                  - Preview production build"
    echo "   ./setup-ssl.sh your-domain.com  - Set up SSL certificates"
else
    echo "1. Fix the failed checks listed above"
    echo "2. Run this script again to verify fixes"
    echo "3. Use ./debug-helper.sh for troubleshooting"
    echo
    echo "🔧 Common fixes:"
    echo "   ./debug-helper.sh fix           - Fix common issues"
    echo "   npm audit fix                   - Fix security vulnerabilities"
    echo "   npm update                      - Update dependencies"
fi

# Create production readiness report
cat > production-readiness-report.md << REPORT_END
# CipherScope Production Readiness Report

**Date**: $(date)
**Status**: $([ $FAILED -eq 0 ] && echo "READY" || echo "NOT READY")

## Summary
- ✅ Passed: $PASSED
- ❌ Failed: $FAILED  
- ⚠️ Warnings: $WARNINGS

## Recommendations

### Critical Issues (Must Fix)
$([ $FAILED -gt 0 ] && echo "- Review failed checks above and fix before production deployment" || echo "- No critical issues found")

### Warnings (Should Address)
$([ $WARNINGS -gt 0 ] && echo "- Review warnings above for optimal production setup" || echo "- No warnings found")

### Next Steps
1. Address any failed checks
2. Configure production environment variables
3. Set up SSL certificates and domain
4. Deploy to production infrastructure
5. Monitor and test in production environment

## Production Checklist
- [ ] Environment variables configured
- [ ] Security settings verified
- [ ] Build process tested
- [ ] Dependencies updated and secure
- [ ] API keys configured
- [ ] Performance optimized
- [ ] Documentation updated
- [ ] SSL certificates ready
- [ ] Domain configured
- [ ] Monitoring set up

REPORT_END

log_success "📄 Production readiness report saved to: production-readiness-report.md"
