# CipherScope Kubernetes Deployment Configuration
apiVersion: v1
kind: Namespace
metadata:
  name: cipherscope
  labels:
    name: cipherscope
    environment: production

---
# ConfigMap for application configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: cipherscope-frontend-config
  namespace: cipherscope
data:
  NODE_ENV: "production"
  NEXT_PUBLIC_APP_URL: "https://cipherscope.example.com"
  NEXT_PUBLIC_API_URL: "https://api.cipherscope.example.com"
  NEXT_PUBLIC_WS_URL: "wss://api.cipherscope.example.com/ws"
  NEXT_PUBLIC_ENABLE_REAL_TIME: "true"
  NEXT_PUBLIC_ENABLE_ADVANCED_CHARTS: "true"
  NEXT_PUBLIC_ENABLE_NOTIFICATIONS: "true"

---
# Secret for sensitive configuration
apiVersion: v1
kind: Secret
metadata:
  name: cipherscope-frontend-secrets
  namespace: cipherscope
type: Opaque
data:
  # Base64 encoded values
  NEXTAUTH_SECRET: <base64-encoded-secret>
  ANALYTICS_KEY: <base64-encoded-key>

---
# Deployment for Frontend
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cipherscope-frontend
  namespace: cipherscope
  labels:
    app: cipherscope-frontend
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: cipherscope-frontend
  template:
    metadata:
      labels:
        app: cipherscope-frontend
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3000"
        prometheus.io/path: "/api/metrics"
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
      containers:
      - name: frontend
        image: ghcr.io/cipherscope/frontend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: http
          protocol: TCP
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: cipherscope-frontend-config
              key: NODE_ENV
        - name: NEXT_PUBLIC_APP_URL
          valueFrom:
            configMapKeyRef:
              name: cipherscope-frontend-config
              key: NEXT_PUBLIC_APP_URL
        - name: NEXT_PUBLIC_API_URL
          valueFrom:
            configMapKeyRef:
              name: cipherscope-frontend-config
              key: NEXT_PUBLIC_API_URL
        - name: NEXT_PUBLIC_WS_URL
          valueFrom:
            configMapKeyRef:
              name: cipherscope-frontend-config
              key: NEXT_PUBLIC_WS_URL
        - name: NEXTAUTH_SECRET
          valueFrom:
            secretKeyRef:
              name: cipherscope-frontend-secrets
              key: NEXTAUTH_SECRET
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: nextjs-cache
          mountPath: /app/.next/cache
      volumes:
      - name: tmp
        emptyDir: {}
      - name: nextjs-cache
        emptyDir: {}
      imagePullSecrets:
      - name: ghcr-secret

---
# Service for Frontend
apiVersion: v1
kind: Service
metadata:
  name: cipherscope-frontend-service
  namespace: cipherscope
  labels:
    app: cipherscope-frontend
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: cipherscope-frontend

---
# Horizontal Pod Autoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: cipherscope-frontend-hpa
  namespace: cipherscope
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: cipherscope-frontend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60

---
# Pod Disruption Budget
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: cipherscope-frontend-pdb
  namespace: cipherscope
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: cipherscope-frontend

---
# Ingress for external access
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cipherscope-frontend-ingress
  namespace: cipherscope
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    nginx.ingress.kubernetes.io/configuration-snippet: |
      add_header X-Frame-Options "SAMEORIGIN" always;
      add_header X-Content-Type-Options "nosniff" always;
      add_header X-XSS-Protection "1; mode=block" always;
      add_header Referrer-Policy "strict-origin-when-cross-origin" always;
      add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' ws: wss:;" always;
spec:
  tls:
  - hosts:
    - cipherscope.example.com
    secretName: cipherscope-frontend-tls
  rules:
  - host: cipherscope.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: cipherscope-frontend-service
            port:
              number: 80

---
# Network Policy for security
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: cipherscope-frontend-netpol
  namespace: cipherscope
spec:
  podSelector:
    matchLabels:
      app: cipherscope-frontend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 3000
  - from:
    - podSelector:
        matchLabels:
          app: cipherscope-backend
    ports:
    - protocol: TCP
      port: 3000
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: cipherscope-backend
    ports:
    - protocol: TCP
      port: 8000
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
  - to: []
    ports:
    - protocol: TCP
      port: 443
