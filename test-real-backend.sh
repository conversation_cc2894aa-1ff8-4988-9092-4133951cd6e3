#!/bin/bash

# CipherScope Real Backend Integration Test
set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }

# Test counters
PASSED=0
FAILED=0
WARNINGS=0

test_result() {
    if [ $1 -eq 0 ]; then
        log_success "✅ $2"
        ((PASSED++))
    else
        log_error "❌ $2"
        ((FAILED++))
    fi
}

test_warning() {
    log_warning "⚠️ $1"
    ((WARNINGS++))
}

log_info "🧪 Testing CipherScope Real Backend Integration"

# Test 1: Check if services are running
log_info "📋 Testing service availability..."

# Test backend
if curl -s --max-time 5 http://localhost:8000/health > /dev/null; then
    test_result 0 "Backend is running on port 8000"
else
    test_result 1 "Backend is not running on port 8000"
fi

# Test frontend
if curl -s --max-time 5 http://localhost:5173 > /dev/null; then
    test_result 0 "Frontend is running on port 5173"
else
    test_result 1 "Frontend is not running on port 5173"
fi

# Test 2: Backend Health Check
log_info "🏥 Testing backend health endpoints..."

# Health endpoint
HEALTH_RESPONSE=$(curl -s --max-time 10 http://localhost:8000/health 2>/dev/null || echo "")
if echo "$HEALTH_RESPONSE" | grep -q "healthy"; then
    test_result 0 "Backend health check returns healthy status"
    
    # Check health response structure
    if echo "$HEALTH_RESPONSE" | grep -q "timestamp"; then
        test_result 0 "Health response includes timestamp"
    else
        test_warning "Health response missing timestamp"
    fi
    
    if echo "$HEALTH_RESPONSE" | grep -q "components"; then
        test_result 0 "Health response includes component status"
    else
        test_warning "Health response missing component status"
    fi
else
    test_result 1 "Backend health check failed or returned unhealthy status"
    echo "Response: $HEALTH_RESPONSE"
fi

# Test 3: Backend API Endpoints
log_info "🔌 Testing backend API endpoints..."

# Metrics endpoint
if curl -s --max-time 10 http://localhost:8000/metrics > /dev/null; then
    test_result 0 "Metrics endpoint accessible"
else
    test_result 1 "Metrics endpoint not accessible"
fi

# API Documentation
if curl -s --max-time 10 http://localhost:8000/docs > /dev/null; then
    test_result 0 "API documentation accessible"
else
    test_result 1 "API documentation not accessible"
fi

# API Inventory Report
if curl -s --max-time 10 http://localhost:8000/api-inventory/report > /dev/null; then
    test_result 0 "API inventory report endpoint accessible"
else
    test_warning "API inventory report endpoint not accessible (may be initializing)"
fi

# Security endpoints
if curl -s --max-time 10 http://localhost:8000/security/bopla/report > /dev/null; then
    test_result 0 "Security BOPLA report endpoint accessible"
else
    test_warning "Security BOPLA report endpoint not accessible"
fi

# Test 4: CORS Configuration
log_info "🌐 Testing CORS configuration..."

CORS_RESPONSE=$(curl -s --max-time 10 \
    -H "Origin: http://localhost:5173" \
    -H "Access-Control-Request-Method: GET" \
    -H "Access-Control-Request-Headers: Content-Type" \
    -X OPTIONS \
    http://localhost:8000/health 2>/dev/null || echo "")

if echo "$CORS_RESPONSE" | grep -q "Access-Control-Allow-Origin"; then
    test_result 0 "CORS headers present in response"
else
    test_warning "CORS headers not found (may still work due to wildcard)"
fi

# Test actual CORS request
if curl -s --max-time 10 -H "Origin: http://localhost:5173" http://localhost:8000/health > /dev/null; then
    test_result 0 "CORS request from frontend origin works"
else
    test_result 1 "CORS request from frontend origin failed"
fi

# Test 5: Backend Data Endpoints
log_info "📊 Testing backend data endpoints..."

# Test token analysis endpoints (if available)
if curl -s --max-time 10 http://localhost:8000/tokens > /dev/null 2>&1; then
    test_result 0 "Token data endpoint accessible"
elif curl -s --max-time 10 http://localhost:8000/api/tokens > /dev/null 2>&1; then
    test_result 0 "Token data endpoint accessible at /api/tokens"
else
    test_warning "Token data endpoint not found (may not be implemented yet)"
fi

# Test WebSocket endpoint (basic connectivity)
if command -v wscat &> /dev/null; then
    log_info "Testing WebSocket connectivity..."
    timeout 5 wscat -c ws://localhost:8000/ws --close 2>/dev/null && test_result 0 "WebSocket endpoint accessible" || test_warning "WebSocket endpoint not accessible or wscat not available"
else
    test_warning "wscat not available for WebSocket testing"
fi

# Test 6: Frontend-Backend Integration
log_info "🔗 Testing frontend-backend integration..."

# Check if frontend can load
FRONTEND_RESPONSE=$(curl -s --max-time 10 http://localhost:5173 2>/dev/null || echo "")
if echo "$FRONTEND_RESPONSE" | grep -q "CipherScope\|React\|Vite"; then
    test_result 0 "Frontend loads successfully"
else
    test_warning "Frontend may not be fully loaded yet"
fi

# Test 7: Performance and Response Times
log_info "⚡ Testing performance and response times..."

# Measure backend response time
START_TIME=$(date +%s%N)
curl -s --max-time 10 http://localhost:8000/health > /dev/null
END_TIME=$(date +%s%N)
RESPONSE_TIME=$(( (END_TIME - START_TIME) / 1000000 )) # Convert to milliseconds

if [ $RESPONSE_TIME -lt 1000 ]; then
    test_result 0 "Backend response time under 1 second ($RESPONSE_TIME ms)"
elif [ $RESPONSE_TIME -lt 5000 ]; then
    test_warning "Backend response time acceptable but slow ($RESPONSE_TIME ms)"
else
    test_result 1 "Backend response time too slow ($RESPONSE_TIME ms)"
fi

# Test 8: Error Handling
log_info "🚨 Testing error handling..."

# Test 404 endpoint
NOT_FOUND_RESPONSE=$(curl -s -w "%{http_code}" --max-time 10 http://localhost:8000/nonexistent 2>/dev/null || echo "000")
if echo "$NOT_FOUND_RESPONSE" | grep -q "404"; then
    test_result 0 "Backend returns 404 for non-existent endpoints"
else
    test_warning "Backend error handling may need improvement"
fi

# Test 9: Security Features
log_info "🔒 Testing security features..."

# Check for security headers
SECURITY_HEADERS=$(curl -s -I --max-time 10 http://localhost:8000/health 2>/dev/null || echo "")
if echo "$SECURITY_HEADERS" | grep -qi "x-correlation-id\|x-request-id"; then
    test_result 0 "Request tracking headers present"
else
    test_warning "Request tracking headers not found"
fi

# Test 10: API Documentation and Schema
log_info "📚 Testing API documentation..."

# Check OpenAPI schema
if curl -s --max-time 10 http://localhost:8000/openapi.json > /dev/null; then
    test_result 0 "OpenAPI schema accessible"
else
    test_warning "OpenAPI schema not accessible"
fi

# Generate test report
echo
echo "=================================="
log_info "📊 Real Backend Integration Test Summary"
echo "=================================="
echo
echo "✅ Passed: $PASSED"
echo "❌ Failed: $FAILED"
echo "⚠️ Warnings: $WARNINGS"
echo

if [ $FAILED -eq 0 ]; then
    if [ $WARNINGS -eq 0 ]; then
        log_success "🎉 ALL TESTS PASSED!"
        echo "Your CipherScope real backend integration is working perfectly!"
    else
        log_warning "🔧 MOSTLY WORKING - Address warnings for optimal performance"
        echo "Core functionality is working, but some features may need attention."
    fi
else
    log_error "🚫 SOME TESTS FAILED"
    echo "Please fix the failed tests before proceeding."
fi

echo
log_info "📋 Next Steps:"

if [ $FAILED -eq 0 ]; then
    echo "1. ✅ Backend integration is working"
    echo "2. ✅ Frontend can communicate with backend"
    echo "3. 🌐 Open http://localhost:5173 to use the application"
    echo "4. 📊 Check http://localhost:8000/docs for API documentation"
    echo "5. 🔍 Monitor logs for any runtime issues"
    echo
    echo "🔗 Useful URLs:"
    echo "   Frontend:     http://localhost:5173"
    echo "   Backend API:  http://localhost:8000"
    echo "   API Docs:     http://localhost:8000/docs"
    echo "   Health Check: http://localhost:8000/health"
    echo "   Metrics:      http://localhost:8000/metrics"
else
    echo "1. 🔧 Fix the failed tests listed above"
    echo "2. 🔄 Restart services if needed: ./start-real-backend.sh"
    echo "3. 🧪 Run this test again: ./test-real-backend.sh"
    echo
    echo "🆘 Common fixes:"
    echo "   - Ensure Python dependencies are installed"
    echo "   - Check if ports 8000 and 5173 are available"
    echo "   - Verify .env file has correct API keys"
    echo "   - Check backend logs for errors"
fi

# Create detailed test report
cat > real-backend-test-report.md << REPORT_END
# CipherScope Real Backend Integration Test Report

**Date**: $(date)
**Status**: $([ $FAILED -eq 0 ] && echo "PASSED" || echo "FAILED")

## Summary
- ✅ Passed: $PASSED
- ❌ Failed: $FAILED  
- ⚠️ Warnings: $WARNINGS

## Test Results

### Service Availability
- Backend running on port 8000
- Frontend running on port 5173

### Backend Health
- Health endpoint responding
- Component status available
- Response time: ${RESPONSE_TIME}ms

### API Endpoints
- Core endpoints accessible
- CORS configuration working
- Error handling functional

### Integration Status
- Frontend-backend communication working
- Real-time features available
- Security features enabled

## Recommendations

### If All Tests Passed
1. Application is ready for development and testing
2. All core features are functional
3. Monitor performance and logs during usage

### If Tests Failed
1. Check service startup logs
2. Verify port availability
3. Ensure all dependencies are installed
4. Check environment configuration

## Next Steps
1. Open application: http://localhost:5173
2. Test user workflows
3. Monitor real-time features
4. Check API documentation: http://localhost:8000/docs

REPORT_END

log_success "📄 Detailed test report saved to: real-backend-test-report.md"
