#!/bin/bash

# CipherScope Frontend Installation Script
# This script sets up the modern React dashboard for the crypto analytics platform

set -e

echo "🚀 CipherScope Frontend Installation"
echo "===================================="

# Check Node.js version
echo "📋 Checking prerequisites..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Check if package manager is available
if command -v yarn &> /dev/null; then
    PACKAGE_MANAGER="yarn"
    echo "✅ Using Yarn package manager"
elif command -v npm &> /dev/null; then
    PACKAGE_MANAGER="npm"
    echo "✅ Using npm package manager"
else
    echo "❌ No package manager found. Please install npm or yarn."
    exit 1
fi

# Install dependencies
echo ""
echo "📦 Installing dependencies..."
if [ "$PACKAGE_MANAGER" = "yarn" ]; then
    yarn install
else
    npm install
fi

# Set up environment variables
echo ""
echo "⚙️  Setting up environment configuration..."
if [ ! -f ".env.local" ]; then
    cp .env.local .env.local.backup 2>/dev/null || true
    echo "✅ Environment file created"
else
    echo "⚠️  .env.local already exists, skipping..."
fi

# Check if backend is running
echo ""
echo "🔍 Checking backend connection..."
BACKEND_URL="http://localhost:8000"
if curl -s "$BACKEND_URL/api/health" > /dev/null 2>&1; then
    echo "✅ Backend is running at $BACKEND_URL"
else
    echo "⚠️  Backend not detected at $BACKEND_URL"
    echo "   Make sure to start the Python backend before running the frontend"
fi

# Build the application
echo ""
echo "🔨 Building the application..."
if [ "$PACKAGE_MANAGER" = "yarn" ]; then
    yarn build
else
    npm run build
fi

echo ""
echo "🎉 Installation completed successfully!"
echo ""
echo "📋 Next steps:"
echo "   1. Make sure the backend is running on port 8000"
echo "   2. Start the development server:"
if [ "$PACKAGE_MANAGER" = "yarn" ]; then
    echo "      yarn dev"
else
    echo "      npm run dev"
fi
echo "   3. Open http://localhost:3000 in your browser"
echo ""
echo "🔧 Available commands:"
if [ "$PACKAGE_MANAGER" = "yarn" ]; then
    echo "   yarn dev          - Start development server"
    echo "   yarn build        - Build for production"
    echo "   yarn start        - Start production server"
    echo "   yarn lint         - Run linting"
    echo "   yarn test         - Run tests"
else
    echo "   npm run dev       - Start development server"
    echo "   npm run build     - Build for production"
    echo "   npm run start     - Start production server"
    echo "   npm run lint      - Run linting"
    echo "   npm run test      - Run tests"
fi
echo ""
echo "📚 Documentation: README-FRONTEND.md"
echo "🐛 Issues: https://github.com/cipherscope/issues"
echo ""
echo "Happy coding! 🚀"
