# CipherScope Development Environment - Real Backend
NODE_ENV=development
VITE_ENVIRONMENT=development
VITE_APP_NAME=CipherScope
VITE_APP_VERSION=1.0.0

# Application URLs (Real Backend)
VITE_APP_URL=http://localhost:5173
VITE_API_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000/ws

# Copy your API keys from .env file
VITE_COINGECKO_API_KEY=CG-wJx8SHeFD1KjG3F73LB14967
VITE_INFURA_API_KEY=********************************
VITE_WEB3_PROVIDER_URL=https://eth-mainnet.g.alchemy.com/v2/BwYx2Nu9Fo8TBdqPmMw18bIz_MWdsQP-
VITE_ETHERSCAN_API_KEY=**********************************
VITE_DUNE_API_KEY=8L1zlFUtMWg4FCMsZuFkKIkXbRpPYefv
VITE_BIRDEYE_API_KEY=34eb196d0dad4234ace628c3e8767360

# WebSocket URLs
VITE_INFURA_MAINNET_WS=wss://mainnet.infura.io/ws/v3/********************************

# Feature Flags
VITE_ENABLE_REAL_TIME=true
VITE_ENABLE_ADVANCED_CHARTS=true
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_TRADING=true
VITE_ENABLE_SECURITY_MONITORING=true

# Debug
VITE_DEBUG=true
VITE_LOG_LEVEL=DEBUG
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_DEBUG_PANEL=true

# Backend Type
VITE_BACKEND_TYPE=real
