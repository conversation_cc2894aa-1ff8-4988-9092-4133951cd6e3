# CipherScope Frontend Environment Configuration

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000/ws

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=CipherScope
NEXT_PUBLIC_APP_VERSION=1.0.0

# Feature Flags
NEXT_PUBLIC_ENABLE_REAL_TIME=true
NEXT_PUBLIC_ENABLE_ADVANCED_CHARTS=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
NEXT_PUBLIC_ENABLE_DARK_MODE=true

# Analytics & Monitoring
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ANALYTICS_ID=

# Security
NEXT_PUBLIC_ENABLE_CSP=true
NEXT_PUBLIC_ENABLE_RATE_LIMITING=true

# Development
NODE_ENV=development
NEXT_TELEMETRY_DISABLED=1
