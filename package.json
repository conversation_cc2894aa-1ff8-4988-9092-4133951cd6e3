{"name": "cipherscope-frontend", "version": "1.0.0", "description": "Advanced crypto analytics dashboard with real-time monitoring", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-dialog": "^1.1.0", "@radix-ui/react-dropdown-menu": "^2.1.0", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-select": "^2.1.0", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.0", "@radix-ui/react-tooltip": "^1.1.0", "@tailwindcss/typography": "^0.5.0", "@tanstack/react-query": "^5.59.0", "@tanstack/react-virtual": "^3.10.0", "@types/node": "^22.0.0", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^4.1.0", "framer-motion": "^11.11.0", "lightweight-charts": "^5.0.0", "lucide-react": "^0.460.0", "postcss": "^8.4.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.53.0", "recharts": "^2.13.0", "sonner": "^1.7.0", "tailwind-merge": "^2.5.0", "tailwindcss": "^4.0.0", "typescript": "^5.6.0", "vite": "^7.0.4", "zod": "^3.23.0", "zustand": "^5.0.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.0", "@types/jest": "^29.5.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^9.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.3.0", "prettier-plugin-tailwindcss": "^0.6.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}